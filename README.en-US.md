<h1>vue-pure-admin Lite Edition</h1>

[![license](https://img.shields.io/github/license/xiaoxian521/vue-pure-admin.svg)](LICENSE)

**English** | [中文](./README.md)

## introduce

The lite version is based on the shelf extracted from [vue-pure-admin](https://github.com/xiaoxian521/vue-pure-admin), which contains the main functions and is more suitable for actual project development, the packaged size is only more than `3MB`

## Supporting Video

- [Click Watch Tutorial](https://www.bilibili.com/video/BV1534y1S7HV)
- [Click Watch UI Design](https://www.bilibili.com/video/BV17g411T7rq)

## Docs

- [Click Watch Docs](https://pure-admin-doc.vercel.app)

## Usage

### Installation dependencies

pnpm install

### Install a package

pnpm add packageName

### Uninstall a package

pnpm remove packageName

I think you should fork the project first to develop, so that you can pull the update synchronously when I update! ! !

## Supporting video tutorial

bilibili: https://www.bilibili.com/video/BV1534y1S7HV/

## ⚠️ Note

The lite version does not accept any issues and prs. If you have any questions, please go to the full version https://github.com/xiaoxian521/vue-pure-admin/issues/new/choose to mention it, thank you! ! !

## License

In principle, no fees and copyrights are charged, and you can use it with confidence, but if you need secondary open source, please contact the author for permission!
