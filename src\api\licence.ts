import { http } from "../utils/http";

// 获取授权列表
// export const licenceListApi = (params?: object) => {
//   return http.request("post", "licence/list", { params });
// };
export const licenceListApi = (data) => {
  return http.request("post", "licence/list", { data });
};

// 获取授权列表
export const serverListApi = (params?: object) => {
  return http.request("post", "server/list", { params });
};


// 获取授权列表
export const clientListApi = (params?: object) => {
  return http.request("post", "client/list", { params });
};



//设置授权状态
export const licenceSetStatusApi = (params?: object) => {
  return http.request("get", "licence/set-status", { params });
};

export const licenceDeleteApi = (params?: object) => {
  return http.request("post", "licence/delete", { params });
};


