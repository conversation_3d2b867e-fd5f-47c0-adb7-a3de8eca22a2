<script setup lang="ts">
import dayjs from "dayjs";
import {FormInstance, ElMessageBox} from "element-plus";
import { reactive, ref, onMounted, nextTick } from "vue";
import {EpTableProBar} from "/@/components/ReTable";
import {Switch, message} from "@pureadmin/components";
import {useRenderIcon} from "/@/components/ReIcon/src/hooks";
import Rmb from '@iconify-icons/fa/rmb';
import useClipboard from 'vue-clipboard3'
import {Edit, Money, Checked, Guide, CopyDocument} from '@element-plus/icons-vue'
import pass from '/@/assets/pass.png'
import {
  orderListApi,
  orderPersonalApi,
  orderCreateApi,
  orderDraftApi,
  orderDeleteApi,
  orderMealOptionsApi,
  orderDetailsApi,
  orderDeviceDetailsApi,
  orderReviewApi,
  orderManagerReviewApi,
  orderDeloyApi,
  orderRevokeApi,
  orderUpgradeApi,
  orderDeviceUpdateApi,
  upgradeOrder<PERSON>reate<PERSON><PERSON>,
  orderTransfer<PERSON><PERSON>,
  getDoctorList,
  setAssignDoctor,
  setFeedbackDoctor, setaAminUpdate
} from "/@/api/order"
import {getRegionApi, ukeyListApi} from "/@/api/public";
import {
  hospitalListApi,
  getBindRegionApi,
  agentHadBindApi,
  agentHadBindUserApi,
  hospitalOrderAddApi,
  agentOrderAddApi,
  getUserRegionApi,
  agentAddApi, agentEditApi, agentListApi, agentPersonalApi
} from "/@/api/sys";
import {Auth, AuthCount} from "/@/utils/auth"
import {storageSession} from "/@/utils/storage";
import {dateFormat} from "/@/utils/tool";
import AMapLoader from '@amap/amap-jsapi-loader';
import warning from "postcss/lib/warning";


const formRef = ref<FormInstance>();
const orderDevicePriceRef = ref<FormInstance>();
const formUpgradeRef = ref<FormInstance>();
const ruleFormRef = ref<FormInstance>()
const ruleFormRef_status = ref<FormInstance>()
const ruleFormRef_deploy = ref<FormInstance>()
const ruleFormRef_revoke = ref<FormInstance>()
const ruleFormRef_upgrade = ref<FormInstance>()
const ruleFormRef_use_type = ref<FormInstance>()
const ruleFormOrderRef_use_type = ref<FormInstance>()
const ruleFormRef_create_upgrade = ref<FormInstance>()
const ruleFormRef_create_hospital = ref<FormInstance>()
const ruleFormRef_create_agent = ref<FormInstance>()
const popoverRef = ref<any>(null);
const popoverOrderRef = ref<any>(null);
let switchLoadMap = ref({});
const selectedId = ref(null)
let selectedMeal = ref(null);
const new_meal_options_select = {
  value: 'id', // 对应数据中的值
  label: 'name', // 对应显示的名称
  children: 'm_val' // 对应子级数据
}
const month_options = [
  { value: 0, label: '无' },
  { value: 1, label: '1个月' },
  { value: 2, label: '2个月' },
  { value: 3, label: '3个月' },
  { value: 6, label: '6个月' },
  { value: -1, label: '永久使用' }
];

const Data = reactive({
  dialogVisible: false,
  dialogVisible_Meal: false,
  dialogVisible_OrderDetails: false,
  dialogVisible_OrderUpgrade: false,
  dialogVisible_DeviceDetails: false,
  dialogVisible_UpdateUseType: true,
  dialogVisible_CreateOrderUpgrade: false,
  dialogVisible_SelectUpgradeDevice: false,
  dialogVisible_ErrMsg: false,
  dialogVisible_CreateHospital: false,
  dialogVisible_CreateAgent: false,
  visible_OrderTransfer: false,
  EditDialogModify: false,
  search_from: {
    keyword: "",
    order_number: "",
    contract_number: "",
    serial_number: "",
    region_ids: [],
    hospital_id: "",
    status: "",
    set_meal: "",
    time: null,
    use_type: "",
    device_status: 1,
    sort: '',
    page_size: 10,
    page: 1,
    total: 0
  },
  search_from_upgrade: {
    keyword: "",
    order_number: "",
    contract_number: "",
    serial_number: "",
    region_ids: [],
    hospital_id: "",
    status: "",
    upgrade_status: [1, 9],
    set_meal: "",
    time: null,
    use_type: "",
    order_type: "",
    device_status: 1,
    page_size: 10,
    page: 1,
    total: 0
  },
  upgrade_order_devices: [],
  device_use_type_update: "",
  loading: true,
  data_list: [],
  upgrade_loading: true,
  upgrade_data_list: [],
  region_tree: [],
  user_region_tree: [],
  agent_region_tree: [],
  user_hospital_list: [],
  hospital_list: [],
  search_hospital_list: [],
  all_hospital_list: [],
  product_options: [],
  meal_options: [],
  meal_options_array: [],
  new_meal_options: [],
  new_meal_options_array:[],
  meal_def_options: [],

  status_options: [
    {value: 0, label: '财务待复核'},
    {value: 2, label: '总经理待审批'},
    {value: 3, label: '财务复核不通过'},
    {value: 5, label: '总经理审批通过'},
    {value: 6, label: '总经理审批不通过'},
    {value: 7, label: '销售撤回'},
    {value: 8, label: '总经理撤回'},
    {value: 9, label: '实施部署中'},
    {value: 1, label: '实施部署已完成'}
  ],
  use_type_options: [
    {value: 1, label: '临床试验用'},
    {value: 2, label: '厂家样机'},
    {value: 3, label: '代理商样机'},
    {value: 4, label: '售卖产品'}
  ],
  maintenance_options: [
    {value: 1, label: '1个月'},
    {value: 2, label: '2个月'},
    {value: 3, label: '3个月'}
  ],
  probation_options: [
    {value: 30, label: '30天'},
    {value: 60, label: '60天'},
    {value: 90, label: '90天'}
  ],
  device_status_options: [
    {value: -1, label: '全部'},
    {value: 0, label: '无效'},
    {value: 1, label: '正常'},
  ],
  contract_company_options: {
    '1001': '广州爱孕记信息科技有限公司',
    '': '长沙爱孕记医疗科技有限公司'
  },
  order_type_options: [
    {value: -1, label: '全部'},
    {value: 0, label: '销售订单'},
    {value: 1, label: '升级订单'},
  ],
  data_from: {
    id: 0,
    contract_number: "",
    region_id: [],
    hospital_id: "",
    hospital_region_id: "",
    agent_region_id: "",
    agent_id: "",
    ukey_code: "",
    sale_name: "",
    sale_phone: "",
    principal: "",
    principal_phone: "",
    contact: "",
    contact_phone: "",
    receiver: "",
    receiver_phone: "",
    receiver_region: "",
    receiver_region_arr: [],
    receiver_address: "",
    count: 1,
    maintenance: 3,
    probation: 30,
    remark: "",
    is_urgent: 0,
    devices: [],
    device_index: '0',
    region_ids: [],
    price: [],
    price_ref: [],
    switch_value: false, // 默认关闭
  },
  curr_device_index: 0,
  curr_set_meal: 1,
  curr_device_upgrade: false,
  device: {
    // set_meal: "2",
    set_meal: 1000,
    use_type: 4,
    month: 6,
    products: [],
    is_append: 0,
    maintenance: 3,
    probation: 30,
    remark: "",
    hospital_list: [],
    ChEn:0,
  },
  order_info: {},
  device_info: {},
  original_devices: {}, //记录原始的设备详情，用于订单升级套餐值比对
  ukey_list: [],
  status_from: {
    order_number: "",
    status: 0,
    remark: "",
  },
  status_from_finance: {
    order_number: "",
    contract_number: "",
    last_date: "",
    status: 2,
    remark: "",
    devices: [],
  },
  deploy_from: {
    order_number: "",
    ukey_code: null,
    express_type_: true,
    express_type: 0,
    express_no: "",
    remark: ""
  },
  revoke_from: {
    order_number: "",
    remark: "",
  },
  status_from_rules: {
    status: [
      {required: true, message: '审核结果必填', trigger: 'blur'},
    ],
    remark: [
      {max: 500, message: '备注限定500字符长度', trigger: ['blur', 'change']},
    ],
  },
  status_from_finance_rules: {
    status: [
      {required: true, message: '审核结果必填', trigger: 'blur'},
    ],
    contract_number: [
      {required: true, message: '合同号必填', trigger: 'blur'},
    ],
    last_date: [
      {required: true, message: '最后发货时间必填', trigger: 'blur'},
    ],
    remark: [
      {max: 500, message: '备注限定500字符长度', trigger: ['blur', 'change']},
    ],
  },
  deploy_from_rules: {
    ukey_code: [
      {required: true, message: 'U-Key信息必填', trigger: 'blur'},
    ],
    remark: [
      {max: 500, message: '备注限定500字符长度', trigger: ['blur', 'change']},
    ],
  },
  rules: {
    agent_id: [
      {required: true, message: '代理商必填', trigger: ['blur', 'change']},
    ],
    principal: [
      {required: true, message: '代理商负责人必填', trigger: ['blur', 'change']},
      {max: 20, message: '代理商负责人姓名限定20字符长度', trigger: ['blur', 'change']},
    ],
    principal_phone: [
      {required: true, message: '负责人电话必填', trigger: ['blur', 'change']},
      {validator: checkPhone, trigger: ['blur', 'change']},
    ],
    contact: [
      {max: 20, message: '医院联系人姓名限定20字符长度', trigger: ['blur', 'change']},
    ],
    contact_phone: [
      {validator: checkPhone, trigger: ['blur', 'change']},
    ],
    receiver: [
      {max: 20, message: '收货人姓名限定20字符长度', trigger: ['blur', 'change']},
    ],
    receiver_phone: [
      {validator: checkPhone, trigger: ['blur', 'change']},
    ],
    receiver_address: [
      {max: 50, message: '详细收货地址限定50字符长度', trigger: ['blur', 'change']},
    ],
    remark: [
      {max: 500, message: '备注限定500字符长度', trigger: ['blur', 'change']},
    ],
    price: [
      {required: true, message: '设备单价必填', trigger: ['blur']},
    ],
  },
  user_info: null,
  bind_region_tree: [],
  agent_list: [],
  all_agent_list: [],
  device_total: '',
  order_sale: {
    sale_id: '',
    sale_list: [],
  },
  order_details: {
    order_number: '',
    device_status: '',
    serial_number: '',
    hospital_id: 0,
    set_meal: 0,
    use_type: 0,
    user_id: 0,
  },
  hospital_rules: {
    name: [
      {required: true, message: '医院名称必填', trigger: 'blur'},
      {min: 1, max: 20, message: '医院名称长度限定为20个字符以内', trigger: ['blur', 'change']},
    ],
    region_id: [
      {required: true, message: '所属区域必填', trigger: ['blur', 'change']},
    ],
    address: [
      {required: true, message: '详细地址必填', trigger: ['blur', 'change']},
    ],
    phone: [
      {validator: checkPhone, trigger: ['blur', 'change']},
    ]
  },
  hospital_data_from: {
    id: 0,
    name: "",
    license_code: "",
    address: "",
    liaison: "",
    phone: "",
    region_id: [],
    index: '',
    action: '',
  },
  agent_rules: {
    name: [
      {required: true, message: '代理商名称必填', trigger: 'blur'},
      {min: 1, max: 20, message: '代理商名称长度限定为20个字符以内', trigger: ['blur', 'change']},
    ],
    type: [
      {required: true, message: '代理商类型必填', trigger: 'blur'},
    ],
    is_all: [
      {required: true, message: '代理商范围必填', trigger: 'blur'},
    ],
    time: [
      {required: true, message: '代理时限必填', trigger: 'blur'},
    ],
    region_id: [
      {required: true, message: '代理区域必填', trigger: 'blur'},
    ],
    phone: [
      {validator: checkPhone, trigger: ['blur', 'change']},
    ],
    email: [
      {type: 'email', message: '邮箱格式异常', trigger: ['blur', 'change']},
      {max: 30, message: '邮箱长度限定为30个字符', trigger: ['blur', 'change']},
    ],
  },
  agent_data_from: {
    group: "",
    name: "",
    type: '1',
    time: null,
    start_time: "",
    end_time: "",
    license_code: "",
    address: "",
    liaison: "",
    phone: "",
    email: "",
    region_id: [],
    hospital_id: [],
    is_all: '0',
    length: 0,
    create_at: "",
  },
  agent_type_options: [
    {value: "1", label: "区域代理商"},
    {value: "2", label: "报单代理商"},
  ],
})
let OPTIONS = {
  trial_version: [2, 18, 1000],
  old_version: [2, 3, 4, 5, 6, 7, 8],
  old_version_compare: {2: 1000, 3: 1001, 4: 1002, 5: 1100, 6: 1200, 7: 1300, 8: 1400},
}
// 去除永久使用
const filteredMonthOptions=(val) =>{
  // 如果套餐为 1000 或 18，移除 "永久使用" 选项
  if (val == 1000 || val == 18) {
    return month_options.filter(option => option.value !== -1);
  }
  return month_options;
}

const getMonthLabel=(value) =>{
  const option = month_options.find(option => option.value === value);
  return option ? option.label : value;
}
// 选择产品
const handleNewMealOptionChange = (val) => {
  if (val && val.length > 0) {
    selectedId.value = val[val.length - 1];
    selectedMeal.value = findSelectedMealById(Data.new_meal_options, selectedId.value);
    Data.device.set_meal = selectedId.value; // 切换用
    addDeviceHandel(selectedId.value,null)
  } else {
    selectedMeal.value = null;
  }

};

const findSelectedMealById = (options, id) => {
  for (const option of options) {
    if (option.id === id) {
      return option;
    } else if (option.m_val && option.m_val.length > 0) {
      const found = findSelectedMealById(option.m_val, id);
      if (found) return found;
    }
  }
  return null;
};
const handleDialogOpen = () => {
  nextTick(() => {
     setDefaultSelected();
  });

};

// 默认选择第一个
const setDefaultSelected = () => {
  // 默认选择第一个系列的第一个型号
  const firstSeries = Data.new_meal_options[0];
  if (firstSeries && firstSeries.m_val?.length > 0) {
    const firstModel = firstSeries.m_val[0];
    selectedId.value = [firstSeries.value, firstModel.value];
    selectedMeal.value = findSelectedMealById(Data.new_meal_options, firstModel.value);
  }
};

// 加一个开关 对于中英文切换的
const handleSwitchChange=(item,val) =>{
  // if (Data.device.set_meal >= 18 && Data.device.set_meal < 1000) {
  //   Data.product_options['new'].forEach((item, index) => {
  //     item['ChEn'] = Data.device.month
  //     item.children.forEach(item2 => {
  //       item2['ChEn'] = Data.device.month
  //     })
  //   })
  // } else {
  //   Data.product_options['old'].forEach((item, index) => {
  //     item['ChEn'] = Data.device.month
  //     item.children.forEach(item2 => {
  //       item2['ChEn'] = Data.device.month
  //     })
  //   })
  // }
  item.month_count = val
};

//校验联系电话
function checkPhone(rule, value, callback) {
  if (value) {
    let isPhone = new RegExp("^1[34578][0-9]{9}$", 'i')
    let isLand = new RegExp("^(0[0-9]{2,3}(-|\\+))([0-9]{7,8})$", 'i')
    if (isPhone.test(value) || isLand.test(value)) {
      callback();
    } else {
      callback(new Error('电话格式异常'));
    }
  } else {
    callback();
  }
}

function formatOptions(value, data_model = []) {
  let lable = ""
  data_model.forEach(item => {
    if (value == item.value) {
      lable = item.label
    }
  })

  return lable
}

function formatMonthCount(value) {
  let lable = ""
  month_options.forEach(item => {
    if (value == item.value) {
      lable = item.label
    }
  })

  return lable
}

function formatModelMonthCount(item2, value, item3) {
  item2.month_count = value
  let flist = []
  switch (item2.product_id){
    case 1000:
      flist = [1000001, 1000002, 1000003, 1000004, 1000005,
        12001, 12002, 12003,
        12004, 12005,
      ]
      break;
    case 1001:
      flist = [
        1001001, 1001002, 1001003, 1001004,
        1001005, 1001006,
        1001007, 1001008, 1001009, 1001010,
        1001011,
        12101, 12102, 12103, 12104,
        12105, 12106, 12107, 12108, 12109, 12110, 12111
      ]
      break;
    case 1002:
      flist = [
        1002001, 1002002, 1002003,
        1002004, 1002005,
        1002006, 1002007, 1002008,
        1002009,
        12201, 12202, 12203, 12204,
        12205, 12206, 12207
      ]
      break;
    case 1003:
      flist = [
        1003001, 1003002, 1003003,
        12301, 12302, 12303
      ]
      break;
    case 1004:
      flist = [
        1004001, 1004002,
        12401, 12402,
      ]
      break;
    case 1005:
      flist = [
        1005001, 1005002, 1005003, 1005004,
        12501, 12502, 12503, 12504,
      ]
      break;
    case 1006:
      flist = [
        1006001, 1006002, 1006003, 1006004,
        12601, 12602, 12603, 12604
      ]
      break;
    case 1007:
      flist = [
        1007001, 1007002, 1007003,
        1007004, 1007005, 1007006, 1007007,
        12701, 12702, 12703, 12704,
        12705, 12706, 12707
      ]
      break;
    case 1008:
      flist = [
        1008001,
        12801
      ]
      break;
    case 1009:
      flist = [
        1009001,
        12901
      ]
      break;
    case 1010:
      flist = [
        1010001, 1010002,
        13002
      ]
      break;

    case 120:
      flist = [12001, 12002, 12003,
        12004, 12005,
      ]
      break;
    case 121:
      flist = [12101, 12102, 12103, 12104 ,
        12105, 12106, 12107, 12108, 12109, 12110, 12111
      ]
      break;
    case 122:
      flist = [12201, 12202, 12203, 12204,
        12205, 12206, 12207
      ]
      break;
    case 123:
      flist = [12301, 12302, 12303]
      break;
    case 124:
      flist = [12401, 12402,]
      break;
    case 125:
      flist = [12501, 12502, 12503, 12504,]
      break;
    case 126:
      flist = [12601, 12602, 12603, 12604]
      break;
    case 127:
      flist = [12701, 12702, 12703, 12704,
        12705, 12706, 12707
      ]
      break;
    case 128:
      flist = [12801]
      break;
    case 129:
      flist = [12901]
      break;
    case 130:
      flist = [13001]
      break;
  }
  item2.children.forEach(child => {
    if (flist.includes(child.product_id)) {
      child.month_count = value;
    }
  });
  if (flist.includes(item3.product_id)) {
      item3.month_count = value;
  }
}

async function handleDelete(row) {
  let {code} = await orderDeleteApi({ids: [row.id]});
  if (code === 0) {
    message.success("删除成功")
    onSearch()
  }
}

function handleCurrentChange(val: number) {
  Data.search_from.page = val
  onSearch()
}

function handleSizeChange(val: number) {
  Data.search_from.page_size = val
  onSearch()
}

async function onSearch() {
  Data.loading = true;
  //根据用户权限判断查询全部列表，还是个人列表
  let response
  if (Auth('order/list')) {
    response = await orderListApi(Data.search_from);
  } else {
    response = await orderPersonalApi(Data.search_from);
  }
  let data = response.data
  if (data.total <= Data.search_from.page_size && Data.search_from.page > 1) {
    Data.search_from.page = 1
    onSearch()
  }



  Data.data_list = data.list;
  Data.search_from.total = data.total;
  Data.device_total = data.device_num
  Data.loading = false;
}

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  Data.search_hospital_list = []
  onSearch();
};
const resetUpgradeForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  onSearchUpgrade();
};

//提交创建订单
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      let device_count = Data.data_from.devices.length
      if (!(device_count >= 1 && device_count <= 99)) {
        message.error("设备数量异常，可添加设备数为1到99之间")
        return
      }

      if (Data.data_from.receiver_region_arr?.length >= 3) {
        Data.data_from.receiver_region = Data.data_from.receiver_region_arr[2]
      }
      var err_msg = ''
      Data.data_from.devices.forEach((item, index) => {
        if (item?.deploy_region?.length > 0) {
          Data.data_from.devices[index].region_id = item.deploy_region[item.deploy_region.length - 1]
        }
        if (item.set_meal == '1001') {
          Data.data_from.devices[index].contract_company = '广州爱孕记信息科技有限公司'
        } else {
          Data.data_from.devices[index].contract_company = '长沙爱孕记医疗科技有限公司'
        }
        //创建订单时，设备金额必填
        if (item.price == undefined || Number(item.price) <= 0) {
          err_msg = "请填写套餐单价"
          return
        }
        if (isNaN(item.hospital_id)) {
          item.hospital_name = item.hospital_id
          item.hospital_id = 0
        }
      })
      if (err_msg) {
        message.error(err_msg)
        return
      }
      Data.data_from.devices.forEach((item, index) => {
        //后端金额存储用整数型，乘以100
        if (Number(item.price) > 0) {
          var price = item.price.toString().match(/\d+\.?\d{0,2}/)[0]
          Data.data_from.devices[index].price = (Number(price) * 100).toFixed()
        }
        if (Data.data_from.devices[index].maintenance_time != null) {
          Data.data_from.devices[index].maintenance_times = dateFormat(Data.data_from.devices[index].maintenance_time, 'YYYY-mm-dd HH:MM:SS')
        }
      })
      let {code, msg} = await orderCreateApi(Data.data_from);
      if (code === 0) {
        message.success(msg)
        Data.dialogVisible = false
        clearCache()
        onSearch()
      } else {
        //报错，重新格式金额
        Data.data_from.devices.forEach((item, index) => {
          if (Number(item.price) > 0) {
            Data.data_from.devices[index].price = (Number(item.price) / 100).toString()
          }
        })
      }
    } else {
      message.error("有表单必填项未完成")
    }
  })
}

//存为草稿
async function handleOrderDraft() {
  if (Data.data_from.receiver_region_arr?.length >= 3) {
    Data.data_from.receiver_region = Data.data_from.receiver_region_arr[2]
  }
  Data.data_from.devices?.forEach((item, index) => {
    if (item?.deploy_region?.length > 0) {
      Data.data_from.devices[index].region_id = item.deploy_region[item.deploy_region.length - 1]
    }
    if (item.set_meal == '1001') {
      Data.data_from.devices[index].contract_company = '广州爱孕记信息科技有限公司'
    } else {
      Data.data_from.devices[index].contract_company = '长沙爱孕记医疗科技有限公司'
    }
    if (Data.data_from.devices[index].maintenance_time != null) {
      Data.data_from.devices[index].maintenance_times = dateFormat(Data.data_from.devices[index].maintenance_time, 'YYYY-mm-dd HH:MM:SS')
    }
    //后端金额存储用整数型，乘以100
    if (Number(item.price) > 0) {
      var price = item.price.toString().match(/\d+\.?\d{0,2}/)[0]
      Data.data_from.devices[index].price = Number(price) * 100
    }
    if (isNaN(item.hospital_id)) {
      item.hospital_name = item.hospital_id
      item.hospital_id = 0
    }
  })
  let {code, msg} = await orderDraftApi(Data.data_from);
  if (code === 0) {
    message.success(msg)
    Data.dialogVisible = false
    clearCache()
  } else {
    //报错，重新格式金额
    Data.data_from.devices.forEach((item, index) => {
      if (Number(item.price) > 0) {
        Data.data_from.devices[index].price = (Number(item.price) / 100).toString()
      }
    })
  }
}

//提交订单升级
const submitUpgradeForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (Data.data_from.devices == null) {
        message.error("设备数量异常")
        return
      }
      let device_count = Data.data_from.devices.length
      if (!(device_count >= 1 && device_count <= 99)) {
        message.error("设备数量异常，可添加设备数为1到99之间")
        return
      }
      let {code, msg} = await orderUpgradeApi(Data.data_from);
      if (code === 0) {
        message.success(msg)
        Data.dialogVisible_OrderUpgrade = false
        clearCache()
        onSearch()
      }
    } else {
      message.error("有表单必填项未完成")
    }
  })
}

function clearCache() {
  Data.curr_set_meal = 1;
  selectedId.value = null;
  selectedMeal.value = null;
}

//清理升级缓存
function clearUpgradeCache() {
  Data.data_from = {
    id: 0,
    contract_number: "",
    region_id: [],
    hospital_id: "",
    hospital_region_id: "",
    agent_region_id: "",
    agent_id: "",
    ukey_code: "",
    sale_name: "",
    sale_phone: "",
    principal: "",
    principal_phone: "",
    contact: "",
    contact_phone: "",
    receiver: "",
    receiver_phone: "",
    receiver_region: "",
    receiver_region_arr: [],
    receiver_address: "",
    count: 1,
    maintenance: 3,
    probation: 30,
    remark: "",
    is_urgent: 0,
    devices: [],
    device_index: '0',
    region_ids: [],
    price: [],
    price_ref: [],
  }
  Data.dialogVisible_OrderUpgrade = false
}

//清理订单详情缓存
function clearOrderDetails() {
  _self.editprice=false;
  _self.edisBox=false;
  Data.visible_OrderTransfer = false
}

const cancelForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  Data.dialogVisible = false
  Data.dialogVisible_OrderUpgrade = false
  clearCache()
  formEl.resetFields()
}

//创建订单/查看订单详情
function handleUpdate(row) {
  cancelForm(ruleFormRef.value)
  if (row != null) {
    cancelStatusForm(null)
    orderDetails(row)
  } else {
    Data.data_from = {
      id: 0,
      contract_number: "",
      region_id: [],
      hospital_id: "",
      hospital_region_id: "",
      agent_region_id: "",
      agent_id: "",
      ukey_code: "",
      sale_name: "",
      sale_phone: "",
      principal: "",
      principal_phone: "",
      contact: "",
      contact_phone: "",
      receiver: "",
      receiver_phone: "",
      receiver_region: "",
      receiver_region_arr: [],
      receiver_address: "",
      count: 1,
      maintenance: 3,
      probation: 30,
      remark: "",
      is_urgent: 0,
      devices: [],
      device_index: '0',
      region_ids: [],
      price: [],
      price_ref: [],
    }
    Data.hospital_list = JSON.parse(JSON.stringify(Data.all_hospital_list))
    Data.user_hospital_list = JSON.parse(JSON.stringify(Data.all_hospital_list))
    Data.agent_list = JSON.parse(JSON.stringify(Data.all_agent_list))
    //查询是否有草稿
    orderDraft()
    Data.data_from.sale_name = Data.user_info.username
    Data.data_from.sale_phone = Data.user_info.phone
    Data.dialogVisible = true
  }
}

//获取设备产品信息
async function getMealOptions() {
  let {data} = await orderMealOptionsApi();
  Data.product_options = data.product_options;
  Data.new_meal_options = data.new_meal_options
  // Data.meal_options = new Map()
  data.meal_options.forEach(item => {
    Data.meal_options[item.id] = item.name
  })

  Data.meal_options_array = data.meal_options.slice(7, data.meal_options.length);

  Data.meal_def_options = data.meal_def_options;

  // 给每一个子集都赋值月份
  assignMonthCount(Data.product_options);

}

function assignMonthCount(data) {
  // 遍历 new 和 old 数组
  ['new', 'old'].forEach(key => {
    data[key].forEach(parent => {
      // 获取父级的 month_count
      const parentMonthCount = parent.month_count;

      // 检查并遍历 children
      if (Array.isArray(parent.children)) {
        parent.children.forEach(child => {
          // 将父级的 month_count 赋值给子级的 month_count
          if (child.product_id == 1000002 || child.product_id == 12002) {
            child.month_count = 0
          } else {
             child.month_count = parentMonthCount;
          }

        });
      }
    });
  });
}

/*

*/

function formatMealOptions(index) {
  Data.meal_options.forEach(item => {
    if (item.id == index) {
      return item.name
    }
  })
}

async function getRegion() {
  let {data} = await getRegionApi();
  Data.region_tree = data;
}

//设备，根据区域获取医院列表
async function changeRegionHandel(value, index, device) {
  Data.search_from.hospital_id = "";
  if (typeof (index) != "undefined") {
    Data.data_from.devices[index].hospital_id = "";
    Data.data_from.devices[index].deploy_units = "";
  }
  if (value == null) {
    Data.hospital_list = JSON.parse(JSON.stringify(Data.all_hospital_list))
    if (device != null) {
      device.hospital_list = JSON.parse(JSON.stringify(Data.all_hospital_list))
    }
    return
  }
  Data.hospital_list = []
  Data.search_hospital_list = []
  getHospitalList(value, device)
}

async function changeDetailsRegionHandel(value, index, device) {
  if (typeof (index) != "undefined") {
    Data.order_info.devices[index].hospital_id = "";
    Data.order_info.devices[index].deploy_units = "";
  }
  if (value == null) {
    Data.hospital_list = JSON.parse(JSON.stringify(Data.all_hospital_list))
    if (device != null) {
      device.hospital_list = JSON.parse(JSON.stringify(Data.all_hospital_list))
    }
    return
  }
  getHospitalList(value, device)
}

async function changeDeviceDetailsRegionHandel(value, device) {
  Data.device_info.hospital_id = "";
  Data.device_info.deploy_units = "";
  if (value == null) {
    Data.hospital_list = JSON.parse(JSON.stringify(Data.all_hospital_list))
    if (device != null) {
      device.hospital_list = JSON.parse(JSON.stringify(Data.all_hospital_list))
    }
    return
  }
  getHospitalList(value, device)
}

async function getHospitalList(region_id, device) {
  let {data} = await hospitalListApi({
    'user_id': Data.user_info.id,
    'region_id': region_id,
    'page_size': 9999999,
    "status": 1
  })
  Data.hospital_list = data.list;
  Data.search_hospital_list = JSON.parse(JSON.stringify(data.list))
  if (device != null) {
    device.hospital_list = JSON.parse(JSON.stringify(data.list))
  }
}

//订单，根据区域获取医院列表
async function changeUserRegionHandle() {
  Data.data_from.hospital_id = ""
  Data.data_from.contact = ""
  Data.data_from.contact_phone = ""
  if (Data.data_from.hospital_region_id) {
    let {data} = await hospitalListApi({
      'user_id': Data.user_info.id,
      'region_id': Data.data_from.hospital_region_id,
      'page_size': 9999999,
      "status": 1
    })
    Data.user_hospital_list = data.list
  } else {
    Data.user_hospital_list = JSON.parse(JSON.stringify(Data.all_hospital_list))
  }
}

//订单新增，选择默认医院
async function changeUserHospitalHandle(is_upgrade) {
  Data.data_from.contact = ""
  Data.data_from.contact_phone = ""
  if (Data.data_from.hospital_id) {
    Data.user_hospital_list?.forEach(item => {
      if (item?.id == Data.data_from.hospital_id) {
        Data.data_from.contact = item?.liaison
        Data.data_from.contact_phone = item?.phone
        var regions = []
        if (item?.region_id) {
          let region_bit_arr = item?.region_id?.split("")
          if (region_bit_arr.length == 6) {
            if (!(region_bit_arr[4] == 0 && region_bit_arr[5] == 0)) {
              regions = [
                region_bit_arr[0] + region_bit_arr[1] + "0000",
                region_bit_arr[0] + region_bit_arr[1] + region_bit_arr[2] + region_bit_arr[3] + '00',
                item?.region_id
              ]
            } else if (region_bit_arr[4] == 0 && region_bit_arr[5] == 0 && (!(region_bit_arr[2] == 0 && region_bit_arr[3] == 0))) {
              regions = [
                region_bit_arr[0] + region_bit_arr[1] + "0000",
                item?.region_id
              ]
            } else {
              regions = [item?.region_id]
            }
          }
        }
        Data.data_from.hospital_region_id = regions
        //选择默认医院后，同步未选择部署医院的设备，升级订单不同步
        if (!is_upgrade) {
          Data.data_from.devices?.forEach((item1, index1) => {
            if (!item1?.hospital_id) {
              Data.data_from.devices[index1].hospital_id = Data.data_from.hospital_id
              Data.data_from.devices[index1].deploy_units = item?.address
              Data.data_from.devices[index1].deploy_region = regions
            }
          })
        }
      }
    })
  }

}

//订单详情，根据区域获取医院列表
async function changeOrderRegionHandle() {
  Data.order_info.hospital_id = ""
  Data.order_info.contact = ""
  Data.order_info.contact_phone = ""
  if (Data.order_info?.hospital_region_id) {
    let {data} = await hospitalListApi({
      'user_id': Data.user_info.id,
      'region_id': Data.order_info?.hospital_region_id,
      'page_size': 9999999,
      "status": 1
    })
    Data.user_hospital_list = data.list
  } else {
    Data.user_hospital_list = JSON.parse(JSON.stringify(Data.all_hospital_list))
  }
}

//订单详情，选择默认医院
async function changeOrderHospitalHandle() {
  Data.order_info.contact = ""
  Data.order_info.contact_phone = ""
  if (Data.order_info.hospital_id) {
    Data.user_hospital_list?.forEach(item => {
      if (item?.id == Data.order_info.hospital_id) {
        Data.order_info.contact = item?.liaison
        Data.order_info.contact_phone = item?.phone
        var regions = []
        if (item?.region_id) {
          let region_bit_arr = item?.region_id?.split("")
          if (region_bit_arr.length == 6) {
            if (!(region_bit_arr[4] == 0 && region_bit_arr[5] == 0)) {
              regions = [
                region_bit_arr[0] + region_bit_arr[1] + "0000",
                region_bit_arr[0] + region_bit_arr[1] + region_bit_arr[2] + region_bit_arr[3] + '00',
                item?.region_id
              ]
            } else if (region_bit_arr[4] == 0 && region_bit_arr[5] == 0 && (!(region_bit_arr[2] == 0 && region_bit_arr[3] == 0))) {
              regions = [
                region_bit_arr[0] + region_bit_arr[1] + "0000",
                item?.region_id
              ]
            } else {
              regions = [item?.region_id]
            }
          }
        }
        Data.order_info.hospital_region_id = regions
        //选择默认医院后，同步未选择部署医院的设备，升级订单不同步
        Data.order_info.devices?.forEach((item1, index1) => {
          if (!item1?.hospital_id) {
            Data.order_info.devices[index1].hospital_id = Data.order_info.hospital_id
            Data.order_info.devices[index1].deploy_units = item?.address
            Data.order_info.devices[index1].deploy_region = regions
          }
        })
      }
    })
  }
}

//根据搜索区域获取医院列表
function changeSearchRegionHandel(value, index) {
  Data.search_from.hospital_id = "";
  Data.search_hospital_list = []
  getSearchHospitalList(value)
}

async function getSearchHospitalList(region_id) {
  let {data} = await hospitalListApi({'region_id': region_id, 'page_size': 9999999, "status": 1})
  Data.search_hospital_list = JSON.parse(JSON.stringify(data.list))
}

//获取用户区域下全部医院列表
async function getAllHospitalList() {
  let {data} = await hospitalListApi({'user_id': Data.user_info.id, 'page_size': 9999999, "status": 1})
  Data.all_hospital_list = data.list;
}

//获取医院详细地址，回显区域
function handleHospitalAddress(device, hospital_id) {
  var deploy_units = ''
  var region_id = ''
  Data.all_hospital_list.forEach(item => {
    if (hospital_id == item.id) {
      deploy_units = item.address
      region_id = item.region_id
    }
  })
  if (region_id == '') {
    Data.hospital_list.forEach(item => {
      if (hospital_id == item.id) {
        deploy_units = item.address
        region_id = item.region_id
      }
    })
  }
  if (region_id != "") {
    device.deploy_region = getDeplyRegions(region_id)
  }
  device.deploy_units = deploy_units
}

//创建升级订单
function handleCreateOrderUpgrade() {
  Data.data_from.sale_name = Data.user_info.username
  Data.data_from.sale_phone = Data.user_info.phone
  Data.order_details.device_status = '99'
  Data.order_details.user_id = Data.user_info.id
  orderDetailsApi(Data.order_details).then(ret => {
    if (ret.data?.order_number == "") {
      Data.data_from.region_ids = ret.data?.region_ids
      return
    }
    Data.data_from.region_ids = ret.data?.region_ids
  })
  Data.hospital_list = JSON.parse(JSON.stringify(Data.all_hospital_list))
  Data.user_hospital_list = JSON.parse(JSON.stringify(Data.all_hospital_list))
  Data.agent_list = JSON.parse(JSON.stringify(Data.all_agent_list))
  Data.dialogVisible_CreateOrderUpgrade = true
}

//选择升级设备
function handleSelectUpgradeDevice() {
  Data.search_from_upgrade = {
    keyword: "",
    order_number: "",
    contract_number: "",
    serial_number: "",
    region_ids: [],
    hospital_id: "",
    status: "",
    upgrade_status: [1, 9],
    set_meal: "",
    time: null,
    use_type: "",
    order_type: "",
    device_status: 1,
    page_size: 10,
    page: 1,
    total: 0
  }
  Data.upgrade_order_devices = []
  onSearchUpgrade()
  Data.hospital_list = []
  Data.dialogVisible_SelectUpgradeDevice = true
}

//清理创建订单缓存
function clearCreateOrderCache(formEl: FormInstance | undefined) {
  if (formEl) {
    formEl.clearValidate()
  }
  Data.data_from = {
    id: 0,
    contract_number: "",
    region_id: [],
    hospital_id: "",
    agent_region_id: "",
    agent_id: "",
    ukey_code: "",
    sale_name: "",
    sale_phone: "",
    principal: "",
    principal_phone: "",
    contact: "",
    contact_phone: "",
    receiver: "",
    receiver_phone: "",
    receiver_region: "",
    receiver_region_arr: [],
    receiver_address: "",
    count: 1,
    maintenance: 3,
    probation: 30,
    remark: "",
    is_urgent: 0,
    devices: [],
    device_index: 0,
  }
  Data.search_from_upgrade = {
    keyword: "",
    order_number: "",
    contract_number: "",
    serial_number: "",
    region_ids: [],
    hospital_id: "",
    status: "",
    upgrade_status: [1, 9],
    set_meal: "",
    time: null,
    use_type: "",
    order_type: "",
    device_status: 1,
    page_size: 10,
    page: 1,
    total: 0
  }
  Data.upgrade_data_list = []
  Data.upgrade_order_devices = []
}

//查询升级设备列表
async function onSearchUpgrade() {
  Data.upgrade_loading = true;
  //根据用户权限判断查询全部列表，还是个人列表
  let response
  if (Auth('order/list')) {
    response = await orderListApi(Data.search_from_upgrade);
  } else {
    response = await orderPersonalApi(Data.search_from_upgrade);
  }
  let data = response.data
  if (data.total <= Data.search_from_upgrade.page_size && Data.search_from_upgrade.page > 1) {
    Data.search_from_upgrade.page = 1
    onSearchUpgrade()
  }
  Data.upgrade_data_list = data.list;
  //格式化设备金额，除以100
  Data.upgrade_data_list?.forEach((item, index) => {
    item.devices?.forEach((item1, index1) => {
      if (Number(item1.price) > 0) {
        Data.upgrade_data_list[index].devices[index1].price = (Number(item1.price) / 100).toString()
      }
    })
  })
  Data.search_from_upgrade.total = data.total;
  Data.upgrade_loading = false;
}

function handleCurrentChangeUpgrade(val: number) {
  Data.search_from_upgrade.page = val
  onSearchUpgrade()
}

function handleSizeChangeUpgrade(val: number) {
  Data.search_from_upgrade.page_size = val
  onSearchUpgrade()
}

//选择升级设备
function handleSelectionChange(devices, order_number, order) {
  devices.forEach((item, index) => {
    devices[index].region_ids = order.region_ids
    devices[index].hospital_list = JSON.parse(JSON.stringify(Data.all_hospital_list))
  })
  Data.upgrade_order_devices[order_number] = devices
}

//确认升级设备
function submitUpgradeDevices() {
  Data.upgrade_order_devices.forEach((item, key) => {
    item.forEach(item1 => {
      var exist = false
      Data.data_from.devices.forEach(item2 => {
        if (item2.id == item1.id) {
          exist = true
          return
        }
      })
      if (!exist) {
        if (item1.set_meal_upgrade > 0) {
          item1.set_meal = item1.set_meal_upgrade
          item1.is_append = item1.is_append_upgrade
          item1.set_meal_upgrade = ''
          item1.is_append_upgrade = 0
        }
        item1.deploy_region = getDeplyRegions(item1.region_id)
        item1.hospital_id = item1.hospital_id == 0 ? '' : item1.hospital_id
        if (item1.hospital_id > 0) {
          //判断医院列表中是否已有该医院
          var exist = false
          item1.hospital_list.forEach(item3 => {
            if (item3.id == item1.hospital_id) {
              exist = true
            }
          })
          if (!exist) {
            Data.hospital_list.push({id: item1.hospital_id, name: item1.hospital_name, region_id: item1.region_id})
            item1.hospital_list.push({id: item1.hospital_id, name: item1.hospital_name, region_id: item1.region_id})
          }
        }
        item1.maintenance = 3
        item1.maintenance_time = null
        Data.data_from.devices.push(JSON.parse(JSON.stringify(item1)))
      }
    })
  })
  Data.dialogVisible_SelectUpgradeDevice = false
}

//取消选择升级设备
function cancleUpgradeDevices() {
  Data.search_from_upgrade = {
    keyword: "",
    order_number: "",
    contract_number: "",
    serial_number: "",
    region_ids: [],
    hospital_id: "",
    status: "",
    upgrade_status: [1, 9],
    set_meal: "",
    time: null,
    use_type: "",
    order_type: "",
    device_status: 1,
    page_size: 10,
    page: 1,
    total: 0
  }
  Data.upgrade_data_list = []
  Data.upgrade_order_devices = []
  Data.dialogVisible_SelectUpgradeDevice = false
}

//取消创建升级订单
const canceCreateUpgradeOrderlForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  Data.dialogVisible_CreateOrderUpgrade = false
  clearCreateOrderCache(formEl)
  formEl.resetFields()
}

//提交创建升级订单
const submitCreateUpgradeOrderlForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      let device_count = Data.data_from.devices.length
      if (!(device_count >= 1 && device_count <= 99)) {
        message.error("设备数量异常，可添加设备数为1到99之间")
        return
      }
      var err_msg = ""
      Data.data_from.devices.forEach((item, index) => {
        if (item.set_meal_upgrade <= 0) {
          err_msg = "请选择升级套餐"
          return
        }
        if (Number(item.price) <= 0) {
          err_msg = "请填写升级套餐单价"
          return
        }
      })
      if (err_msg) {
        message.error(err_msg)
        return
      }
      Data.data_from.devices.forEach((item, index) => {
        //后端金额存储用整数型，乘以100
        if (Number(item.price) > 0) {
          var price = item.price.toString().match(/\d+\.?\d{0,2}/)[0]
          Data.data_from.devices[index].price = (Number(price) * 100).toFixed()
        }
        Data.data_from.devices[index].set_meal = Data.data_from.devices[index].set_meal_upgrade
        Data.data_from.devices[index].is_append = Data.data_from.devices[index].is_append_upgrade
        if (Data.data_from.devices[index].maintenance_time != null) {
          Data.data_from.devices[index].maintenance_times = dateFormat(Data.data_from.devices[index].maintenance_time, 'YYYY-mm-dd HH:MM:SS')
        }
      })
      if (Data.data_from.receiver_region_arr.length >= 0) {
        Data.data_from.receiver_region = Data.data_from.receiver_region_arr[Data.data_from.receiver_region_arr.length - 1]
      }
      if (Data.data_from.region_id.length >= 0) {
        Data.data_from.agent_region_id = Data.data_from.region_id[Data.data_from.region_id.length - 1]
      }
      let {code, msg} = await upgradeOrderCreateApi(Data.data_from);
      if (code === 0) {
        message.success(msg)
        Data.dialogVisible_CreateOrderUpgrade = false
        clearCreateOrderCache(formEl)
        onSearch()
      } else {
        //报错，重新格式金额
        Data.data_from.devices.forEach((item, index) => {
          if (Number(item.price) > 0) {
            Data.data_from.devices[index].price = (Number(item.price) / 100).toString()
          }
        })
      }
    } else {
      message.error("有表单必填项未完成")
    }
  })
}


function removeUpgradeDevice(index) {
  Data.data_from.devices.splice(index, 1)
}

let _self = reactive({
  daotorLists: [],
  data_from: {},
  edisBox: false,
  editprice: false,
  roid: JSON.parse(sessionStorage.getItem("info")).role_id,
  Useid: JSON.parse(sessionStorage.getItem("info")).id,
  assUser:'',
  isfankui:false,
});

onMounted(async () => {
  Data.user_info = storageSession.getItem("info")
  await getRegion()
  await getBindRegion()
  await getAgentList(null)
  await getUserRegion()
  await getMealOptions()
  await getAllHospitalList()
  await onSearch();
  await doctorList();


});


async function doctorList() {
  if (Auth('order/assign')) {
    let data = await getDoctorList();
    _self.daotorLists = data?.data;
  }
}


// 定义禁止选择的时间范围
const disabledDate = (time) => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const targetDate = new Date(time.getFullYear(), time.getMonth(), time.getDate());
  // 禁用今天之前的日期
  return targetDate < today
}

//级联选择器样式，可以点击每一级
const propsSearch = {
  checkStrictly: true,
}

//获取用户绑定代理商区域
async function getBindRegion() {
  let {data} = await getBindRegionApi();
  Data.bind_region_tree = data;
}

//选择代理商区域
function changeBindRegionHandle(value) {
  Data.data_from.agent_id = "";
  _self.data_from.agent_id="";
  _self.data_from.principal="";
  _self.data_from.principal_phone="";
  Data.agent_list = []
  getAgentList(value)
}

//根据区域查询代理商
async function getAgentList(region_id) {
  var data_from = {'user_id': Data.user_info.id, 'status': 1, 'sort': 'date_status'}
  if (region_id) {
    data_from.region_id = region_id
  }
  let {code, data} = await agentHadBindApi(data_from);
  if (code === 0) {
    Data.agent_list = data
    if (region_id == null) {
      Data.all_agent_list = JSON.parse(JSON.stringify(Data.agent_list))
    }
  }
}

//根据用户权限查询代理商
async function getAllAgentList() {
  let response
  if (Auth('agent/list')) {
    response = await agentListApi({'status': 1, 'sort': 'date_status'});
  } else {
    response = await agentPersonalApi({'user_id': Data.user_info.id, 'status': 1, 'sort': 'date_status'});
  }
  Data.all_agent_list = response.data?.list
}

//选择代理商
function changeAgentHandle() {
  Data.data_from.principal = "";
  Data.data_from.principal_phone = "";


  if (Data.data_from.agent_id || _self.data_from.agent_id) {
    Data.agent_list?.forEach(item => {

      if (item?.group == (Data.data_from.agent_id ||_self.data_from.agent_id)){

        if(_self.data_from.agent_id){
          _self.data_from.principal = item?.liaison;
          _self.data_from.principal_phone = item?.phone;
        }
        Data.data_from.principal = item?.liaison
        Data.data_from.principal_phone = item?.phone
      }
    })
  }
}

//收货信息套用代理商信息，新增
function selectAgentHandle() {
  Data.data_from.receiver = ""
  Data.data_from.receiver_phone = ""
  Data.data_from.receiver_address = ""
  Data.data_from.receiver_region_arr = []
  if (Data.data_from.agent_id) {
    Data.agent_list?.forEach(item => {
      if (item?.group == Data.data_from.agent_id) {
        Data.data_from.receiver = item?.liaison
        Data.data_from.receiver_phone = item?.phone
        Data.data_from.receiver_address = item?.address
      }
    })
  } else {
    message.warn("请选择代理商")
  }
}

//收货信息套用医院信息，新增
function selectHospitalHandle() {
  Data.data_from.receiver = ""
  Data.data_from.receiver_phone = ""
  Data.data_from.receiver_address = ""
  Data.data_from.receiver_region_arr = []
  if (Data.data_from.hospital_id) {
    Data.user_hospital_list?.forEach(item => {
      if (item?.id == Data.data_from.hospital_id) {
        Data.data_from.receiver = item?.liaison
        Data.data_from.receiver_phone = item?.phone
        Data.data_from.receiver_address = item?.address
        if (item?.region_id) {
          let region_bit_arr = item?.region_id?.split("")
          if (region_bit_arr.length == 6) {
            if (!(region_bit_arr[4] == 0 && region_bit_arr[5] == 0)) {
              Data.data_from.receiver_region_arr = [
                region_bit_arr[0] + region_bit_arr[1] + "0000",
                region_bit_arr[0] + region_bit_arr[1] + region_bit_arr[2] + region_bit_arr[3] + '00',
                item?.region_id
              ]
            } else if (region_bit_arr[4] == 0 && region_bit_arr[5] == 0 && (!(region_bit_arr[2] == 0 && region_bit_arr[3] == 0))) {
              Data.data_from.receiver_region_arr = [
                region_bit_arr[0] + region_bit_arr[1] + "0000",
                item?.region_id
              ]
            } else {
              Data.data_from.receiver_region_arr = [item?.region_id]
            }
          }
        }
      }
    })
  } else {
    message.warn("请选择医院")
  }
}

//收货信息套用代理商信息，详情
function selectDetailsAgentHandle() {
  Data.order_info.receiver = ""
  Data.order_info.receiver_phone = ""
  Data.order_info.receiver_address = ""
  Data.order_info.receiver_region_arr = []
  if (Data.order_info.agent_id) {
    Data.order_info.receiver = Data.order_info?.agent?.liaison
    Data.order_info.receiver_phone = Data.order_info?.agent?.phone
    Data.order_info.receiver_address = Data.order_info?.agent?.address

  } else {
    message.warn("请选择代理商")
  }
}

//收货信息套用医院信息，详情
function selectDetailsHospitalHandle() {
  Data.order_info.receiver = ""
  Data.order_info.receiver_phone = ""
  Data.order_info.receiver_address = ""
  Data.order_info.receiver_region_arr = []
  if (Data.order_info.hospital_id) {
    Data.user_hospital_list?.forEach(item => {
      if (item?.id == Data.order_info.hospital_id) {
        Data.order_info.receiver = item?.liaison
        Data.order_info.receiver_phone = item?.phone
        Data.order_info.receiver_address = item?.address
        Data.order_info.receiver_region_arr = getDeplyRegions(item?.region_id)
      }
    })
  } else {
    message.warn("请选择医院")
  }
}

//获取用户区域树
async function getUserRegion() {
  //显示用户区域
  let {code, data} = await getUserRegionApi();
  if (code === 0) {
    Data.user_region_tree = data?.tree
    Data.agent_region_tree = JSON.parse(JSON.stringify(Data.user_region_tree))
    //如果用户可查看全部医院，则区域树为全部区域
    if (Auth('hospital/list')) {
      Data.user_region_tree = JSON.parse(JSON.stringify(Data.region_tree))
    }
  }
}

//获取创建订单设备总金额
function getTotalPrice() {
  var total = 0
  Data.data_from.devices?.forEach(item => {
    if (item.price == undefined) {
      return
    }
    if (item.price.toString().match(/\d+\.?\d{0,2}/) != null) {
      total += Number(item.price.toString().match(/\d+\.?\d{0,2}/)[0])
    }
  })
  if (total > 0) {
    total = Number(total.toFixed(2))
  }
  return total
}

//获取订单详情设备总金额
function getOrderTotalPrice() {
  var total = 0
  Data.order_info.devices?.forEach(item => {
    if (item.price == undefined) {
      return
    }
    if (item.price.toString().match(/\d+\.?\d{0,2}/) != null) {
      total += Number(item.price.toString().match(/\d+\.?\d{0,2}/)[0])
    }
  })
  if (total > 0) {
    total = Number(total.toFixed(2))
  }
  return total
}

//金额大写转换
function formatPrice(money) {
  if (money == undefined || money == '0' || money == '') {
    return '零'
  }
  if (typeof money !== "string") {
    money = money.toString()
  }
  if (money.match(/\d+\.?\d{0,2}/) == null) {
    return '零'
  }
  money = money.match(/\d+\.?\d{0,2}/)[0]
  // 汉字的数字
  const cnNums = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖',]
  // 基本单位
  const cnIntRadice = ['', '拾', '佰', '仟']
  // 对应整数部分扩展单位
  const cnIntUnits = ['', '万', '亿', '兆']
  // 对应小数部分单位
  const cnDecUnits = ['角', '分']
  // 整数金额时后面跟的字符
  const cnInteger = '整'
  // 整型完以后的单位
  const cnIntLast = '元'
  // 最大处理的数字
  const maxNum = 9999999999999999.99
  // 金额整数部分
  let integerNum
  // 金额小数部分
  let decimalNum
  // 输出的中文金额字符串
  let chineseStr = ''
  // 分离金额后用的数组，预定义
  let parts
  if (money === '') {
    return ''
  }
  money = parseFloat(money)
  if (money >= maxNum) {
    // 超出最大处理数字
    return ''
  }
  //值为0 赋值为 空字符
  if (money === 0) {
    return '';
  }
  // 转换为字符串
  money = money.toString()
  //负数 最前方加入 负
  if (money.indexOf('-') === 0) {
    chineseStr += '负'
    money = money.substr(1)
  }
  if (money.indexOf('.') === -1) {
    integerNum = money

    decimalNum = ''
  } else {
    parts = money.split('.')
    integerNum = parts[0]
    decimalNum = parts[1].substr(0, 4)
  }
  // 获取整型部分转换
  if (parseInt(integerNum, 10) > 0) {
    let zeroCount = 0
    const IntLen = integerNum.length
    for (let i = 0; i < IntLen; i++) {
      const n = integerNum.substr(i, 1)
      const p = IntLen - i - 1
      const q = p / 4
      const m = p % 4
      if (n === '0') {
        zeroCount++
      } else {
        if (zeroCount > 0) {
          chineseStr += cnNums[0]
        }
        // 归零
        zeroCount = 0
        //alert(cnNums[parseInt(n)])
        chineseStr += cnNums[parseInt(n)] + cnIntRadice[m]
      }
      if (m === 0 && zeroCount < 4) {
        chineseStr += cnIntUnits[q]
      }
    }
    chineseStr += cnIntLast
  }
  //小数部分
  if (decimalNum !== '') {
    const decLen = decimalNum.length
    for (let i = 0; i < decLen; i++) {
      const n = decimalNum.substr(i, 1)
      if (n !== '0') {
        chineseStr += cnNums[Number(n)] + cnDecUnits[i]
      }
    }
  }

  if (chineseStr === '') {
    chineseStr += cnNums[0] + cnIntLast + cnInteger
  } else if (decimalNum === '') {
    chineseStr += cnInteger
  }
  return chineseStr
}

// 更新设备信息，部署医院
async function updateDeviceInfo(device) {
  if (device.deploy_region?.length > 0) {
    device.region_id = device.deploy_region[device.deploy_region.length - 1]
  }
  if (isNaN(device.hospital_id) && device.deploy_region == null) {
    message.error('请选择部署区域')
    return
  }
  let from = new FormData()
  from.append('device_id', device.id);
  from.append('region_id', device.region_id);
  from.append('deploy_units', device.deploy_units);
  if (isNaN(device.hospital_id)) {
    from.append('hospital_name', device.hospital_id);
  } else {
    from.append('hospital_id', device.hospital_id);
  }

  let {code, msg} = await orderDeviceUpdateApi(from);
  if (code !== 0) {
    message.error(msg)
    return
  }
  onSearch()
}

// 更新设备维保信息
async function updateMaintenanceInfo(device) {
  var maintenance_time = ""
  if(device.maintenance_time) {
    var datetime = new Date(device.maintenance_time.getFullYear(), device.maintenance_time.getMonth(), device.maintenance_time.getDate(), device.maintenance_time.getHours(), device.maintenance_time.getMinutes(), device.maintenance_time.getSeconds())
    maintenance_time = dateFormat(datetime, "YYYY-mm-dd HH:MM:SS")
  }
  let {code, msg} = await orderDeviceUpdateApi({
    device_id: device.id,
    maintenance: device.maintenance,
    maintenance_time: maintenance_time
  });
  if (code !== 0) {
    message.error(msg)
    return
  }
}

//判断设备部署医院区域是否在用户区域内
function checkDeviceRegionInAgent(region_ids, hospital_id, deploy_region) {
  var is_in = false
  if (hospital_id == null || region_ids == undefined) {
    return true
  }
  var region = ''
  if (isNaN(hospital_id)) {
    region = deploy_region[deploy_region.length - 1]
  } else {
    Data.hospital_list.forEach(item => {
      if (hospital_id == item.id) {
        region = item.region_id.toString()
        return
      }
    })
  }
  if (region == '' || region == '0') {
    return true
  }
  region_ids?.forEach(item => {
    var prefix = ''
    let region_bit_arr = item.split("")
    if (region_bit_arr.length == 6) {
      if (!(region_bit_arr[4] == 0 && region_bit_arr[5] == 0)) {
        prefix = item
      } else if (region_bit_arr[4] == 0 && region_bit_arr[5] == 0 && (!(region_bit_arr[2] == 0 && region_bit_arr[3] == 0))) {
        prefix = region_bit_arr[0] + region_bit_arr[1] + region_bit_arr[2] + region_bit_arr[3]
      } else {
        prefix = region_bit_arr[0] + region_bit_arr[1]
      }
    }
    if (region.startsWith(prefix)) {
      is_in = true
      return
    }
  })
  return is_in
}

//判断设备部署医院是否在用户区域医院列表
function checkDeviceHospitalIn(hospital_ids) {
  var is_in = false
  Data.all_hospital_list?.forEach(item => {
    hospital_ids?.forEach(item1 => {
      if (item.id == item1 || item1 == undefined || item1 == '') {
        is_in = true
        return
      }
    })
  })
  return is_in
}

//判断订单是否有设备部署医院在用户区域医院列表
function checkOrderHospitalIn(order_info) {
  var is_in = true
  order_info.devices?.forEach(item => {
    if (!checkDeviceHospitalIn([item.hospital_id])) {
      is_in = false
      return
    }
  })
  return is_in
}

//更新订单收货信息
async function updateReceiverInfo(order) {
  if (order.receiver_region_arr.length >= 0) {
    order.receiver_region = order.receiver_region_arr[order.receiver_region_arr.length - 1]
  }
  let {code, msg} = await orderDeviceUpdateApi({
    order_id: order.id,
    receiver: order.receiver,
    receiver_phone: order.receiver_phone,
    receiver_region: order.receiver_region,
    receiver_address: order.receiver_address
  });
  if (code !== 0) {
    message.error(msg)
    return
  }
}

//更新默认医院信息
async function updateDefaultHospitalInfo(order) {
  let {code, msg} = await orderDeviceUpdateApi({
    order_id: order.id,
    is_save_hospital: 1,
    hospital_id: order.hospital_id,
    contact: order.contact,
    contact_phone: order.contact_phone,
  });
  if(code=='0'){
    message.success('更新成功')
  }
  if (code !== 0) {
    message.error(msg)
    return
  }
}

//订单详情
async function orderDetails(order) {
  Data.order_details.order_number = order.order_number
  Data.order_details.device_status = order.device_status
  Data.order_details.serial_number = order.serial_number
  Data.order_details.hospital_id = order.hospital_id
  Data.order_details.set_meal = order.set_meal
  Data.order_details.use_type = order.use_type
  Data.order_details.user_id = 0
  if (!Auth('order/list')) {
    Data.order_details.user_id = Data.user_info.id
  }
  Data.hospital_list = JSON.parse(JSON.stringify(Data.all_hospital_list))
  Data.user_hospital_list = JSON.parse(JSON.stringify(Data.all_hospital_list))
  let {code, data} = await orderDetailsApi(Data.order_details);
  if (code === 0) {
    Data.order_info = data
    Data.status_from.order_number = Data.order_info.order_number
    Data.revoke_from.order_number = Data.order_info.order_number
    Data.deploy_from.order_number = Data.order_info.order_number
    Data.status_from_finance.order_number = Data.order_info.order_number
    Data.status_from_finance.contract_number = Data.order_info.contract_number
    if (Data.order_info.express_type == 0) {
      Data.order_info.express_type_ = true
    }
    Data.dialogVisible_OrderDetails = true
    if (Data.order_info.status == 0 || Data.order_info.status == 3 || Data.order_info.status == 7) {
      Data.order_info.step = 1
      Data.status_from.status = 2
    } else if (Data.order_info.status == 2 || Data.order_info.status == 6 || Data.order_info.status == 8) {
      Data.order_info.step = 2
      Data.status_from.status = 5
    } else if (Data.order_info.status == 5) {
      Data.order_info.step = 3
    } else if (Data.order_info.status == 9 || Data.order_info.status == 1) {
      Data.order_info.step = 4
    }
    if (Data.order_info.receiver_region != '') {
      Data.order_info.receiver_region_arr = getDeplyRegions(Data.order_info.receiver_region)
    }
    if (Data.order_info?.hospital_id) {
      // Data.order_info.hospital_region_id
      Data.order_info.hospital_region_id = getDeplyRegions(Data.order_info?.hospital?.region_id)
      Data.order_info.hospital_region_name = getRegionNameById(Data.order_info.hospital_region_id)
      Data.order_info.hospital_name = Data.order_info?.hospital?.name
      // await changeOrderRegionHandle()
      var is_exist = false
      Data.user_hospital_list?.forEach(item => {
        if (item.id == Data.order_info.hospital_id) {
          is_exist = true
        }
      })
      if (!is_exist && Data.order_info?.hospital) {
        Data.user_hospital_list.push(Data.order_info?.hospital)
      }

    } else {
      Data.order_info.hospital_id = ""
    }
    Data.data_from.device_index = '0'
    Data.data_from.price = []
    Data.data_from.price_ref = []
    Data.order_info.devices.forEach((item, index) => {
      //格式化设备金额，除以100
      if (Number(item.price) > 0) {
        Data.order_info.devices[index].price = (Number(item.price) / 100).toString()
        Data.data_from.price.push(Data.order_info.devices[index].price)
      }
    })
    Data.order_info.devices.forEach((item, index) => {
      Data.order_info.devices[index].deploy_region = getDeplyRegions(Data.order_info.devices[index].region_id)
      Data.order_info.devices[index].hospital_id = Data.order_info.devices[index].hospital_id == 0 ? '' : Data.order_info.devices[index].hospital_id
      Data.order_info.devices[index].hospital_list = JSON.parse(JSON.stringify(Data.all_hospital_list))
      if (Data.order_info.devices[index].hospital_id > 0) {
        //判断医院列表中是否已有该医院
        var exist = false
        Data.order_info.devices[index].hospital_list.forEach(item1 => {
          if (item1.id == Data.order_info.devices[index].hospital_id) {
            Data.order_info.devices[index].deploy_region = getDeplyRegions(item1.region_id)
            exist = true
          }
        })
        if (!exist) {
          Data.order_info.devices[index].hospital_list?.push({
            name: Data.order_info.devices[index].hospital_name,
            id: Data.order_info.devices[index].hospital_id,
            region_name: Data.order_info.devices[index].region_name,
            region_id: Data.order_info.devices[index].region_id,
          })
        }
      }
    })

    if (Data.order_info.status == 5) {
      ukeyListApi({page_size: 1000}).then(ret => {
        Data.ukey_list = ret.data.list
      })
    }
  }
}

//查询草稿
async function orderDraft() {
  Data.order_details.device_status = '99'
  Data.order_details.user_id = Data.user_info.id
  let {code, data} = await orderDetailsApi(Data.order_details)
  if (code === 0) {
    if (data?.order_number == "") {
      Data.data_from.region_ids = data?.region_ids
      return
    }
    Data.data_from = data
    Data.data_from.region_ids = data?.region_ids
    if (Data.data_from.hospital_id) {
      Data.data_from.hospital_region_id = getDeplyRegions(Data.data_from?.hospital?.region_id)
      // await changeUserRegionHandle()
      var is_exist = false
      Data.user_hospital_list?.forEach(item => {
        if (item.id == Data.data_from.hospital_id) {
          is_exist = true
        }
      })
      if (!is_exist && Data.data_from?.hospital) {
        Data.user_hospital_list.push(Data.data_from?.hospital)
      }
    } else {
      Data.data_from.hospital_id = ''
    }
    //是否存在草稿代理商
    if (Data.data_from.agent_id) {
      var is_exist = false
      Data.agent_list.forEach(item => {
        if (item.group == Data.data_from.agent_id) {
          is_exist = true
          return
        }
      })
      if (!is_exist) {
        Data.agent_list.push({group: Data.data_from.agent_id, name: Data.data_from.agent?.name})
      }
    }
    if (Data.data_from.receiver_region != '') {
      Data.data_from.receiver_region_arr = getDeplyRegions(Data.data_from.receiver_region)
    }
    if (Data.data_from.devices == null) {
      Data.data_from.devices = []
    }
    Data.data_from.price = []
    Data.data_from.price_ref = []
    Data.data_from.device_index = '0'
    Data.data_from.devices.forEach((item, index) => {
      //格式化设备金额，除以100
      if (Number(item.price) > 0) {
        Data.data_from.devices[index].price = (Number(item.price) / 100).toString()
        Data.data_from.price.push(Data.data_from.devices[index].price)
      } else {
        Data.data_from.devices[index].price = ""
      }
      Data.data_from.price_ref.push(ref<FormInstance>())
      Data.data_from.devices[index].deploy_region = getDeplyRegions(Data.data_from.devices[index].region_id)
      Data.data_from.devices[index].hospital_id = Data.data_from.devices[index].hospital_id == 0 ? '' : Data.data_from.devices[index].hospital_id
      Data.data_from.devices[index].hospital_list = JSON.parse(JSON.stringify(Data.all_hospital_list))
      if (Data.data_from.devices[index].hospital_id > 0) {
        //判断医院列表中是否已有该医院
        var exist = false
        Data.data_from.devices[index].hospital_list?.forEach(item1 => {
          if (item1.id == Data.data_from.devices[index].hospital_id) {
            exist = true
            return
          }
        })
        if (!exist) {
          Data.hospital_list.push({
            name: Data.data_from.devices[index].hospital_name,
            id: Data.data_from.devices[index].hospital_id,
            region_name: Data.data_from.devices[index].region_name,
            region_id: Data.data_from.devices[index].region_id,
          })
          Data.data_from.devices[index].hospital_list?.push({
            name: Data.data_from.devices[index].hospital_name,
            id: Data.data_from.devices[index].hospital_id,
            region_name: Data.data_from.devices[index].region_name,
            region_id: Data.data_from.devices[index].region_id,
          })
        }
      }
    })
  }
}

//添加设备
function addDeviceHandel(index, action) {
  if (typeof (action) != "undefined" && action) {
    Data.curr_device_index = index

    if (action == 'upgrade') {
      Data.curr_set_meal = Data.original_devices[index].set_meal
      Data.curr_device_upgrade = true
      Data.device.set_meal = Data.curr_set_meal
      if (OPTIONS.old_version.indexOf(Data.curr_set_meal) !== -1) {
        Data.device.set_meal = OPTIONS.old_version_compare[Data.curr_set_meal]
      }
    } else {
      Data.curr_device_upgrade = false
    }

    if (action == "remove") {
      Data.data_from.device_index = (Data.data_from.devices.length - 2).toString()
      if (Data.data_from.devices.length - 2 < 0) {
        Data.data_from.device_index = '0'
      }
      Data.data_from.devices.splice(index, 1)
      Data.data_from.price.splice(index, 1)
      return
    }
  }

  let def_meal = Data.meal_def_options[Data.device.set_meal]

  if (Data.device.set_meal > 18 && Data.device.set_meal < 1000) {
    Data.product_options['new'].forEach((item) => {
      item['month_count'] = 0
      if (def_meal.indexOf(item.product_id) !== -1) {
        item['month_count'] = -1
      }
      if (Array.isArray(item.children)) {
        item.children.forEach(child => {
          if (child.product_id == 1000002 || child.product_id == 12002) {
            child.month_count = 0
          } else {
            child.month_count = item['month_count']
          }
        });
      }
    })
  } else if (Data.device.set_meal == 18 || Data.device.set_meal == 1000){
    Data.product_options['new'].forEach((item) => {
      item['month_count'] = 0
      if (def_meal.indexOf(item.product_id) !== -1) {
        item['month_count'] = 6
      }
      if (Array.isArray(item.children)) {
        item.children.forEach(child => {
          if (child.product_id == 1000002 || child.product_id == 12002) {
            child.month_count = 0
          } else {
            child.month_count = item['month_count']
          }
        });
      }
    })
    Data.product_options['old'].forEach((item) => {
      item['month_count'] = 0
      if (def_meal.indexOf(item.product_id) !== -1) {
        item['month_count'] = 6
      }
      if (Array.isArray(item.children)) {
        item.children.forEach(child => {
          if (child.product_id == 1000002 || child.product_id == 12002) {
            child.month_count = 0
          } else {
            child.month_count = item['month_count']
          }
        });
      }
    })
  }else {
    Data.product_options['old'].forEach((item, index) => {
      item['month_count'] = 0
      if (def_meal.indexOf(item.product_id) !== -1) {
        item['month_count'] = -1
      }
      if (Array.isArray(item.children)) {
        item.children.forEach(child => {

          if (child.product_id == 1000002 || child.product_id == 12002) {
            child.month_count = 0
          } else {
            child.month_count = item['month_count']
          }

        });
      }
    })
  }
  Data.device.month = 6
  Data.dialogVisible_Meal = true
}

//判断套餐是否包含此产品
function checkProductIsNo(set_meal, product_id) {
  if (set_meal == 1000 || set_meal == 18) {
    return false
  }
  let def_meal = Data.meal_def_options[set_meal]
  var flag = false
  if (set_meal >= 18 && set_meal < 1000) {
    Data.product_options['new'].forEach((item) => {
      if (item.product_id == product_id) {
        if (def_meal.indexOf(item.product_id) !== -1) {
          return
        }
        flag = true
        return
      }
    })
  } else {
    Data.product_options['old'].forEach((item, index) => {
      if (item.product_id == product_id) {
        if (def_meal.indexOf(item.product_id) !== -1) {
          return
        }
        flag = true
        return
      }
    })
  }
  return flag
}

//修改产品有效期
function changeDefMonthHandel() {
  if (Data.device.set_meal >= 18 && Data.device.set_meal < 1000) {
    Data.product_options['new'].forEach((item, index) => {
      item['month_count'] = Data.device.month
      item.children.forEach(item2 => {
        item2['month_count'] = Data.device.month
      })
    })
  } else {
    Data.product_options['old'].forEach((item, index) => {
      item['month_count'] = Data.device.month
      item.children.forEach(item2 => {
        item2['month_count'] = Data.device.month
      })
    })
  }
}

//确认选择设备套餐
function submitMealForm() {
  let product_options = Data.product_options['old']
  if (Data.device.set_meal >= 18 && Data.device.set_meal < 1000) {
    product_options = Data.product_options['new']
  }
  let valid = false
  Data.device.is_append = 0
  var is_append = 0
  var is_comp = 0
  for (let i = 0; i < product_options.length; i++) {
    //仅判断第一层产品
    if (product_options[i].month_count !== 0) {
      valid = true
    }
    //判断是否有额外追加产品，或赠品
    if (checkProductIsNo(Data.device.set_meal, product_options[i].product_id) && product_options[i].month_count !== 0) {
      if (product_options[i].month_count == -1) {
        is_comp = 1
      } else {
        is_append = 1
      }
    }
  }
  //同时拥有赠品或追加为3，仅有增加为2，仅有追加为1
  if (is_comp && is_append) {
    Data.device.is_append = 3
  } else if (is_comp) {
    Data.device.is_append = 2
  } else if (is_append) {
    Data.device.is_append = 1
  }
  if (!valid) {
    message.error("请至少选择一个有效期产品")
    return
  }
  Data.dialogVisible_Meal = false

  //判断是否是创建升级订单
  if (Data.dialogVisible_CreateOrderUpgrade) {
    //检查升级前后套餐是否一样？套餐一样，额外添加产品是否一样
    var is_upgrade = false
    if (Data.data_from.devices[Data.curr_device_index].set_meal != Data.device.set_meal) {
      is_upgrade = true
    } else {
      //均为正式版，且没有额外添加产品，则不升级
      if (!(Data.device.set_meal != 1000 && Data.device.set_meal != 18 &&
        Data.data_from.devices[Data.curr_device_index].is_append == 0 && Data.device.is_append == 0)) {
        is_upgrade = true
      }
    }
    if (!is_upgrade) {
      Data.data_from.devices[Data.curr_device_index].set_meal_upgrade = 0
      Data.data_from.devices[Data.curr_device_index].is_append_upgrade = 0
      return
    }
    Data.data_from.devices[Data.curr_device_index].set_meal_upgrade = Data.device.set_meal
    Data.data_from.devices[Data.curr_device_index].is_append_upgrade = Data.device.is_append
    Data.data_from.devices[Data.curr_device_index].products = JSON.parse(JSON.stringify(product_options))
    return
  }

  if (Data.curr_device_upgrade) {
    Data.data_from.devices[Data.curr_device_index].set_meal = Data.device.set_meal
    Data.data_from.devices[Data.curr_device_index].is_append = Data.device.is_append
    Data.data_from.devices[Data.curr_device_index].products = JSON.parse(JSON.stringify(product_options))
  } else {
    Data.device.products = product_options
    Data.device.hospital_list = JSON.parse(JSON.stringify(Data.all_hospital_list))
    //订单选择默认医院，设备同步部署医院
    if (Data.data_from.hospital_id) {
      Data.device.hospital_id = Data.data_from.hospital_id
      Data.device.deploy_region = Data.data_from.hospital_region_id
      Data.device.deploy_units = Data.data_from.hospital_region_id
      Data.device.deploy_units = Data.data_from?.hospital?.address
      var is_exist = false
      Data.device.hospital_list?.forEach(item => {
        if (item?.id == Data.data_from.hospital_id) {
          is_exist = true
        }
      })
      if (!is_exist && Data.data_from?.hospital) {
        Data.device.hospital_list?.push(Data.data_from?.hospital)
      }
    }
    Data.data_from.devices.push(JSON.parse(JSON.stringify(Data.device)))
    Data.data_from.price.push("")
  }

  Data.data_from.device_index = (Data.data_from.devices.length - 1).toString()
}

//取消选择设备套餐
function cancelMealForm() {
  clearCache()
  Data.dialogVisible_Meal = false;
  selectedId.value = null;
  selectedMeal.value = null;
}

//提交订单审核
const submitStatusForm = async (formEl: FormInstance | undefined, type: string) => {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (type == 'deploy') {
        if (Data.deploy_from.express_type_) {
          Data.deploy_from.express_type = 0
        }
        orderDeloyApi(Data.deploy_from).then(ret => {
          if (ret.code === 0) {
            message.success("U-Key绑定完成")
            Data.dialogVisible_OrderDetails = false
            onSearch()
          }
        })
      } else if (type == 'revoke') {
        orderRevokeApi(Data.revoke_from).then(ret => {
          if (ret.code === 0) {
            message.success("撤销成功")
            Data.dialogVisible_OrderDetails = false
            onSearch()
          }
        })
      } else {
        if (Data.status_from.status == 5 || Data.status_from.status == 6) {
          orderManagerReviewApi(Data.status_from).then(ret => {
            if (ret.code === 0) {
              message.success("审核完成")
              Data.dialogVisible_OrderDetails = false
              onSearch()
            }
          })
        } else if (Data.status_from_finance.status == 2 || Data.status_from_finance.status == 3) { //财务待复核
          if (Data.status_from_finance.status == 2) {
            // if(Data.status_from_finance.contract_number == '' || Data.status_from_finance.last_date == '') {
            //   message.error("请输入正确的参数")
            //   return
            // }
            if (Data.status_from_finance.contract_number.length < 15) {
              message.error("合同号长度必须为15位")
              return
            }
            //财务复核时，必须填写设备金额
            var err_msg = ''
            Data.order_info.devices.forEach((item, index) => {
              if (Number(item.price) <= 0) {
                err_msg = '设备金额必填'
                return
              }
            })
            if (err_msg != '') {
              message.error(err_msg)
              return
            }
            Data.order_info.devices.forEach((item, index) => {
              //后端金额存储用整数型，乘以100
              if (Number(item.price) > 0) {
                var price = item.price.toString().match(/\d+\.?\d{0,2}/)[0]
                Data.order_info.devices[index].price = (Number(price) * 100).toFixed()
              }
            })
          }
          //财务复核时，需要填写金额
          Data.status_from_finance.devices = Data.order_info.devices
          orderReviewApi(Data.status_from_finance).then(ret => {
            if (ret.code === 0) {
              message.success("审核完成")
              Data.dialogVisible_OrderDetails = false
              onSearch()
            } else {
              //报错，重新格式金额
              Data.order_info.devices.forEach((item, index) => {
                if (Number(item.price) > 0) {
                  Data.order_info.devices[index].price = (Number(item.price) / 100).toString()
                }
              })
            }
          })
        } else {
          orderReviewApi(Data.status_from).then(ret => {
            if (ret.code === 0) {
              message.success("审核完成")
              Data.dialogVisible_OrderDetails = false
              onSearch()
            }
          })
        }
      }
    }
  })
}

//财务复核时，仅保存合同号
async function saveFinanceInfo(id, contract_number) {
  let {code, msg} = await orderDeviceUpdateApi({
    order_id: id,
    contract_number: contract_number
  });
  if (code !== 0) {
    message.error(msg)
    return
  }
  Data.order_info.contract_number = contract_number
}

function cancelStatusForm(formEl) {
  Data.status_from = {
    order_number: "",
    status: 0,
    remark: "",
  }
  Data.revoke_from = {
    order_number: "",
    remark: "",
  }
  Data.deploy_from = {
    order_number: "",
    ukey_code: null,
    express_type_: true,
    express_type: 0,
    express_no: "",
    remark: ""
  }
  Data.status_from_finance = {
    order_number: "",
    contract_number: "",
    last_date: "",
    status: 2,
    remark: "",
    devices: [],
  }
  if (!formEl) return
  Data.dialogVisible_OrderDetails = false
  formEl.resetFields()
}

let Isfeedback = ref(true);
let IsDoctor = ref(true);

//获取设备详情
function handleDeviceDeatil(row, index, order) {
  Data.order_details.order_number = row.order_number
  Data.order_details.device_status = order.device_status
  Data.order_details.serial_number = row.serial_number
  Data.order_details.hospital_id = order.hospital_id
  Data.order_details.set_meal = order.set_meal
  Data.order_details.use_type = order.use_type
  Data.order_details.user_id = 0
  if (!Auth('order/list')) {
    Data.order_details.user_id = Data.user_info.id
  }
  orderDeviceDetailsApi(Data.order_details).then(ret => {
    Data.dialogVisible_DeviceDetails = true
    if (ret.data.devices?.length > 0) {
      ret.data.devices[0].assign_user = ret.data.devices[0].assign_user == '0' ? null : ret.data.devices[0].assign_user;

      if(!ret.data.devices[0].assign_user){
        IsDoctor.value=false;
      }
      _self.assUser=ret.data.devices[0].assign_user;
      if(ret.data.devices[0].feedback){
        _self.isfankui=true;
      }
      if (!ret.data.devices[0].feedback) {
        //  Isfeedback.value = true;
       // if(ret.data.devices[0].assign_user==_self.Useid){
       //   Isfeedback.value = false;
       // }
       ret.data.devices[0].feedback = {};
        ret.data.devices[0].feedback['status'] = 0;
        ret.data.devices[0].feedback['remark'] = '';
      };
      Data.device_info = ret.data.devices[0];

      Data.device_info.is_upgrade = ret.data.is_upgrade
      Data.device_info.region_ids = ret.data.region_ids
      Data.device_info.order_status = order.status
      Data.device_info.deploy_region = getDeplyRegions(Data.device_info.region_id)
      Data.device_info.hospital_id = Data.device_info.hospital_id == 0 ? '' : Data.device_info.hospital_id
      Data.device_info.hospital_list = JSON.parse(JSON.stringify(Data.all_hospital_list))
      if (Data.device_info.hospital_id > 0) {
        //判断医院列表中是否已有该医院
        var exist = false
        Data.device_info.hospital_list.forEach(item1 => {
          if (item1.id == Data.device_info.hospital_id) {
            exist = true
          }
        })
        if (!exist) {
          Data.device_info.hospital_list.push({
            name: Data.device_info.hospital_name,
            id: Data.device_info.hospital_id,
            region_name: Data.device_info.region_name,
            region_id: Data.device_info.region_id
          })
        }
      }
      //格式化设备金额，除以100
      if (Number(Data.device_info.price) > 0) {
        Data.device_info.price = (Number(Data.device_info.price) / 100).toString()
      }
    }
  })
}

//根据区域id获取区域数组
function getDeplyRegions(region_id) {
  region_id += ''
  let region_ids = []
  let region_bit_arr = region_id.split("")
  if (region_bit_arr.length == 6) {
    if (!(region_bit_arr[4] == 0 && region_bit_arr[5] == 0)) {
      region_ids = [region_bit_arr[0] + region_bit_arr[1] + "0000", region_bit_arr[0] + region_bit_arr[1] + region_bit_arr[2] + region_bit_arr[3] + '00', region_id]
    } else if (region_bit_arr[4] == 0 && region_bit_arr[5] == 0 && (!(region_bit_arr[2] == 0 && region_bit_arr[3] == 0))) {
      region_ids = [region_bit_arr[0] + region_bit_arr[1] + "0000", region_id]
    } else {
      region_ids = [region_id]
    }
  }
  return region_ids
}

//根据区域id获取区域名称
function getRegionNameById(region_ids) {
  var region_names = []
  region_ids?.forEach((item, index) => {
    Data.user_region_tree?.forEach(item1 => {
      var index1 = 0
      if (index == index1 && item == item1.value) {
        region_names.push(item1.label)
        return
      }
      item1?.children?.forEach(item2 => {
        index1++
        if (index == index1 && item == item2.value) {
          region_names.push(item2.label)
          return
        }
        item2?.children?.forEach(item3 => {
          index1++
          if (index == index1 && item == item3.value) {
            region_names.push(item3.label)
            return
          }
        })
      })
    })
  })
  return region_names.join('/')
}

//订单升级
async function handleUpgrade(row) {
  let {data} = await orderDetailsApi({order_number: row.order_number, device_status: Data.search_from.device_status});
  Data.data_from['order_number'] = data.order_number
  Data.data_from.devices = data.devices
  Data.data_from.sale_name = Data.user_info.username
  Data.data_from.sale_phone = Data.user_info.phone
  Data.dialogVisible_OrderUpgrade = true
  Data.original_devices = JSON.parse(JSON.stringify(data.devices))
}

async function copy(text: string) {
  const {toClipboard} = useClipboard()
  try {
    await toClipboard(text)
    message.success("复制成功")
  } catch (e) {
    message.error("复制失败")
  }
}

//更新设备用途
async function handleUpdateUseType(item) {
  if (item != null) {
    let {code} = await orderDeviceUpdateApi({device_id: item.id, use_type: Data.device_use_type_update});
    if (code === 0) {
      item.use_type = Data.device_use_type_update
      Data.device_use_type_update = ""
      Data.dialogVisible_UpdateUseType = false
      // popoverOrderRef.value.hide();
    }
    return
  }
  let {code} = await orderDeviceUpdateApi({device_id: Data.device_info.id, use_type: Data.device_use_type_update});
  if (code === 0) {
    Data.device_info.use_type = Data.device_use_type_update
    Data.device_use_type_update = ""
    // Data.dialogVisible_UpdateUseType = false
    popoverRef.value.hide();
  }
}

function cancelUpdateUseType(item) {
  Data.device_use_type_update = ""
  // Data.dialogVisible_UpdateUseType = false
  if (item != null) {
    // popoverOrderRef.value.hide();
    Data.dialogVisible_UpdateUseType = false
    return
  }
  popoverRef.value.hide();
}

//判断设备套餐产品，追加或赠品
function formatDeviceTabLabel(device) {
  var lable = Data.meal_options[device.set_meal]
  if (device.is_append == '1') {
    lable += ' 加'
  } else if (device.is_append == '2') {
    lable += ' 赠'
  } else if (device.is_append == '3') {
    lable += ' 加 赠'
  }
  return lable
}

function handleTransfer(order) {
  Data.visible_OrderTransfer = true
  var sale_id = order.operator_id
  if (order.sale_id > 0) {
    sale_id = order.sale_id
  }
  hadBindUser(sale_id)
}

async function hadBindUser(sale_id) {
  let {code, data} = await agentHadBindUserApi({sale_id: sale_id});
  if (code === 0) {
    Data.order_sale.sale_list = data
    Data.order_sale.sale_list?.forEach((item, index) => {
      if (item.id == sale_id) {
        Data.order_sale.sale_list.splice(index, 1)
      }
    })
  }
}

//提交订单转移
function submitTransfer(order_info) {
  if (Data.order_sale.sale_id == 0 || Data.order_sale.sale_id == '') {
    message.error("请选择正确的销售")
    return
  }
  orderTransfer(order_info)
  var sale_name = ''
  var phone = ""
  Data.order_sale.sale_list.forEach(item => {
    if (item.id == Data.order_sale.sale_id) {
      sale_name = item.username
      phone = item.phone
      return
    }
  })
  if (sale_name) {
    Data.order_info.sale_name = sale_name
    Data.order_info.sale_phone = phone
  }
  Data.data_list.forEach((item, index) => {
    if (item.id == order_info.id && sale_name) {
      Data.data_list[index].sale_name = sale_name
      Data.data_list[index].is_transfer = 1
    }
  })
  Data.visible_OrderTransfer = false
}

async function orderTransfer(order_info) {
  let {code, msg} = await orderTransferApi({
    order_id: order_info.id,
    sale_id: Data.order_sale.sale_id
  });
  if (code !== 0) {
    message.error(msg)
    return
  }
}

function handleDevicePrice(price, index) {
  if (index >= Data.data_from.price?.length) {
    Data.data_from.price.push(price)
  } else {
    Data.data_from.price[index] = price
  }
}

//提交创建医院
const submitHospitalForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      let {code, msg, data} = await hospitalOrderAddApi(Data.hospital_data_from);
      if (code !== 0) {
        return
      }
      message.success(msg)
      Data.hospital_data_from.id = data.id
      Data.dialogVisible_CreateHospital = false

      var hospital = {
        id: Data.hospital_data_from.id,
        name: Data.hospital_data_from.name,
        region_id: Data.hospital_data_from.region_id[2],
        liaison: Data.hospital_data_from.liaison,
        phone: Data.hospital_data_from.phone,
        address: Data.hospital_data_from.address,
      }

      //判断医院区域是否在用户区域
      if (checkHospitalRegion(Data.data_from?.region_ids, Data.hospital_data_from.region_id[2])) {
        getAllHospitalList()
      }
      var is_exist = false
      Data.hospital_list?.forEach(item1 => {
        if (Data.hospital_data_from.id == item1.id) {
          is_exist = true
          return
        }
      })
      if (!is_exist) {
        Data.hospital_list?.unshift(hospital)
      }

      if (Data.hospital_data_from.action == 'add') {//创建订单时，新建设备部署医院
        Data.data_from.devices[Data.hospital_data_from.index].hospital_id = Data.hospital_data_from.id
        Data.data_from.devices[Data.hospital_data_from.index].deploy_region = Data.hospital_data_from.region_id
        Data.data_from.devices[Data.hospital_data_from.index].deploy_units = Data.hospital_data_from.address
        // await changeRegionHandel(Data.data_from.devices[Data.hospital_data_from.index]?.deploy_region, Data.hospital_data_from.index, Data.data_from.devices[Data.hospital_data_from.index])
        var is_exist = false
        Data.data_from.devices[Data.hospital_data_from.index]?.hospital_list?.forEach(item1 => {
          if (Data.hospital_data_from.id == item1.id) {
            is_exist = true
            return
          }
        })
        if (!is_exist) {
          Data.data_from.devices[Data.hospital_data_from.index]?.hospital_list?.unshift(hospital)
        }
      } else if (Data.hospital_data_from.action == 'order') {//订单详情时，新建医院
        Data.order_info.devices[Data.hospital_data_from.index].hospital_id = Data.hospital_data_from.id
        Data.order_info.devices[Data.hospital_data_from.index].deploy_region = Data.hospital_data_from.region_id
        Data.order_info.devices[Data.hospital_data_from.index].deploy_units = Data.hospital_data_from.address
        // await changeDetailsRegionHandel(Data.order_info.devices[Data.hospital_data_from.index]?.deploy_region, Data.hospital_data_from.index, Data.order_info.devices[Data.hospital_data_from.index])
        var is_exist = false
        Data.order_info.devices[Data.hospital_data_from.index]?.hospital_list?.forEach(item1 => {
          if (Data.hospital_data_from.id == item1.id) {
            is_exist = true
            return
          }
        })
        if (!is_exist) {
          Data.order_info.devices[Data.hospital_data_from.index]?.hospital_list?.unshift(hospital)
        }
      } else if (Data.hospital_data_from.action == 'device') {//设备详情时，新建医院
        Data.device_info.hospital_id = Data.hospital_data_from.id
        Data.device_info.deploy_region = Data.hospital_data_from.region_id
        Data.device_info.deploy_units = Data.hospital_data_from.address
        // await changeDeviceDetailsRegionHandel(Data.device_info?.deploy_region, Data.device_info)
        var is_exist = false
        Data.device_info.hospital_list?.forEach(item => {
          if (Data.hospital_data_from.id == item.id) {
            is_exist = true
            return
          }
        })
        if (!is_exist) {
          Data.device_info.hospital_list?.unshift(hospital)
        }
      } else if (Data.hospital_data_from.action == 'add-default') {//新增订单，新建默认医院
        Data.data_from.hospital_id = Data.hospital_data_from.id
        Data.data_from.hospital_region_id = Data.hospital_data_from.region_id
        Data.data_from.contact = Data.hospital_data_from.liaison
        Data.data_from.contact_phone = Data.hospital_data_from.phone
        // await changeUserRegionHandle()
        var is_exist = false
        Data.user_hospital_list?.forEach(item => {
          if (Data.hospital_data_from.id == item.id) {
            is_exist = true
            return
          }
        })
        if (!is_exist) {
          Data.user_hospital_list?.unshift(hospital)
        }
        Data.data_from.devices?.forEach((item1, index1) => {
          if (!item1?.hospital_id || (item1?.hospital_id && item1?.hospital_id == Data.hospital_data_from.id)) {
            Data.data_from.devices[index1].hospital_id = Data.hospital_data_from.id
            Data.data_from.devices[index1].deploy_units = Data.hospital_data_from.address
            Data.data_from.devices[index1].deploy_region = Data.hospital_data_from.region_id
            is_exist = false
            Data.data_from.devices[index1].hospital_list?.forEach(item2 => {
              if (Data.hospital_data_from.id == item2.id) {
                is_exist = true
                return
              }
            })
            if (!is_exist) {
              Data.data_from.devices[index1].hospital_list?.unshift(hospital)
            }
          }
        })
      } else if (Data.hospital_data_from.action == 'details-default') {//订单详情，新建默认医院
        Data.order_info.hospital_id = Data.hospital_data_from.id
        Data.order_info.hospital_region_id = Data.hospital_data_from.region_id
        Data.order_info.contact = Data.hospital_data_from.liaison
        Data.order_info.contact_phone = Data.hospital_data_from.phone
        // await changeOrderRegionHandle()
        var is_exist = false
        Data.user_hospital_list?.forEach(item => {
          if (Data.hospital_data_from.id == item.id) {
            is_exist = true
            return
          }
        })
        if (!is_exist) {
          Data.user_hospital_list?.unshift(hospital)
        }
        Data.order_info.devices?.forEach((item1, index1) => {
          if (!item1?.hospital_id || (item1?.hospital_id && item1?.hospital_id == Data.hospital_data_from.id)) {
            Data.order_info.devices[index1].hospital_id = Data.hospital_data_from.id
            Data.order_info.devices[index1].deploy_units = Data.hospital_data_from.address
            Data.order_info.devices[index1].deploy_region = Data.hospital_data_from.region_id
            is_exist = false
            Data.order_info.devices[index1].hospital_list?.forEach(item2 => {
              if (Data.hospital_data_from.id == item2.id) {
                is_exist = true
                return
              }
            })
            if (!is_exist) {
              Data.order_info.devices[index1].hospital_list?.unshift(hospital)
            }
          }
        })
      }
    } else {
      message.error("有表单必填项未完成")
    }
  })
}

function checkHospitalRegion(region_ids, region) {
  var is_in = false
  region_ids?.forEach(item => {
    var prefix = ''
    let region_bit_arr = item.split("")
    if (region_bit_arr.length == 6) {
      if (!(region_bit_arr[4] == 0 && region_bit_arr[5] == 0)) {
        prefix = item
      } else if (region_bit_arr[4] == 0 && region_bit_arr[5] == 0 && (!(region_bit_arr[2] == 0 && region_bit_arr[3] == 0))) {
        prefix = region_bit_arr[0] + region_bit_arr[1] + region_bit_arr[2] + region_bit_arr[3]
      } else {
        prefix = region_bit_arr[0] + region_bit_arr[1]
      }
    }
    if (region.startsWith(prefix)) {
      is_in = true
      return
    }
  })
  return is_in
}

const cancelHospitalForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  Data.dialogVisible_CreateHospital = false
  formEl.resetFields()
}

//点击新建医院
function handleCreateHospital(index, action) {
  Data.hospital_data_from = {
    id: 0,
    name: "",
    license_code: "",
    address: "",
    liaison: "",
    phone: "",
    region_id: [],
    index: '',
    action: '',
  }
  if (action == "add") {
    Data.data_from.devices[index].hospital_id = ''
  } else if (action == "order") {
    Data.order_info.devices[index].hospital_id = ''
  } else if (action == "device") {
    Data.device_info.hospital_id = ''
  } else if (action == "add-default") {
    Data.data_from.hospital_id = ""
  } else if (action == "details-default") {
    Data.order_info.hospital_id = ''
  }
  Data.hospital_data_from.action = action
  Data.hospital_data_from.index = index
  Data.dialogVisible_CreateHospital = true
}

//点击新增代理商
function handleCreateAgent() {
  Data.agent_data_from = {
    group: "",
    name: "",
    type: '1',
    time: null,
    start_time: "",
    end_time: "",
    license_code: "",
    address: "",
    liaison: "",
    phone: "",
    email: "",
    region_id: [],
    hospital_id: [],
    is_all: '0',
    length: 0,
    create_at: "",
  }
  Data.agent_data_from.region_id.push("")
  Data.dialogVisible_CreateAgent = true
}

//新增代理商区域
function handleAddRegion() {
  Data.agent_data_from.region_id.push("")
}

//删除代理商区域
function handleDelRegion(index) {
  Data.agent_data_from.region_id.splice(index, 1)
}

//提交创建代理商
const submitAgentForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      var err_msg = ''
      //判断是否有重复区域或医院
      var dup_region = []
      if (Data.agent_data_from.is_all == '0') {
        Data.agent_data_from.region_id.forEach((item, index) => {
          if (item?.length == 0 || item == null) {
            err_msg = '代理商区域必填'
            return
          }
          if (dup_region.indexOf(item[item.length - 1]) != -1) {
            err_msg = '代理商区域重复'
            return
          }
          dup_region.push(item[item.length - 1])
        })
      }
      if (err_msg) {
        message.error(err_msg)
        return
      }

      if (Data.agent_data_from.time && Data.agent_data_from.time?.length == 2) {
        if (Data.agent_data_from.time[0] == '' || Data.agent_data_from.time[1] == '') {
          message.error('代理商时限必填')
          return
        }
        Data.agent_data_from.start_time = dateFormat(Data.agent_data_from.time[0], 'YYYY-mm-dd')
        Data.agent_data_from.end_time = dateFormat(Data.agent_data_from.time[1], 'YYYY-mm-dd')
      } else {
        message.error('代理商时限必填')
        return
      }

      let {code, msg, data} = await agentOrderAddApi(Data.agent_data_from);
      if (code === 0) {
        message.success(msg)
        await getAgentList(null)
        await getBindRegion();
        Data.data_from.region_id = [];
        Data.data_from.agent_id = data?.group;
        Data.data_from.principal = Data.agent_data_from.liaison;
        Data.data_from.principal_phone = Data.agent_data_from.phone;
        Data.dialogVisible_CreateAgent = false
      }
    } else {
      message.error("有表单必填项未完成")
    }
  })
}

const cancelAgentForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  Data.dialogVisible_CreateAgent = false
  formEl.resetFields()
}

//检查医院名称是否重复
async function checkHospitalName() {
  if (Data.hospital_data_from.name) {
    let {code, data} = await hospitalOrderAddApi({
      name: Data.hospital_data_from.name,
      region_id: Data.hospital_data_from.region_id,
      is_query: 1
    });
    if (code === 0) {
      if (data != null) {
        message.warn("已存在同名医院")
      }
    }
  }
}

//检查代理商名称是否重复
async function checkAgentName() {
  if (Data.agent_data_from.name) {
    let {code, data} = await agentOrderAddApi({name: Data.agent_data_from.name, is_query: 1});
    if (code === 0) {
      if (data != null) {
        message.warn("已存在同名代理商")
      }
    }
  }
}

const activeNames = ref(['1']);

async function addDoctor(obj) {
  if(!obj.assign_user){
    message.warn("请选择指派医生！")
    return
  }
  let data = await setAssignDoctor({
    order_number: obj.order_number,
    serial_number: obj.serial_number,
    user_id: obj.assign_user
  });
  if (data.code == '0') {
    _self.assUser= obj.assign_user
   //  IsDoctor.value=true;
    onSearch()
    message.success("提交成功")
  }
};

async function seteedback(obj) {
  let data = await setFeedbackDoctor({
    order_number: obj.order_number,
    serial_number: obj.serial_number,
    status: obj.feedback.status,
    remark: obj.feedback.remark,
    id: obj?.feedback?.id
  });
  if (data.code == '0') {
    Isfeedback.value = true;
    _self.isfankui=true;
    message.success("提交成功")
  }
}

//修改信息
async function getEditServer(obj) {
  if (obj.agent_id) {
    if(!Data.agent_list.some(item=>item.name===obj.agent.name)){
      Data.agent_list.unshift({
        group: obj.agent.group || obj.agent_id,
        name: obj.agent.name,
        date_status: obj.agent.date_status
      })
    }
  }
  _self.data_from = JSON.parse(JSON.stringify(obj));

  Data.EditDialogModify = true;

}

async function handleEditServer(data) {
  if(!_self.data_from.agent_id){
    message.error("代理商不能为空");
    return;
  }
  if(!_self.data_from.principal){
    message.error("代理商负责人不能为空");
    return;
  }
  if(!_self.data_from.principal_phone){
    message.error("负责人电话不能为空");
    return;
  }

  let result = await setaAminUpdate({
    order_number: data.order_number,//订单
    //serial_number: data.serial_number,
    agent_id: data.agent_id,
    principal: data.principal,
    principal_phone: data.principal_phone,
    //  contract_number:data.contract_number,
    // price: 100,
  });
  if (result.code == '0') {
    message.success("修改成功");
    Data.EditDialogModify = false;
    Data.dialogVisible_OrderDetails = false;
    onSearch()
  }
}


async function Eventconfig(data) {
   if(!data.Dtocontract_number){
     message.error("合同编号不能为空！");
     return
   }
  if (data.Dtocontract_number.length < 15) {
    message.error("合同编号不能小于15位");
    return;
  }
  let result = await setaAminUpdate({
    order_number: data.order_number,//订单
    contract_number: data.Dtocontract_number,
    // price: 100,
  });
  if (result.code == '0') {
    message.success("修改成功");
    data.contract_number = data.Dtocontract_number;
    _self.edisBox = false;
    onSearch()
  }
}

async function priceEditServer(item) {
  if(!item.Newprice){
    message.error("金额不能为空！");
    return
  }
  if(item.Newprice=='0'){
    message.error("金额不能为零！");
    return;
  }
  if(parseFloat(item.Newprice)<0.1){
    message.error("金额不能小于0.1");
    return;
  }
  let result = await setaAminUpdate({
    order_number: item.order_number,//订单
    serial_number: item.serial_number,
    price: item.Newprice*100,
  });
  if (result.code == '0') {
    message.success("修改成功");
    item.price = item.Newprice;
    _self.editprice = false;
    onSearch()
  }

}


const specialProductIds= [
  12002, 1000002, // 中英操作系统
  12004, 1000004,  // 高级病例筛查
  12005, 1000005, // 图文报告
  12107, 1001007, // 病例标签
  12109, 1001009, //  数据导出与打印
  12110, 1001010,//  多胎模式
  12111, 1001011,//  质控训练模式

  12401,1004001 ,// 胎儿医学计算器（实时分析）
  12402, 1004002,// 秒查询（知识图谱）

  12501, 1005001, //  胎儿筛查切面实时引导
  12502, 1005002, //  常规心功能指标
  12503, 1005003, //  血管管径测量
  12504, 1005004, //  胎儿心功能Z-Score

  12601, 1006001, // 拓展心功能指标
  12602, 1006002, // 详细测量数据
  12603, 1006003, // 24节段数据
  12604, 1006004, // 批量胎心分析

  12702, 1007002,//  医师质控
  12703, 1007003,//  科室质控
]



const isSpecialProduct =(productId)=> {
  return specialProductIds.includes(productId);
}

</script>

<template>
  <el-row class="main">
    <el-form ref="formRef" :inline="true" :model="Data.search_from" class="bg-white w-99/100 pl-8 pt-4">
      <el-form-item label="关键词搜索：" prop="keyword">
        <el-input style="width: 200px;" v-model="Data.search_from.keyword" placeholder="创建者、销售、U-Key" clearable/>
      </el-form-item>
      <el-form-item label="订单号：" prop="order_number">
        <el-input style="width: 200px;" v-model="Data.search_from.order_number" placeholder="请输入订单号" clearable/>
      </el-form-item>
      <el-form-item label="合同号：" prop="contract_number">
        <el-input style="width: 200px;" v-model="Data.search_from.contract_number" placeholder="请输入合同号" clearable/>
      </el-form-item>
      <el-form-item label="选择区域:" prop="region_ids">
        <el-cascader :options="Data.region_tree" v-model="Data.search_from.region_ids"
                     @change="changeSearchRegionHandel"/>
      </el-form-item>
      <el-form-item label="部署医院:" prop="hospital_id">
        <el-select v-model="Data.search_from.hospital_id" placeholder="请选择部署医院" clearable>
          <el-option v-for="(item,index) in Data.search_hospital_list" :label="item.name" :value="item.id"/>
        </el-select>
      </el-form-item>
      <el-form-item label="设备序列号：" prop="serial_number">
        <el-input style="width: 200px;" v-model="Data.search_from.serial_number" placeholder="请输入设备序列号" clearable/>
      </el-form-item>
      <el-form-item label="状态订单：" prop="status">
        <el-select v-model="Data.search_from.status" placeholder="请选择订单状态" clearable>
          <el-option v-for="(item,index) in Data.status_options" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="设备套餐：" prop="set_meal">
        <el-select v-model="Data.search_from.set_meal" placeholder="请选择设备套餐" clearable>
          <template v-for="(item, index) in Data.meal_options_array">
            <el-option v-if="item.id != 1000 && item.id != 18" :label="item.name" :value="item.id"/>
          </template>
        </el-select>
      </el-form-item>
      <el-form-item label="设备用途：" prop="use_type">
        <el-select v-model="Data.search_from.use_type" placeholder="请选择设备用途" clearable>
          <el-option v-for="(item,index) in Data.use_type_options" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="设备状态：" prop="device_status">
        <el-select v-model="Data.search_from.device_status" placeholder="请选择设备状态" clearable>
          <el-option v-for="(item,index) in Data.device_status_options" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="更新时间：" prop="time">
        <el-date-picker
          v-model="Data.search_from.time"
          type="datetimerange"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('search')"
          :loading="Data.loading"
          @click="onSearch">搜索
        </el-button>
        <el-button :icon="useRenderIcon('refresh')" @click="resetForm(formRef)">重置</el-button>
      </el-form-item>
    </el-form>
    <EpTableProBar
      title="订单列表"
      :loading="Data.loading"
      :columnList="[
        {label: '订单类型', show: true},
        {label: '订单编号', show: true},
        {label: '升级前订单编号', show: false},
        {label: '合同号', show: true},
        {label: '创建者', show: true},
        {label: '单位', show: false},
        {label: '代理商', show: true},
        {label: '销售', show: true},
        {label: 'UKey代码', show: true},
        {label: '设备数量\n（指派数）', show: true},
        {label: '订单状态', show: true},
        {label: '更新时间', show: true}]"
      :dataList="Data.data_list"
      @refresh="onSearch">
      <template #buttons>
        <el-space style="margin-right: 100px">设备总数：{{ Data.device_total }}</el-space>
        <el-button v-if="Auth('order/upgrade')" type="primary" :icon="useRenderIcon('add')"
                   @click="handleCreateOrderUpgrade">创建升级订单
        </el-button>
        <el-button v-if="Auth('order/create')" type="primary" :icon="useRenderIcon('add')" @click="handleUpdate(null)">
          创建订单
        </el-button>
      </template>
      <template v-slot="{ size, checkList }">
        <el-table
          border
          table-layout="auto"
          :size="size"
          :data="Data.data_list"
          :header-cell-style="{ background: '#fafafa', color: '#606266' }">
          <el-table-column type="expand">
            <template #default="props">
              <div style="margin: 5px 20px;">
                <p style="font-weight: 700;">设备列表</p>
                <el-table :data="props.row.devices" border>
                  <el-table-column prop="serial_number" label="设备号" width="200" align="center">
                    <template #default="scope2">
                      <div
                        v-if="((props.row.status == 5 || props.row.status == 9 || props.row.status == 1)&&props.row.is_upgrade!=1) || props.row.is_upgrade==1">
                        {{ scope2.row.serial_number }}
                        <a href="javascript:void(0);"
                           style="color: cornflowerblue;font-family: -webkit-body;font-size: 12px;"
                           @click="copy(scope2.row.serial_number)">复制</a>
                      </div>
                      <div v-else></div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="set_meal" label="原套餐" width="100" align="center">
                    <template #default="scope2">
                      <div style="display:flex;flex-direction:row;align-items: center;">
                        <span>{{ Data.meal_options[scope2.row.set_meal] }}</span>
                        <div v-if="scope2.row.is_append==1"
                             style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                          <span
                            style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">加</span>
                        </div>
                        <div v-if="scope2.row.is_append==2"
                             style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                          <span
                            style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">赠</span>
                        </div>
                        <div v-if="scope2.row.is_append==3" style="display: flex;flex-direction:row;">
                          <div
                            style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                            <span
                              style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">加</span>
                          </div>
                          <div
                            style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                            <span
                              style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">赠</span>
                          </div>
                        </div>
                        <div v-if="scope2.row.is_across && scope2.row.set_meal_upgrade==0"
                             style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                          <span
                            style="background-color:#154bea;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">跨</span>
                        </div>
                      </div>
                      <!--                      {{formatMealOptions(scope2.row.set_meal)}}-->
                    </template>
                  </el-table-column>
                  <el-table-column prop="set_meal" label="升级套餐" width="100" align="center">
                    <template #default="scope2">
                      <div style="display:flex;flex-direction:row;align-items: center;">
                        <span>{{ Data.meal_options[scope2.row.set_meal_upgrade] }}</span>
                        <div v-if="scope2.row.is_append_upgrade==1"
                             style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                          <span
                            style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">加</span>
                        </div>
                        <div v-if="scope2.row.is_append_upgrade==2"
                             style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                          <span
                            style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">赠</span>
                        </div>
                        <div v-if="scope2.row.is_append_upgrade==3" style="display: flex;flex-direction:row;">
                          <div
                            style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                            <span
                              style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">加</span>
                          </div>
                          <div
                            style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                            <span
                              style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">赠</span>
                          </div>
                        </div>
                        <div v-if="scope2.row.is_across && scope2.row.set_meal_upgrade>0"
                             style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                          <span
                            style="background-color:#154bea;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">跨</span>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="status" label="状态" width="100" align="center">
                    <template #default="scope2">
                      <el-tag v-if="scope2.row.status == 1" type="success">正常</el-tag>
                      <el-tag v-else type="danger">无效</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="use_type" label="设备用途" width="150" align="center">
                    <template #default="scope2">
                      {{ formatOptions(scope2.row.use_type, Data.use_type_options) }}
                    </template>
                  </el-table-column>


                  <el-table-column prop="use_type" label="指派" width="150" align="center">
                    <template #default="scope2">

                      <span v-if="scope2.row?.assign_user>0 && scope2.row.assign_name && !scope2.row?.feedback"
                            class="hq-bnt group">派</span>
                      <span v-if="scope2.row?.feedback" class="hq-bnt feed">馈</span>
                      <span v-if="scope2.row.assign_name">{{ scope2.row.assign_name }}</span>
                      <div v-if="scope2.row?.assign_user==0 && !scope2.row?.feedback && !scope2.row.assign_name">未指派
                      </div>

                    </template>
                  </el-table-column>


                  <el-table-column prop="region_name" label="激活区域" width="100" align="center">
                    <template #default="scope2">
                      <span v-if="scope2.row.region_name != ''">
                      {{ scope2.row.region_name }}
                      </span>
                      <span v-else>未激活</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="hospital_name" label="激活单位" width="100" align="center">
                    <template #default="scope2">
                      <span v-if="scope2.row.hospital_name != ''">
                      {{ scope2.row.hospital_name }}
                      </span>
                      <span v-else>未激活</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="authorize_time" label="授权时间" width="150" align="center">
                    <template #default="scope2">
                      <span v-if="scope2.row.authorize_time > 0">
                      {{ dayjs(scope2.row.authorize_time * 1000).format("YYYY-MM-DD HH:mm:ss") }}
                      </span>
                      <span v-else>未授权</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="activate_time" label="激活时间" width="150" align="center">
                    <template #default="scope2">
                      <span v-if="scope2.row.activate_time > 0">
                      {{ dayjs(scope2.row.activate_time * 1000).format("YYYY-MM-DD HH:mm:ss") }}
                      </span>
                      <span v-else>未激活</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="150" align="center">
                    <template #default="scope2">
                      <el-button v-if="Auth('order/device_details')"
                                 @click="handleDeviceDeatil(scope2.row, scope2.$index, props.row)" type="text"
                                 size="small">
                        {{ props.row.status == 9 && scope2.row.hospital_name == '' ? '激活' : '详情' }}
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </template>
          </el-table-column>
          <el-table-column v-if="checkList.includes('订单类型')" label="订单类型" align="center">
            <template #default="scope">
              <div style="display:flex;flex-direction:row;align-items: center;">
                <span v-if="scope.row.is_upgrade || scope.row.prev_order_number != ''">升级订单</span>
                <span v-else>销售订单</span>
                <div v-if="scope.row.is_urgent"
                     style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                  <span
                    style="background-color:red;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">急</span>
                </div>
                <div v-if="scope.row.is_transfer"
                     style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                  <span
                    style="background-color:#aa11ec;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">转</span>
                </div>
                <div v-if="scope.row.is_across"
                     style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                  <span
                    style="background-color:#154bea;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">跨</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column v-if="checkList.includes('订单编号')" label="订单编号" align="center" prop="order_number"/>
          <el-table-column v-if="checkList.includes('升级前订单编号')" label="升级前订单编号" align="center"
                           prop="prev_order_number"/>
          <el-table-column v-if="checkList.includes('合同号')" label="合同号" align="center" prop="contract_number"/>
          <el-table-column v-if="checkList.includes('创建者')" label="创建者" align="center" prop="operator_name"/>
          <el-table-column v-if="checkList.includes('单位')" label="单位" align="center" prop="hospital_name"/>
          <el-table-column v-if="checkList.includes('代理商')" label="代理商" align="center">
            <template #default="scope">
              <div style="display:flex;flex-direction:row;align-items: center;">
                <span>{{ scope.row.agent?.name }}</span>
                <div v-if="scope.row.is_report"
                     style="height:30px;width:40px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                  <span
                    style="background-color:#13ea16;color:white;width:40px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">报单</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column v-if="checkList.includes('销售')" label="销售" align="center" prop="sale_name"/>
          <el-table-column v-if="checkList.includes('UKey代码')" label="UKey代码" align="center" prop="ukey_code"/>
          <el-table-column v-if="checkList.includes('设备数量\n（指派数）')" label="设备数量
（指派数）" align="center" prop="count">
            <template #default="scope">
              {{ scope.row.devices?.length }}
              <br/>
              {{ `(${scope?.row?.devices?.filter(sub => sub.assign_name)?.length})` }}
            </template>
          </el-table-column>
          <el-table-column v-if="checkList.includes('订单状态')" label="订单状态" align="center" prop="status">
            <template #default="scope">
              <el-tag v-if="scope.row.status == 1 || scope.row.status == 5">{{
                  formatOptions(scope.row.status,
                    Data.status_options)
                }}
              </el-tag>
              <el-tag v-if="scope.row.status == 9" type="success">{{
                  formatOptions(scope.row.status,
                    Data.status_options)
                }}
              </el-tag>
              <el-tag v-if="scope.row.status == 0 || scope.row.status == 2" type="warning">
                {{ formatOptions(scope.row.status, Data.status_options) }}
              </el-tag>
              <el-tag v-if="scope.row.status == 3 || scope.row.status == 6" type="danger">
                {{ formatOptions(scope.row.status, Data.status_options) }}
              </el-tag>
              <el-tag v-if="scope.row.status == 7 || scope.row.status == 8" type="info">
                {{ formatOptions(scope.row.status, Data.status_options) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column v-if="checkList.includes('更新时间')" label="更新时间" align="center" prop="update_at"/>
          <el-table-column fixed="right" label="操作" align="center">
            <template #default="scope">
              <el-button
                v-if="Auth('order/details') && !(scope.row.is_urgent==1 && (scope.row.status==9 || scope.row.status==1) && scope.row.is_finance_review != 1)"
                class="reset-margin"
                type="text"
                :size="size"
                @click="handleUpdate(scope.row)"
                :icon="useRenderIcon('edits')">
                详情
              </el-button>
              <el-button
                v-if="Auth('order/review') && scope.row.is_urgent==1 && (scope.row.status==9 || scope.row.status==1) && scope.row.is_finance_review != 1"
                class="reset-margin"
                type="text"
                :size="size"
                @click="handleUpdate(scope.row)"
                :icon="useRenderIcon('edits')">
                补全
              </el-button>
              <el-popconfirm v-if="Auth('order/delete')" title="是否确认删除?" @confirm="handleDelete(scope.row)">
                <template #reference>
                  <el-button
                    class="reset-margin"
                    type="text"
                    :size="size"
                    :icon="useRenderIcon('delete')">
                    删除
                  </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          class="flex justify-end mt-4"
          :small="size === 'small' ? true : false"
          v-model:page-size="Data.search_from.page_size"
          :page-sizes="[10, 20, 30, 50]"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="Data.search_from.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"/>
      </template>
    </EpTableProBar>

    <el-dialog v-model="Data.dialogVisible" title="创建订单" draggable
               width="900px" top="50px"
               :close-on-click-modal="false"
               @close="clearCreateOrderCache(ruleFormRef)">
      <el-form
        ref="ruleFormRef"
        :model="Data.data_from"
        :rules="Data.rules"
        label-width="110px">
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="销售" prop="sale_name">
              {{ Data.data_from.sale_name }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="销售电话" prop="sale_phone">
              {{ Data.data_from.sale_phone }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="代理商区域" prop="region_id">
              <el-cascader :props="propsSearch" :options="Data.bind_region_tree" v-model="Data.data_from.region_id"
                           @change="changeBindRegionHandle" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="代理商:" prop="agent_id">
              <el-select v-model="Data.data_from.agent_id" placeholder="请选择代理商" @change="changeAgentHandle" clearable>
                <el-option v-for="(item,index) in Data.agent_list"
                           :key="item.group"
                           :label="item?.date_status>0?(item.date_status==1?item.name+'（临近）':item.name+'（过期）'):item.name"
                           :value="item.group"/>
              </el-select>
              <el-button v-if="Auth('agent/add')" type="primary" @click="handleCreateAgent">新建</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="代理商负责人" prop="principal">
              <el-input v-model="Data.data_from.principal" show-word-limit maxlength="20"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人电话" prop="principal_phone">
              <el-input v-model="Data.data_from.principal_phone" show-word-limit maxlength="20"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="默认医院区域" prop="hospital_region_id">
              <el-cascader :options="Data.user_region_tree"
                           v-model="Data.data_from.hospital_region_id"
                           @change="changeUserRegionHandle" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="默认医院名称:" prop="hospital_id">
              <el-select v-model="Data.data_from.hospital_id" placeholder="请选择医院"
                         @change="changeUserHospitalHandle(false)" clearable>
                <el-option v-for="(item,index) in Data.user_hospital_list" :key="item.id" :label="item.name"
                           :value="item.id"/>
              </el-select>
              <el-button v-if="Auth('hospital/add')" type="primary" @click="handleCreateHospital(-1, 'add-default')">
                新建
              </el-button>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="医院联系人" prop="contact">
              <el-input v-model="Data.data_from.contact" show-word-limit maxlength="20"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人电话" prop="contact_phone">
              <el-input v-model="Data.data_from.contact_phone" show-word-limit maxlength="20"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item class="receiver" label="收货人" prop="receiver">
              <el-input v-model="Data.data_from.receiver" show-word-limit maxlength="20"/>
              <el-button type="text" :size="size" @click="selectAgentHandle">套用代理商信息</el-button>
              <el-button type="text" :size="size" @click="selectHospitalHandle">套用医院信息</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收货人电话" prop="receiver_phone">
              <el-input v-model="Data.data_from.receiver_phone" show-word-limit maxlength="20"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收货地址" prop="receiver_region_arr">
              <el-cascader :options="Data.region_tree" v-model="Data.data_from.receiver_region_arr"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="详细地址" prop="receiver_address">
              <el-input v-model="Data.data_from.receiver_address" show-word-limit maxlength="50"/>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="order-create">
            <div style="margin-left: 30px;display:flex;">
              <div style="flex:1;font-size: 16px;font-weight: bold;">
                <el-span>订单设备({{ Data.data_from.devices?.length }})</el-span>
              </div>
              <div style="flex:1;display:flex;justify-content:center;" :data="total=getTotalPrice()">
                <div style="font-size: 16px;font-weight: bold;">
                  <p>合计金额:￥{{ total.toString().match(/\d+\.?\d{0,2}/) ? total : '' }}</p>
                  <p>{{ formatPrice(total.toString()) }}</p>
                </div>
              </div>
            </div>
            <el-tabs
              class="divice-tab"
              type="border-card"
              v-model="Data.data_from.device_index"
              editable
              @edit="addDeviceHandel">
              <el-tab-pane v-for="(item, index) in Data.data_from.devices"
                           :label="Data.meal_options[Data.data_from.devices[index].set_meal]" :name="index.toString()"
                           :key="index.toString()">
                <el-row class="device-item" :gutter="24" style="padding-top: 20px;">
                  <el-col :span="24">
                    <el-form-item label="合同公司">
                      <el-span
                        v-if="Data.data_from.devices[index].set_meal=='1000' || Data.data_from.devices[index].set_meal=='18'"
                        style="width:214px"></el-span>
                      <el-span v-else-if="Data.data_from.devices[index].set_meal=='1001'" style="width:214px">
                        广州爱孕记信息科技有限公司
                      </el-span>
                      <el-span v-else style="width:214px">长沙爱孕记医疗科技有限公司</el-span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="设备套餐">
                      <span>{{ Data.meal_options[Data.data_from.devices[index].set_meal] }}</span>
                      <div v-if="Data.data_from.devices[index].is_append==1"
                           style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                        <span
                          style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">加</span>
                      </div>
                      <div v-if="Data.data_from.devices[index].is_append==2"
                           style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                        <span
                          style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">赠</span>
                      </div>
                      <div v-if="Data.data_from.devices[index].is_append==3" style="display: flex;flex-direction:row;">
                        <div
                          style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                          <span
                            style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">加</span>
                        </div>
                        <div
                          style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                          <span
                            style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">赠</span>
                        </div>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="设备用途">
                      <el-select v-model="Data.data_from.devices[index].use_type" placeholder="选择设备用途">
                        <el-option v-for="item in Data.use_type_options" :label="item.label" :value="item.value"/>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="部署区域">
                      <el-cascader v-model="Data.data_from.devices[index].deploy_region" :options="Data.region_tree"
                                   @change="(value) => {changeRegionHandel(value, index, Data.data_from.devices[index])}"
                                   clearable/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="部署医院">
                      <el-select v-model="Data.data_from.devices[index].hospital_id" placeholder="请选择部署医院"
                                 @change="handleHospitalAddress(Data.data_from.devices[index], Data.data_from.devices[index].hospital_id)"
                                 clearable>
                        <el-option v-for="(item,index) in Data.data_from.devices[index].hospital_list"
                                   :key="item.id" :label="item.name" :value="item.id"/>
                      </el-select>
                      <el-button v-if="Auth('hospital/add')" type="primary" @click="handleCreateHospital(index, 'add')">
                        新建
                      </el-button>
                    </el-form-item>
                  </el-col>
                  <template
                    v-if="(Data.data_from.devices[index].hospital_id && Data.data_from.hospital_id && Data.data_from.hospital_id != Data.data_from.devices[index].hospital_id) || (!checkDeviceRegionInAgent(Data.data_from?.region_ids, Data.data_from.devices[index].hospital_id, Data.data_from.devices[index].deploy_region))">
                    <el-col :span="12">
                      <el-space
                        v-if="(!checkDeviceRegionInAgent(Data.data_from?.region_ids, Data.data_from.devices[index].hospital_id, Data.data_from.devices[index].deploy_region))"
                        style="color: red;margin-bottom: 10px;margin-left: 30px;">
                        {{ '设备部署医院不在用户区域内，请慎重选择!!!' }}
                      </el-space>
                    </el-col>
                    <el-col :span="12">
                      <el-space
                        v-if="Data.data_from.devices[index].hospital_id && Data.data_from.hospital_id && Data.data_from.hospital_id != Data.data_from.devices[index].hospital_id"
                        style="color: red;margin-bottom: 10px;margin-left: 30px;">
                        {{ '设备部署医院与默认医院不一致!!!' }}
                      </el-space>
                    </el-col>
                  </template>
                  <el-col :span="24">
                    <el-form-item label="医院详细地址">
                      <el-span>{{ Data.data_from.devices[index].deploy_units }}</el-span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="设备单价" prop="price">
                      <el-input v-model="Data.data_from.devices[index].price" style="width:214px" maxlength="15"
                                onkeyup="this.value=this.value.match(/\d+\.?\d{0,2}/)"
                                @change="handleDevicePrice(Data.data_from.devices[index].price, index)"
                                :prefix-icon="useRenderIcon('rmb')"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-span style="margin-left: 40px">{{ formatPrice(Data.data_from.devices[index].price) }}</el-span>
                  </el-col>
                  <el-col :span="24">
                    <el-collapse class="order-create-collapse">
                      <el-collapse-item title="设备套餐详情">
                        <table cellpadding="5" style="width:100%;margin-left: -1px;margin-bottom: 20px;">
                          <template v-for="(item2, index2) in item.products">
                            <tr v-for="(item3,index3) in item2.children" style="border: 1px solid rgba(0,0,0,0.2);">
                              <th v-if="index3 == 0" width="150px;" :rowspan="item2.children.length"
                                  style="border-right: 1px solid rgba(0,0,0,0.2);">{{ item2.product_name }}
                              </th>
                              <td style="font-family: fangsong;">{{ item3.product_name }}</td>
                              <th v-if="index3 == 0" width="100px" :rowspan="item2.children.length"
                                  style="font-family: fangsong;border-left: 1px solid rgba(0,0,0,0.2);">
                                {{ formatMonthCount(item2.month_count) }}
                              </th>
                            </tr>
                          </template>
                        </table>
                      </el-collapse-item>
                    </el-collapse>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="维保月数" prop="maintenance">
                      <el-select v-model="Data.data_from.devices[index].maintenance" placeholder="时长选择">
                        <el-option v-for="item in Data.maintenance_options" :label="item.label" :value="item.value"/>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="维保开始时间" prop="maintenance_time">
                      <el-date-picker
                        v-model="Data.data_from.devices[index].maintenance_time"
                        type="datetime"
                        :disabled-date="disabledDate"
                        placeholder="开始时间"/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24" style="padding-right: 20px;">
                    <el-form-item label="设备备注" prop="remark">
                      <el-input v-model="Data.data_from.devices[index].remark" type="textarea" show-word-limit
                                maxlength="500"/>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-tab-pane>
            </el-tabs>
          </el-col>
          <el-col :span="24">
            <el-form-item label="订单备注" prop="remark">
              <el-input v-model="Data.data_from.remark" type="textarea" show-word-limit maxlength="500"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="info" style="width: 100px" @click="handleOrderDraft">存为草稿</el-button>
          <el-button style="width: 100px" @click="cancelForm(ruleFormRef)">取消</el-button>
          <el-button type="primary" style="width: 100px" @click="submitForm(ruleFormRef)">提交</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog
    class="upgrade-dialog"
      v-model="Data.dialogVisible_Meal"
      title="选择产品型号"
      draggable
      width="1200px"
      top="50px"
      :close-on-click-modal="false"
      @close="clearCache"
      @open="handleDialogOpen"
    >


        <span style="color: #409EFF;font-family: fangsong;font-size: 16px;">
          请选择产品型号:
        </span>

        <el-cascader
          v-model="selectedId"
          :options="Data.new_meal_options"
          :props="new_meal_options_select"
          placeholder="请选择产品系列和型号"
          filterable
          show-all-levels
          clearable
          @change="handleNewMealOptionChange"

        ></el-cascader>

       <el-tabs v-model="Data.device.set_meal" @tab-change="addDeviceHandel" type="border-card">
          <el-tab-pane
            :label="selectedMeal?.name?selectedMeal?.name:''"
            :name="selectedMeal?.id?selectedMeal?.id:''">

            <el-form-item label="选择默认月份:"
              v-if="Data.device.set_meal == 1000 || Data.device.set_meal == 18">
              <el-select
              @change="changeDefMonthHandel"
              v-model="Data.device.month"
              placeholder="使用时长选择"
              clearable
            >
              <el-option
                v-for="(month) in filteredMonthOptions(Data.device.set_meal)"
                :key="month.value"
                :label="month.label"
                 :value="month.value"
              />
              </el-select>
            </el-form-item>
            <table cellpadding="5"
                   style="width: calc(100% + 40px);
                   margin-left: -20px;
                   margin-top: -10px;
                   margin-top: -15px;"
                   >


              <template
                v-if="Data.device.set_meal >= 18 && Data.device.set_meal < 1000"
                v-for="(item2,index2) in Data.product_options.new"
              >
                <tr v-for="(item3,index3) in item2.children" style="border: 1px solid rgba(0,0,0,0.2);">
                  <th width="150px;" v-if="index3 == 0" :rowspan="item2.children.length"
                      style="border-right: 1px solid rgba(0,0,0,0.2);">
                    <div style="height:30px;display:flex;align-items: center;justify-content: center;">
                      <span>{{ item2.product_name}}</span>
                      <span v-if="checkProductIsNo(Data.device.set_meal, item2.product_id) && item2.month_count != 0"
                            style="margin-left:3px;background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;">
                        {{ item2.month_count == -1 ? '赠' : '加' }}
                      </span>
                    </div>
                  </th>

                  <td style="font-family: fangsong; display: flex; align-items: center; justify-content: space-between;">
                    {{ item3.product_name }}
                    <div
                      v-if=" isSpecialProduct(item3.product_id)"
                      style="margin-right: 30px"
                    >

                      <el-select
                        @input="handleSwitchChange"
                        v-model="item3.month_count"
                        placeholder="使用时长选择"
                        style="width: 90px"
                      >
                        <el-option
                          v-for="(month,index) in filteredMonthOptions(Data.device.set_meal)"
                          :key="month.value"
                          :label="month.label"
                          :value="month.value"
                        />
                      </el-select>
                    </div>

                  </td>
                  <th style="width:110px;border-left: 1px solid rgba(0,0,0,0.2);" v-if="index3 == 0"
                      :rowspan="item2.children.length">
                    <select
                          style="border: 1px solid #222222;height: 24px;"
                          v-if="Data.device.set_meal == 1000 || Data.device.set_meal == 18"
                          v-model="item2.month_count"
                          @change="formatModelMonthCount(item2, item2.month_count,item3)"
                    >
                      <option
                      v-for="(month,index) in filteredMonthOptions(Data.device.set_meal)"
                      :key="month.value"
                      :label="month.label"
                      :value="month.value"
                      >
                    </option>

                    </select>
                    <div v-else>
                      <p
                        v-if="!checkProductIsNo(Data.device.set_meal, item2.product_id)"
                      >
                        {{ formatMonthCount(item2.month_count) }}
                      </p>
                      <select
                        v-else style="border: 1px solid #222222;height: 24px; "
                        v-model="item2.month_count"
                        @click="formatModelMonthCount(item2, item2.month_count,item3)"
                      >
                        <option
                          v-for="(month) in month_options"
                          :key="month.value"
                          :index="month.value"
                          :label="month.label"
                          :value="month.value">
                      </option>
                      </select>
                    </div>
                  </th>
                </tr>
              </template>

              <template v-else-if="Data.device.set_meal == 1200"
                        v-for="(item2,index2) in Data.product_options.old">
                <tr v-for="(item3,index3) in item2.children" style="border: 1px solid rgba(0,0,0,0.2);">
                  <th width="150px;" v-if="index3 == 0" :rowspan="item2.children.length"
                      style="border-right: 1px solid rgba(0,0,0,0.2);">
                    <div style="height:30px;display:flex;align-items: center;justify-content: center;">
                      <span>{{ item2.product_name }}</span>
                      <span v-if="checkProductIsNo(Data.device.set_meal, item2.product_id) && item2.month_count != 0"
                            style="margin-left:3px;background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;">
                        {{ item2.month_count == -1 ? '赠' : '加' }}
                      </span>
                    </div>
                  </th>

                  <td style="font-family: fangsong; display: flex; align-items: center; justify-content: space-between;">
                    {{ item3.product_name }}
                    <div v-if="
                          isSpecialProduct(item3.product_id)
                      "
                      style="margin-right: 30px">
                      <el-select
                      @change="handleSwitchChange(item3,item3.month_count)"
                      v-model="item3.month_count"
                      placeholder="使用时长选择"
                      >
                        <el-option v-for="(month) in month_options"
                        :key="month.value"
                        :label="month.label"
                        :value="month.value"/>
                      </el-select>
                    </div>
                  </td>

                  <th style="width:110px;border-left: 1px solid rgba(0,0,0,0.2);" v-if="index3 == 0"
                      :rowspan="item2.children.length">
                    <el-select
                      style="border: 1px solid #222222;height: 24px;"
                      v-if="Data.device.set_meal == 1000 || Data.device.set_meal == 18"
                      v-model="item2.month_count"
                      @change="formatModelMonthCount(item2, item2.month_count,item3)"
                    >
                      <el-option
                        v-for="(month) in month_options"
                        :key="month.value"
                        :label="month.label"
                        :value="month.value">
                      </el-option>
                    </el-select >
                    <div v-else>
                      <p v-if="!checkProductIsNo(Data.device.set_meal, item2.product_id)">
                        {{ formatMonthCount(item2.month_count) }}</p>
                      <select
                        v-else
                        style="border: 1px solid #222222;height: 24px;"
                        v-model="item2.month_count"
                        @click="formatModelMonthCount(item2, item2.month_count ,item3)"
                      >
                        <option
                          v-for="(month) in month_options"
                          :key="month.value"
                          :index="month.value"
                          :label="month.label"
                          :value="month.value"
                          >
                        </option>
                      </select>
                    </div>
                  </th>

                </tr>
              </template>

              <template v-else v-for="(item2,index2) in Data.product_options.old">
                <tr v-for="(item3,index3) in item2.children" style="border: 1px solid rgba(0,0,0,0.2);">
                  <th width="150px;" v-if="index3 == 0" :rowspan="item2.children.length"
                      style="border-right: 1px solid rgba(0,0,0,0.2);">
                    <div style="height:30px;display:flex;align-items: center;justify-content: center;">
                      <span>{{ item2.product_name }}</span>
                      <span v-if="checkProductIsNo(Data.device.set_meal, item2.product_id) && item2.month_count != 0"
                            style="margin-left:3px;background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;">
                        {{ item2.month_count == -1 ? '赠' : '加' }}
                      </span>
                    </div>
                  </th>
                  <td style="font-family: fangsong; display: flex; align-items: center; justify-content: space-between;">
                    {{ item3.product_name }}
                    <div
                      v-if="
                        isSpecialProduct(item3.product_id)
                        "
                      style="margin-right: 30px;">
                      <el-select
                        @input="handleSwitchChange"
                        v-model="item3.month_count"
                        placeholder="使用时长选择"
                        >
                          <el-option v-for="(month) in month_options"
                          :key="month.value"
                          :label="month.label"
                          :value="month.value">
                          </el-option>
                      </el-select>
                    </div>
                  </td>
                  <th style="width:110px;border-left: 1px solid rgba(0,0,0,0.2);" v-if="index3 == 0"
                      :rowspan="item2.children.length">
                    <select
                      style="border: 1px solid #222222;height: 24px;"
                      v-if="Data.device.set_meal == 1000 || Data.device.set_meal == 18"
                      v-model="item2.month_count"
                      @click="formatModelMonthCount(item2, item2.month_count,item3)"
                    >
                      <option v-for="(month) in month_options"
                        :key="month.value"
                        :label="month.label"
                        :value="month.value"
                      >
                      </option>
                    </select>
                    <div v-else>
                      <p v-if="!checkProductIsNo(Data.device.set_meal, item2.product_id)">
                        {{ formatMonthCount(item2.month_count) }}</p>
                      <select v-else style="border: 1px solid #222222;height: 24px;width: 60px" v-model="item2.month_count"
                              @click="formatModelMonthCount(item2, item2.month_count,item3)">
                        <option
                         v-for="(month) in month_options"
                         :key="month.value"
                         :label="month.label"
                          :value="month.value"
                          >

                        </option>
                      </select>
                    </div>
                  </th>
                </tr>
              </template>




            </table>


          </el-tab-pane>
      </el-tabs>

      <template #footer>
        <span class="dialog-footer">
            <el-button @click="cancelMealForm()">取消</el-button>
            <el-button type="primary" @click="submitMealForm()">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <div class="order-details">
      <el-dialog v-model="Data.dialogVisible_OrderDetails" title="订单详情" draggable
                 width="900px" top="50px"
                 :close-on-click-modal="false"
                 @close="clearOrderDetails">
        <el-steps :space="250" :active="Data.order_info.step" simple>
          <el-step title="创建订单" :icon="Edit"/>
          <el-step title="财务复核" :icon="Money"/>
          <el-step title="总经理审批" :icon="Checked"/>
          <el-step title="实施部署" :icon="Guide"/>
        </el-steps>

        <p style="font-size: 20px;font-weight: bolder;padding: 20px 0 0;">{{ Data.order_info?.hospital?.['name'] }}</p>

        <div class="item-title">基本信息</div>
        <el-row style="margin-left: 20px;">
          <el-col :span="12">
            订单编号：<span>{{ Data.order_info.order_number }}</span>
          </el-col>
          <el-col :span="12">
            订单时间：<span>{{ Data.order_info.update_at }}</span>
          </el-col>
          <el-col :span="12">
            代理商：<span>{{ Data.order_info?.agent?.['name'] }}</span>
            <el-button type="text" :icon="useRenderIcon('edits')" v-if="_self.roid=='1'"
                       @click="getEditServer(Data.order_info)">修改
            </el-button>
          </el-col>
          <el-col :span="12">
            <div style="display: flex;flex-direction:row;">
              <div style="width: 90px">代理商区域:</div>
              <div style="display: inline-block"><p v-for="(v,k) in Data.order_info?.agent?.['region_names']">
                {{ v }}</p></div>

            </div>
          </el-col>
          <el-col :span="12">
            代理商负责人：<span>{{ Data.order_info.principal }}</span>

          </el-col>
          <el-col :span="12">
            负责人电话：<span>{{ Data.order_info.principal_phone }}</span>

          </el-col>
          <template v-if="Data.order_info.status == 1 || !Auth('order/update-device')">
            <el-col :span="12">
              默认医院区域：<span>{{ Data.order_info.hospital_region_name }}</span>
            </el-col>
            <el-col :span="12">
              默认医院名称：<span>{{ Data.order_info.hospital_name }}</span>
            </el-col>
            <el-col :span="12">
              医院联系人：<span>{{ Data.order_info.contact }}</span>
            </el-col>
            <el-col :span="12">
              联系人电话：<span>{{ Data.order_info.contact_phone }}</span>
            </el-col>
          </template>
          <template v-else>
            <el-col :span="12">
              <el-form-item label="默认医院区域" style="margin-bottom: 0;">
                <el-cascader style="width: 200px" :props="propsSearch" :options="Data.user_region_tree"
                             v-model="Data.order_info.hospital_region_id"
                             @change="changeOrderRegionHandle" clearable/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="默认医院名称:" style="margin-bottom: 0;">
                <el-select style="width: 200px" v-model="Data.order_info.hospital_id"
                           placeholder="请选择医院" @change="changeOrderHospitalHandle"
                           clearable>
                  <el-option v-for="(item,index) in Data.user_hospital_list" :key="item.id" :label="item.name"
                             :value="item.id"/>
                </el-select>
                <el-button v-if="Auth('hospital/add')" type="primary"
                           @click="handleCreateHospital(-1, 'details-default')">新建
                </el-button>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="医院联系人" style="margin-bottom: 0;">
                <el-input style="width: 200px" v-model="Data.order_info.contact" show-word-limit maxlength="20"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系人电话" style="margin-bottom: 0;">
                <el-input style="width: 200px" v-model="Data.order_info.contact_phone" show-word-limit maxlength="20"/>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-button type="primary" @click="updateDefaultHospitalInfo(Data.order_info)">提交</el-button>
            </el-col>
          </template>
        </el-row>
        <div class="item-line"></div>

        <div class="item-title">合同信息</div>
        <el-row style="margin-left: 20px;">
          <el-col :span="12">
            <span v-if="!_self.edisBox">合同编号：{{ Data.order_info.contract_number }}</span>

            <el-button v-if="!_self.edisBox && _self.roid=='1' && (Data.order_info.status ==9 || Data.order_info.status ==1)" type="text" :icon="useRenderIcon('edits')"
                       @click="_self.edisBox=true;Data.order_info.Dtocontract_number=Data.order_info.contract_number">修改
            </el-button>

            <template v-if="_self.edisBox">
              合同编号：
              <el-input style="width: 200px" v-model="Data.order_info.Dtocontract_number" show-word-limit
                        maxlength="15" @input="Data.order_info.Dtocontract_number=Data.order_info.Dtocontract_number.replace(/[^(\d|a-z|A-Z)]/g, '')" />
              <el-button type="text" @click="Eventconfig(Data.order_info)">确认</el-button>
              <el-button type="text" @click="_self.edisBox=false">取消</el-button>
            </template>


          </el-col>
          <el-col :span="12">
            <span>设备数量：{{ Data.order_info.count }}</span>
          </el-col>
          <el-col :span="12">
            <span v-if="!Data.visible_OrderTransfer">销售姓名：{{ Data.order_info.sale_name }}</span>
            <el-select v-else v-model="Data.order_sale.sale_id" placeholder="请选择销售" clearable>
              <el-option v-for="item in Data.order_sale.sale_list" :label="item.username" :value="item.id"/>
            </el-select>
            <el-button v-if="Auth('order/transfer') && !Data.visible_OrderTransfer" type="text"
                       :icon="useRenderIcon('edits')" @click="handleTransfer(Data.order_info)">转移
            </el-button>
            <el-button v-if="Auth('order/transfer') && Data.visible_OrderTransfer" type="text"
                       @click="submitTransfer(Data.order_info)">确认
            </el-button>
            <el-button v-if="Auth('order/transfer') && Data.visible_OrderTransfer" type="text"
                       @click="clearOrderDetails">取消
            </el-button>
          </el-col>
          <el-col :span="12">
            <span>销售电话：{{ Data.order_info.sale_phone }}</span>
          </el-col>
        </el-row>
        <div class="item-line"></div>

        <div class="item-title">发货信息</div>
        <el-row style="margin-left: 20px;" v-if="Data.order_info.status == 1 || !Auth('order/update-device')">
          <el-col :span="12">
            收货人: <span>{{ Data.order_info.receiver }}</span>
          </el-col>
          <el-col :span="12">
            收货人电话: {{ Data.order_info.receiver_phone }}
          </el-col>
          <el-col :span="24">
            收货地址：<span v-if="Data.order_info.receiver_region_name||Data.order_info.receiver_address">{{
              Data.order_info.receiver_region_name
            }} / {{ Data.order_info.receiver_address }}</span>
          </el-col>
        </el-row>
        <el-row style="margin-left: 20px;" v-else>
          <el-col :span="12" style="padding-bottom: 0;">
            <el-form-item class="receiver" label="收货人" style="margin-bottom: 0;">
              <el-input style="width: 300px" v-model="Data.order_info.receiver" show-word-limit maxlength="20"/>
              <el-button type="text" :size="size" @click="selectDetailsAgentHandle">套用代理商信息</el-button>
              <el-button type="text" :size="size" @click="selectDetailsHospitalHandle">套用医院信息</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收货人电话" style="margin-bottom: 0;">
              <el-input style="width: 300px" v-model="Data.order_info.receiver_phone" show-word-limit maxlength="20"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收货地址" style="margin-bottom: 0;">
              <el-cascader style="width: 300px" :options="Data.region_tree"
                           v-model="Data.order_info.receiver_region_arr" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="详细地址" style="margin-bottom: 0;">
              <el-input style="width: 300px" v-model="Data.order_info.receiver_address" show-word-limit maxlength="50"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-button type="primary" @click="updateReceiverInfo(Data.order_info)">提交</el-button>
          </el-col>
        </el-row>

        <div class="item-line"></div>
        <div style="display: flex;">
          <div class="item-title" style="flex:1;">
            <el-span>设备信息（设备数量：{{ Data.order_info.devices?.length }}）</el-span>
          </div>
          <div v-if="Auth('order/view-amount')" style="flex:1;display:flex;justify-content:center;"
               :data="total=getOrderTotalPrice()">
            <div style="font-size:16px;font-weight: bold">
              <p>合计金额:￥{{ total.toString().match(/\d+\.?\d{0,2}/) ? total : '' }}</p>
              <p>{{ formatPrice(total.toString()) }}</p>
            </div>
          </div>
        </div>
        <el-form ref="ruleFormRef"
                 :model="Data.data_from"
                 :rules="{price: [{required: true, message: '设备单价必填', trigger: 'blur'}]}">
          <el-tabs type="border-card" v-model="Data.data_from.device_index">
            <el-tab-pane v-for="(item, index) in Data.order_info.devices" :label="Data.meal_options[item.set_meal]">
              <el-row class="device-item" :gutter="24">
                <el-col :span="24" style="padding: 0 0 0 10px;">
                  <el-form-item label="合同公司" style="margin-bottom: 0;">
                    <el-span v-if="Data.order_info.set_meal=='1000' || Data.order_info.set_meal=='18'"
                             style="width:214px"></el-span>
                    <el-span v-else-if="Data.order_info.set_meal=='1001'" style="width:214px">广州爱孕记信息科技有限公司</el-span>
                    <el-span v-else style="width:214px">长沙爱孕记医疗科技有限公司</el-span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="设备单价" style="margin-bottom: 0;" v-if="Auth('order/view-amount')" prop="price">
                    <el-input
                      :disabled="!Auth('order/edit-amount') || (Data.order_info.is_urgent != 1 && Data.order_info.status!=0) || (Data.order_info.is_urgent == 1 && !(Data.order_info.is_finance_review != 1 && (Data.order_info.status == 9 || Data.order_info.status == 1)))"
                      v-model="item.price" style="width:214px" maxlength="15"
                      onkeyup="this.value=this.value.match(/\d+\.?\d{0,2}/)"
                      @change="handleDevicePrice(item.price, index)"
                      :prefix-icon="useRenderIcon('rmb')"
                      v-if="!_self.editprice"/>

                    <el-input v-model="item.Newprice"
                              onkeyup="this.value=this.value.match(/\d+\.?\d{0,2}/)"
                              v-if="_self.editprice"
                              maxlength="15"
                              @change="handleDevicePrice(item.Newprice, index)"
                              :prefix-icon="useRenderIcon('rmb')">

                    </el-input>


                    <template v-if="_self.editprice">
                      <el-button type="text" @click="priceEditServer(item)">确认</el-button>
                      <el-button type="text" @click="_self.editprice=false;item.Newprice=''">取消</el-button>
                    </template>
                    <el-button type="text" v-if="!_self.editprice && _self.roid=='1'" :icon="useRenderIcon('edits')"
                               @click="_self.editprice=true">
                      修改
                    </el-button>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="" style="margin-bottom: 0;" v-if="Auth('order/view-amount')">
                    <el-span>{{ formatPrice(item.price) }}</el-span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="设备序列号" style="margin-bottom: 0;">
                    <div
                      v-if="((Data.order_info.status ==5 || Data.order_info.status ==9 || Data.order_info.status ==1)&&Data.order_info.is_upgrade!=1) || Data.order_info.is_upgrade==1">
                      {{ item.serial_number }}

                      <a href="javascript:void(0);"
                         style="color: cornflowerblue;font-family: -webkit-body;font-size: 12px;"
                         @click="copy(item.serial_number)">复制</a>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="设备状态:" style="margin-bottom: 0;">
                    <el-tag v-if="item.status == 1" type="success">正常</el-tag>
                    <el-tag v-else type="danger">无效</el-tag>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="设备套餐:" style="margin-bottom: 0;">
                    <span>{{ Data.meal_options[item.set_meal] }}</span>
                    <div v-if="item.is_append==1"
                         style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                      <span
                        style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">加</span>
                    </div>
                    <div v-if="item.is_append==2"
                         style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                      <span
                        style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">赠</span>
                    </div>
                    <div v-if="item.is_append==3" style="display: flex;flex-direction:row;">
                      <div
                        style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                        <span
                          style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">加</span>
                      </div>
                      <div
                        style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                        <span
                          style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">赠</span>
                      </div>
                    </div>
                    <!--                {{formatMealOptions(item.set_meal)}}-->
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="设备用途" style="margin-bottom: 0;">
                    <el-select disabled v-model="item.use_type" placeholder="选择设备用途" clearable>
                      <el-option v-for="item in Data.use_type_options" :label="item.label" :value="item.value"/>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="部署区域" style="margin-bottom: 0;">
                    <el-cascader
                      :disabled="item.is_pair>0 || item.activate_time>0 || item.upgrade_hospital_id>0 || !Auth('order/update-device')"
                      v-model="item.deploy_region"
                      :options="Data.region_tree" @change="(value) => {changeDetailsRegionHandel(value, index, item)}"
                      clearable/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="部署医院" style="margin-bottom: 0;">
                    <el-select
                      :disabled="item.is_pair>0 || item.activate_time>0 || item.upgrade_hospital_id>0 || !Auth('order/update-device')"
                      @change="handleHospitalAddress(item, item.hospital_id)"
                      v-model="item.hospital_id" placeholder="请选择部署医院" clearable>
                      <el-option v-for="(item1,index1) in item.hospital_list" :key="item1.id" :label="item1.name"
                                 :value="item1.id"/>
                    </el-select>
                    <el-button
                      v-if="Auth('hospital/add') && !(item.is_pair>0 || item.activate_time>0 || item.upgrade_hospital_id>0)"
                      type="primary" @click="handleCreateHospital(index, 'order')">新建
                    </el-button>
                  </el-form-item>
                </el-col>
                <template
                  v-if="(Data.order_info.devices[index].hospital_id && Data.order_info.hospital_id && Data.order_info.hospital_id != Data.order_info.devices[index].hospital_id) || (!checkDeviceRegionInAgent(Data.order_info?.region_ids, item.hospital_id, item.deploy_region))">
                  <el-col :span="12">
                    <el-space
                      v-if="(!checkDeviceRegionInAgent(Data.order_info?.region_ids, item.hospital_id, item.deploy_region))"
                      style="color: red;margin-bottom: 10px;margin-left: 30px;">
                      {{ '设备部署医院不在用户区域内，请慎重选择!!!' }}
                    </el-space>
                  </el-col>
                  <el-col :span="12">
                    <el-space
                      v-if="Data.order_info.devices[index].hospital_id && Data.order_info.hospital_id && Data.order_info.hospital_id != Data.order_info.devices[index].hospital_id"
                      style="color: red;margin-bottom: 10px;margin-left: 30px;">
                      {{ '设备部署医院与默认医院不一致!!!' }}
                    </el-space>
                  </el-col>
                </template>
                <el-col :span="24">
                  <el-form-item label="医院详细地址" style="margin-bottom: 0;">
                    <el-span style="width: 500px;">{{ item.deploy_units }}</el-span>
                    <el-button
                      v-if="Auth('order/update-device') && !(item.is_pair>0 || item.activate_time>0 || item.upgrade_hospital_id>0)"
                      style="margin-left: 10px" type="text" @click="updateDeviceInfo(item)">保存医院信息
                    </el-button>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="授权时间:" style="margin-bottom: 0;">
                    <span
                      v-if="item.authorize_time > 0">{{
                        dayjs(item.authorize_time * 1000).format("YYYY-MM-DD HH:mm:ss")
                      }}</span>
                    <span v-else>未授权</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="激活时间:" style="margin-bottom: 0;">
                    <span
                      v-if="item.activate_time > 0">{{
                        dayjs(item.activate_time * 1000).format("YYYY-MM-DD HH:mm:ss")
                      }}</span>
                    <span v-else>未激活</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="超声机品牌:" style="margin-bottom: 0;">
                    {{ item.machine_brand }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="超声机信息:" style="margin-bottom: 0;">
                    {{ item.machine_number }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="楼层:" style="margin-bottom: 0;">
                    {{ item.floor }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="房号:" style="margin-bottom: 0;">
                    {{ item.room }}
                  </el-form-item>
                </el-col>
                <el-col :span="24" style="padding: 0;">
                  <el-collapse class="order-details-collapse">
                    <el-collapse-item title="设备套餐详情">
                      <table cellpadding="5" style="width:100%;margin: 0 -10px;margin-bottom: 20px;">
                        <template v-for="item2 in item.products">
                          <tr v-for="(item3,index3) in item2.children" style="border: 1px solid rgba(0,0,0,0.2);">
                            <th v-if="index3 == 0" width="150px;" :rowspan="item2.children.length"
                                style="border-right: 1px solid rgba(0,0,0,0.2);">{{ item2.product_name }}
                            </th>
                            <td style="font-family: fangsong; position: relative;">
                              {{ item3.product_name }}
                               <div
                                v-if=" isSpecialProduct(item3.product_id) "  style="position: absolute; right: 30px;  display: inline-block;align-items: center;text-align: right;">
                                  {{ getMonthLabel(item3.month_count) }}
                              </div>
                          </td>
                            <th v-if="index3 == 0" width="100px" :rowspan="item2.children.length"
                                style="font-family: fangsong;border-left: 1px solid rgba(0,0,0,0.2);">
                              {{ formatMonthCount(item2.month_count) }}
                            </th>
                          </tr>
                        </template>
                      </table>
                    </el-collapse-item>
                  </el-collapse>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="维保月数" prop="maintenance">
                    <el-select :disabled="Data.order_info.status == 1 || !Auth('order/update-device')"
                               v-model="item.maintenance" placeholder="时长选择">
                      <el-option v-for="item in Data.maintenance_options" :label="item.label" :value="item.value"/>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="维保开始时间" prop="maintenance_time">
                    <el-date-picker
                      :disabled="Data.order_info.status == 1 || !Auth('order/update-device')"
                      v-model="item.maintenance_time"
                      type="datetime"
                      :disabled-date="disabledDate"
                      placeholder="开始时间"/>
                    <el-button v-if="Auth('order/update-device') && Data.order_info.status != 1"
                               style="margin-left: 10px" type="text" @click="updateMaintenanceInfo(item)">保存
                    </el-button>
                  </el-form-item>
                </el-col>
                <el-col :span="24" style="padding: 0 0 0 10px;">
                  <el-form-item label="设备备注" prop="remark">
                    <el-input disabled v-model="item.remark" type="textarea" show-word-limit maxlength="500"/>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-tab-pane>
          </el-tabs>

        </el-form>

        <div class="item-line"></div>
        <div class="item-title">其他信息</div>
        <el-row style="margin-left: 20px;">
          <el-col :span="24">
            订单备注：<span>{{ Data.order_info.remark }}{{ Data.order_info.receiver_address }}</span>
          </el-col>
        </el-row>

        <div class="item-line" v-if="(Data.order_info.status != 7 && Data.order_info.is_urgent != 1) ||
          (Data.order_info.is_urgent == 1 && Data.order_info.is_finance_review == 1)"></div>
        <div class="item-title" v-if="(Data.order_info.status != 7 && Data.order_info.is_urgent != 1) ||
          (Data.order_info.is_urgent == 1 && Data.order_info.is_finance_review == 1)">财务复核
        </div>
        <el-form
          v-if="Auth('order/review') && Data.order_info.status === 0 && Data.order_info.is_urgent != 1"
          ref="ruleFormRef_status"
          :model="Data.status_from_finance"
          :rules="Data.status_from_finance.status == 3 ? Data.status_from_rules : Data.status_from_finance_rules"
          label-width="110px">
          <el-form-item label="审核结果" prop="status">
            <el-radio-group v-model="Data.status_from_finance.status">
              <el-radio :label="2">同意</el-radio>
              <el-radio :label="3">拒绝</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="Data.status_from_finance.status!=3" label="合同号" prop="contract_number">
            <el-input type="text" v-model="Data.status_from_finance.contract_number" style="width: 600px"
                      @input="Data.status_from_finance.contract_number=Data.status_from_finance.contract_number.replace(/[^(\d|a-z|A-Z)]/g, '')"
                      show-word-limit minlength="15" maxlength="15"/>
            <el-button style="margin-left: 10px" type="text"
                       @click="saveFinanceInfo(Data.order_info.id, Data.status_from_finance.contract_number)">保存
            </el-button>
          </el-form-item>
          <el-form-item v-if="Data.status_from_finance.status!=3" label="最后发货时间" prop="last_date">
            <el-date-picker
              v-model="Data.status_from_finance.last_date"
              type="date"
              :disabled-date="disabledDate"
              placeholder="最后发货时间"
            />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="Data.status_from_finance.remark" type="textarea" show-word-limit maxlength="500"/>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitStatusForm(ruleFormRef_status)">提交</el-button>
            <el-button @click="cancelStatusForm(ruleFormRef_status)">取消</el-button>
          </el-form-item>
        </el-form>


        <div v-if="(Data.order_info.status > 0 && Data.order_info.status != 7 && Data.order_info.is_urgent != 1) ||
          (Data.order_info.is_urgent == 1 && Data.order_info.is_finance_review == 1)" style="margin-left: 20px;">
          <el-row>
            <template v-for="item in Data.order_info.reviews">
              <template v-if="item.status == 2 || item.status == 3">
                <img v-if="item.status == 2" style="right:100px;position: absolute;top: -20px;" :src="pass" alt="">
                <el-col :span="8">
                  审核结果：{{ item.status == 2 ? "同意" : "拒绝" }}
                </el-col>
                <el-col :span="8">
                  操作人：{{ item.operator_name }}
                </el-col>
                <el-col :span="8">
                  时间：{{ item.update_at }}
                </el-col>
                <el-col :span="24">
                  最后发货时间：{{ Data.order_info.last_date?dateFormat(Data.order_info.last_date, 'YYYY-mm-dd'):'' }}
                </el-col>
                <el-col :span="24">
                  备注：{{ item.remark }}
                </el-col>
                <el-col :span="24">
                  附加信息：{{ item.ip }} {{ item.ip_home }}
                </el-col>
              </template>
            </template>
          </el-row>
        </div>

        <div class="item-line" v-if="Data.order_info.status == 2 || Data.order_info.status == 5|| Data.order_info.status == 6 || Data.order_info.status == 9 || Data.order_info.status == 1 ||
          (Data.order_info.is_urgent == 1 && (Data.order_info.status == 0 || Data.order_info.status == 5))"></div>
        <div class="item-title" v-if="Data.order_info.status == 2 || Data.order_info.status == 5|| Data.order_info.status == 6 || Data.order_info.status == 9 || Data.order_info.status == 1 ||
          (Data.order_info.is_urgent == 1 && (Data.order_info.status == 0 || Data.order_info.status == 5))">总经理审批
        </div>
        <el-form
          v-if="Auth('order/manager_review') && Data.order_info.status == 2"
          ref="ruleFormRef_status"
          :model="Data.status_from"
          :rules="Data.status_from_rules"
          label-width="90px">
          <el-form-item label="审核结果" prop="status">
            <el-radio-group v-model="Data.status_from.status">
              <el-radio :label="5">同意</el-radio>
              <el-radio :label="6">拒绝</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="Data.status_from.remark" type="textarea" show-word-limit maxlength="500"/>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitStatusForm(ruleFormRef_status)">提交</el-button>
            <el-button @click="cancelStatusForm(ruleFormRef_status)">取消</el-button>
          </el-form-item>
        </el-form>


        <div v-if="Data.order_info.status == 5 || Data.order_info.status == 6 || Data.order_info.status == 9 || Data.order_info.status == 1 ||
          (Data.order_info.is_urgent == 1 && (Data.order_info.status == 0 || Data.order_info.status == 5))"
             style="margin-left: 20px;">
          <el-row>
            <template v-for="item in Data.order_info.reviews">
              <template v-if="item.status == 5 || item.status == 6">
                <img v-if="item.status == 5" style="right:100px;position: absolute;top: -20px;" :src="pass" alt="">
                <el-col :span="8">
                  审核结果：{{ item.status == 5 ? "同意" : "拒绝" }}
                </el-col>
                <el-col :span="8">
                  操作人：{{ item.operator_name }}
                </el-col>
                <el-col :span="8">
                  时间：{{ item.update_at }}
                </el-col>
                <el-col :span="24">
                  备注：{{ item.remark }}
                </el-col>
                <el-col :span="24">
                  附加信息：{{ item.ip }} {{ item.ip_home }}
                </el-col>
              </template>
            </template>
          </el-row>
        </div>


        <div class="item-line" v-if="((Data.order_info.status == 5 || Data.order_info.status == 9 || Data.order_info.status == 1) && Data.order_info.is_urgent != 1) ||
           (Data.order_info.is_urgent == 1 && (Data.order_info.status == 9 || Data.order_info.status == 1 || Data.order_info.status == 5 || (Data.order_info.status == 2 && Data.order_info.is_finance_review == 1)))"></div>
        <div class="item-title" v-if="((Data.order_info.status == 5 || Data.order_info.status == 9 || Data.order_info.status == 1) && Data.order_info.is_urgent != 1) ||
           (Data.order_info.is_urgent == 1 && (Data.order_info.status == 9 || Data.order_info.status == 1 || Data.order_info.status == 5 || (Data.order_info.status == 2  && Data.order_info.is_finance_review == 1)))">
          实施部署
        </div>
        <el-form
          v-if="Auth('order/deploy') && Data.order_info.status == 5"
          ref="ruleFormRef_deploy"
          :model="Data.deploy_from"
          :rules="Data.deploy_from_rules"
          label-width="90px">
          <el-form-item label="授权U-Key" prop="ukey_code">
            <el-select v-model="Data.deploy_from.ukey_code" placeholder="请选择U-Key">
              <el-option
                v-for="item in Data.ukey_list"
                :label="item.code"
                :value="item.code">
              </el-option>
            </el-select>
          </el-form-item>
          <table>
            <tr>
              <td>
                <el-form-item label="物流编号" prop="express_no">
                  <el-input v-model="Data.deploy_from.express_no" show-word-limit maxlength="20"/>
                </el-form-item>
              </td>
              <td style="vertical-align: top;padding-left: 20px;">
                <el-checkbox v-model="Data.deploy_from.express_type_" label="自提/直接配送" size="large"/>
              </td>
            </tr>
          </table>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="Data.deploy_from.remark" type="textarea" show-word-limit maxlength="500"/>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitStatusForm(ruleFormRef_deploy, 'deploy')">提交</el-button>
            <el-button @click="cancelStatusForm(ruleFormRef_deploy)">取消</el-button>
          </el-form-item>
        </el-form>


        <el-row v-if="((Data.order_info.status == 9 || Data.order_info.status == 1) && Data.order_info.is_urgent != 1) ||
          (Data.order_info.is_urgent == 1 && (Data.order_info.status == 9 || Data.order_info.status == 1 || ((Data.order_info.status == 5 || Data.order_info.status == 2)  && Data.order_info.is_finance_review == 1)))"
                style="margin-left: 20px;">
          <el-col :span="8">
            授权U-Key：{{ Data.order_info.ukey_code }}
          </el-col>
          <el-col :span="8">
            操作人：{{ Data.order_info.deploy_operator_name }}
          </el-col>
          <el-col :span="8">
            时间：{{ Data.order_info.deploy_at }}
          </el-col>
          <el-col :span="12">
            物流编号：{{ Data.order_info.express_no }}
          </el-col>
          <el-col :span="12">
            <el-checkbox disabled v-model="Data.order_info.express_type_" label="自提/直接配送" style="height: 20px;"/>
          </el-col>
          <el-col :span="24">
            备注：{{ Data.order_info.remark }}
          </el-col>
        </el-row>

        <div class="item-line"
             v-if="Data.order_info.is_urgent == 1 && Data.order_info.is_finance_review != 1 && (Data.order_info.status == 9 || Data.order_info.status == 1)"></div>
        <div class="item-title"
             v-if="Data.order_info.is_urgent == 1 && Data.order_info.is_finance_review != 1 && (Data.order_info.status == 9 || Data.order_info.status == 1)">
          财务复核
        </div>
        <el-form
          v-if="Auth('order/review') && Data.order_info.is_urgent == 1 && Data.order_info.is_finance_review != 1 && (Data.order_info.status == 9 || Data.order_info.status == 1)"
          ref="ruleFormRef_status"
          :model="Data.status_from_finance"
          :rules="Data.status_from_finance.status == 3 ? Data.status_from_rules : Data.status_from_finance_rules"
          label-width="110px">
          <el-form-item label="审核结果" prop="status">
            <el-radio-group v-model="Data.status_from_finance.status">
              <el-radio :label="2">同意</el-radio>
              <el-radio :label="3">拒绝</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="Data.status_from_finance.status!=3" label="合同号" prop="contract_number">
            <!--            <el-input type="text" v-model="Data.status_from_finance.contract_number" show-word-limit minlength="15" maxlength="15"/>-->
            <el-input type="text" v-model="Data.status_from_finance.contract_number" style="width: 600px"
                      @input="Data.status_from_finance.contract_number=Data.status_from_finance.contract_number.replace(/[^(\d|a-z|A-Z)]/g, '')"
                      show-word-limit minlength="15" maxlength="15"/>
            <el-button style="margin-left: 10px" type="text"
                       @click="saveFinanceInfo(Data.order_info.id, Data.status_from_finance.contract_number)">保存
            </el-button>
          </el-form-item>
          <el-form-item v-if="Data.status_from_finance.status!=3" label="最后发货时间" prop="last_date">
            <el-date-picker
              v-model="Data.status_from_finance.last_date"
              type="date"
              :disabled-date="disabledDate"
              placeholder="最后发货时间"
            />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="Data.status_from_finance.remark" type="textarea" show-word-limit maxlength="500"/>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitStatusForm(ruleFormRef_status)">提交</el-button>
            <el-button @click="cancelStatusForm(ruleFormRef_status)">取消</el-button>
          </el-form-item>
        </el-form>

        <div class="item-line" v-if="(Data.order_info.status === 0 || Data.order_info.status == 2 || Data.order_info.status == 3 ||
      Data.order_info.status == 6 || Data.order_info.status == 7 || Data.order_info.status == 8) && Auth('order/revoke')"></div>
        <div class="item-title" v-if="(Data.order_info.status === 0 || Data.order_info.status == 2 || Data.order_info.status == 3 ||
      Data.order_info.status == 6 || Data.order_info.status == 7 || Data.order_info.status == 8) && Auth('order/revoke')">
          撤销订单
        </div>
        <el-form
          v-if="(Data.order_info.status === 0 || Data.order_info.status == 2 || Data.order_info.status == 3 || Data.order_info.status == 6) && Auth('order/revoke')"
          ref="ruleFormRef_revoke"
          :model="Data.revoke_from"
          :rules="{remark: [
            {required: true, message: '审核结果必填', trigger: 'blur'},
            {max: 500, message: '备注限定500字符长度', trigger: ['blur', 'change']}]}"
          label-width="90px">
          <el-form-item label="撤销理由" prop="remark">
            <el-input v-model="Data.revoke_from.remark" type="textarea" show-word-limit maxlength="500"/>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitStatusForm(ruleFormRef_revoke, 'revoke')">提交</el-button>
            <el-button @click="cancelStatusForm(ruleFormRef_revoke)">取消</el-button>
          </el-form-item>
        </el-form>
        <div v-if="(Data.order_info.status == 7 || Data.order_info.status == 8) && Auth('order/revoke')"
             style="margin-left: 20px">
          <el-row>
            <template v-for="item in Data.order_info.reviews">
              <template v-if="item.status == 7 || item.status == 8">
                <el-col :span="12">
                  操作人：{{ item.operator_name }}
                </el-col>
                <el-col :span="12">
                  时间：{{ item.update_at }}
                </el-col>
                <el-col :span="24">
                  备注：{{ item.remark }}
                </el-col>
              </template>
            </template>
          </el-row>
        </div>
      </el-dialog>
    </div>

    <el-dialog v-model="Data.dialogVisible_OrderUpgrade" title="订单升级" draggable
               width="900px" top="50px"
               :close-on-click-modal="false"
               @close="clearUpgradeCache">
      <div class="item-title" style="margin-top: -10px;">基本信息</div>
      <el-form
        ref="ruleFormRef_upgrade"
        :model="Data.data_from"
        :rules="{
              remark: [
                {max: 500, message: '备注限定500字符长度', trigger: ['blur', 'change']},
              ]}"
        label-width="90px">
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="销售" prop="sale_name">
              {{ Data.data_from.sale_name }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="销售电话" prop="sale_phone">
              {{ Data.data_from.sale_phone }}
            </el-form-item>
          </el-col>
        </el-row>

        <div class="item-line"></div>
        <div class="item-title">设备信息（{{ Data.original_devices ? Data.original_devices.length : 0 }}）</div>
        <el-tabs type="border-card">
          <el-tab-pane v-for="(item, index) in Data.data_from.devices" :label="formatDeviceTabLabel(item)">
            <el-row class="device-item" :gutter="24">
              <el-col :span="12">
                <el-form-item label="设备序列号">
                  {{ item.serial_number }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="设备用途">
                  <el-select disabled v-model="item.use_type" placeholder="选择设备用途" clearable>
                    <el-option v-for="item in Data.use_type_options" :label="item.label" :value="item.value"/>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="升级前套餐">
                  {{ Data.meal_options[Data.original_devices[index].set_meal] }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-button :disabled="item.status != 1" type="danger" plain style="margin-left: 30px;"
                           @click="addDeviceHandel(index, 'upgrade')">点击升级此设备
                </el-button>
              </el-col>
              <el-col :span="12">
                <el-form-item label="设备单价">
                  <el-input v-model="Data.data_from.devices[index].price" style="width:214px" maxlength="15"
                            @input="Data.data_from.devices[index].price=Data.data_from.devices[index].price.replace(/[^\d]/g,'')"
                            :prefix-icon="useRenderIcon('rmb')"/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-span style="margin-left: 30px;">{{ formatPrice(Data.data_from.devices[index].price) }}</el-span>
              </el-col>
              <el-col :span="24" style="padding: 0;">
                <el-collapse class="order-create-collapse">
                  <el-collapse-item title="设备套餐详情">
                    <table cellpadding="5" style="margin: 0 -10px;margin-bottom: 20px;">
                      <template v-for="item2 in item.products">
                        <tr v-for="(item3,index3) in item2.children" style="border: 1px solid rgba(0,0,0,0.2);">
                          <th v-if="index3 == 0" width="150px;" :rowspan="item2.children.length"
                              style="border-right: 1px solid rgba(0,0,0,0.2);">{{ item2.product_name }}
                          </th>
                          <td style="font-family: fangsong;">{{ item3.product_name }}</td>
                          <th v-if="index3 == 0" width="100px;" :rowspan="item2.children.length"
                              style="font-family: fangsong;border-left: 1px solid rgba(0,0,0,0.2);">
                            {{ formatMonthCount(item2.month_count) }}
                          </th>
                        </tr>
                      </template>
                    </table>
                  </el-collapse-item>
                </el-collapse>
              </el-col>
              <el-col :span="24" style="padding: 0 0 0 10px;">
                <el-form-item label="设备备注" prop="remark">
                  <el-input v-model="item.remark" type="textarea" show-word-limit maxlength="500"/>
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>

        <div class="item-line"></div>
        <div class="item-title">其他信息</div>
        <el-form-item label="升级备注" prop="remark">
          <el-input v-model="Data.data_from.remark" type="textarea" show-word-limit maxlength="500"/>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
           <el-button @click="cancelForm(ruleFormRef_upgrade)">取消</el-button>
           <el-button type="primary" @click="submitUpgradeForm(ruleFormRef_upgrade)">提交</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="Data.dialogVisible_DeviceDetails" title="设备详情" draggable width="900px"
               :close-on-click-modal="false">
      <el-form label-width="100px">
        <el-row class="device-item" :gutter="24">
          <el-col :span="12">
            <el-form-item label="设备序列号:">
              <div
                v-if="((Data.device_info.order_status == 5 || Data.device_info.order_status == 9 || Data.device_info.order_status == 1)&&Data.device_info.is_upgrade!=1)||Data.device_info.is_upgrade==1">
                {{ Data.device_info.serial_number }}
                &nbsp;
                <a href="javascript:void(0);" style="color: cornflowerblue;font-family: -webkit-body;font-size: 12px;"
                   @click="copy(Data.device_info.serial_number)">复制</a>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备状态:">
              <el-tag v-if="Data.device_info.status == 1" type="success">正常</el-tag>
              <el-tag v-else type="danger">无效</el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备套餐:">
              <span>{{ Data.meal_options[Data.device_info.set_meal] }}</span>
              <div v-if="Data.device_info.is_append==1"
                   style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                <span
                  style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">加</span>
              </div>
              <div v-if="Data.device_info.is_append==2"
                   style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                <span
                  style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">赠</span>
              </div>
              <div v-if="Data.device_info.is_append==3" style="display: flex;flex-direction:row;">
                <div
                  style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                  <span
                    style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">加</span>
                </div>
                <div
                  style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                  <span
                    style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">赠</span>
                </div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备用途:">
              <el-select disabled v-model="Data.device_info.use_type" placeholder="选择设备用途" clearable>
                <el-option v-for="item in Data.use_type_options" :label="item.label" :value="item.value"/>
              </el-select>
              <el-popover
                ref="popoverRef"
                placement="right"
                width="400"
                trigger="click">
                <el-form
                  ref="ruleFormRef_use_type"
                  label-width="80px">
                  <el-form-item label="设备用途" prop="name">
                    <el-select v-model="Data.device_use_type_update" placeholder="选择设备用途" :teleported="false" clearable>
                      <el-option v-for="item in Data.use_type_options" :label="item.label" :value="item.value"/>
                    </el-select>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="handleUpdateUseType(null)">提交</el-button>
                    <el-button @click="cancelUpdateUseType(null)">取消</el-button>
                  </el-form-item>
                </el-form>
                <template #reference>
                  <el-button v-if="Auth('order/update-device')" slot="reference" type="text"
                             :icon="useRenderIcon('edits')">修改
                  </el-button>
                </template>
              </el-popover>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备单价" v-if="Auth('order/view-amount')" prop="price">

              <el-input v-if="!_self.editprice" disabled v-model="Data.device_info.price" style="width:214px" maxlength="15"
                        :prefix-icon="useRenderIcon('rmb')"/>


              <el-input v-model="Data.device_info.Newprice" onkeyup="this.value=this.value.match(/\d+\.?\d{0,2}/)"
                        v-if="_self.editprice" maxlength="15"></el-input>


              <template v-if="_self.editprice">
                <el-button type="text" @click="priceEditServer(Data.device_info)">确认</el-button>
                <el-button type="text" @click="_self.editprice=false">取消</el-button>
              </template>
              <el-button type="text" v-if="!_self.editprice && _self.roid=='1'" :icon="useRenderIcon('edits')"
                         @click="_self.editprice=true">
                修改
              </el-button>


            </el-form-item>


          </el-col>
          <el-col :span="12">
            <el-span v-if="Auth('order/view-amount')" style="margin-left: 20px">
              {{ formatPrice(Data.device_info.price) }}
            </el-span>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部署区域">
              <el-cascader
                :disabled="Data.device_info.is_pair>0 || Data.device_info.activate_time>0 || Data.device_info.upgrade_hospital_id>0 || !Auth('order/update-device')"
                v-model="Data.device_info.deploy_region"
                :options="Data.region_tree"
                @change="(value) => {changeDeviceDetailsRegionHandel(value, Data.device_info)}" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部署医院">
              <el-select
                :disabled="Data.device_info.is_pair>0 || Data.device_info.activate_time>0 || Data.device_info.upgrade_hospital_id>0 || !Auth('order/update-device')"
                @change="handleHospitalAddress(Data.device_info, Data.device_info.hospital_id)"
                v-model="Data.device_info.hospital_id" placeholder="请选择部署医院" clearable>
                <el-option v-for="(item,index) in Data.device_info.hospital_list" :key="item.id" :label="item.name"
                           :value="item.id"/>
              </el-select>
              <el-button
                v-if="Auth('hospital/add') && !(Data.device_info.is_pair>0 || Data.device_info.activate_time>0 || Data.device_info.upgrade_hospital_id>0)"
                type="primary" @click="handleCreateHospital(-1, 'device')">新建
              </el-button>
            </el-form-item>
          </el-col>
          <el-col :span="24"
                  v-if="(!checkDeviceRegionInAgent(Data.device_info?.region_ids, Data.device_info.hospital_id, Data.device_info.deploy_region))">
            <el-space style="color: red;margin-bottom: 10px;margin-left: 30px;">
              {{ '设备部署医院不在用户区域内，请慎重选择!!!' }}
            </el-space>
          </el-col>
          <el-col :span="24">
            <el-form-item label="医院详细地址">
              <el-span style="width: 500px;">{{ Data.device_info.deploy_units }}</el-span>
              <el-button
                v-if="Auth('order/update-device') && !(Data.device_info.is_pair>0 || Data.device_info.activate_time>0 || Data.device_info.upgrade_hospital_id>0)"
                style="margin-left: 10px" type="text" @click="updateDeviceInfo(Data.device_info)">保存医院信息
              </el-button>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="授权时间:">
              <span
                v-if="Data.device_info.authorize_time > 0">{{
                  dayjs(Data.device_info.authorize_time * 1000).format("YYYY-MM-DD HH:mm:ss")
                }}</span>
              <span v-else>未授权</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="激活时间:">
              <span
                v-if="Data.device_info.activate_time > 0">{{
                  dayjs(Data.device_info.activate_time * 1000).format("YYYY-MM-DD HH:mm:ss")
                }}</span>
              <span v-else>未激活</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="超声机品牌:">
              {{ Data.device_info.machine_brand }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="超声机信息:">
              {{ Data.device_info.machine_number }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="楼层:">
              {{ Data.device_info.floor }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="房号:">
              {{ Data.device_info.room }}
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="Auth('order/assign')">

            <el-form-item label="指派医生:" :class="{'hongquanIs':_self.isfankui}">
              <el-select
                v-model="Data.device_info.assign_user"
                placeholder="请选择医生"
                :disabled="IsDoctor && (_self.assUser!=_self.Useid)?true:false"
                clearable
               >
                <el-option
                  v-for="(item,index) in _self.daotorLists"
                  :key="item.id"
                  :label="item.username"
                  :value="item.id"
                />

              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="Auth('order/assign')" :class="{'hongquanIs':_self.isfankui}">
            <el-button type="primary" :disabled="IsDoctor && (_self.assUser!=_self.Useid)?true:false" @click="addDoctor(Data.device_info)">提交</el-button>
            <p style="height: 30px"></p>
          </el-col>

          <el-col style="padding: 0 0 0 10px;" v-if="Auth('order/feedback')">
            <el-collapse class="device-details-collapse" v-model="activeNames" accordion>
              <el-collapse-item title="指派反馈" name="1">
                <el-col>
                  <el-form-item label="指派结果:">
                    <el-radio-group v-model="Data.device_info.feedback.status" class="ml-4"
                                    :disabled="_self.assUser==_self.Useid && !_self.isfankui?false:true">
                      <el-radio :label="0" size="large">未完成</el-radio>
                      <el-radio :label="1" size="large">已完成</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>

                <div style="clear: both;"></div>


                <el-col span="12">
                  <el-form-item label="反馈、备注">
                    <el-input
                      v-model="Data.device_info.feedback.remark"
                      :autosize="{ minRows: 2, maxRows: 4 }"
                      placeholder="请输入反馈、备注"
                      :disabled="_self.assUser==_self.Useid && !_self.isfankui?false:true"
                      type="textarea" show-word-limit maxlength="500"
                    />
                  </el-form-item>
                </el-col>


                <el-col :span="24">
                  <el-button type="primary" :disabled="_self.assUser==_self.Useid && !_self.isfankui?false:true" @click="seteedback(Data.device_info)">提交</el-button>
                  <p style="height: 30px"></p>
                </el-col>


              </el-collapse-item>
            </el-collapse>
          </el-col>


          <el-col :span="24" style="padding: 0 0 0 10px;">
            <el-collapse class="device-details-collapse">
              <el-collapse-item title="设备套餐详情">
                <table cellpadding="5" style="width:100%;margin-bottom: 20px;">
                  <template v-for="item in Data.device_info.products">
                    <tr v-for="(item2,index2) in item.children" style="border: 1px solid rgba(0,0,0,0.2);">
                      <th v-if="index2 == 0" width="150px;" :rowspan="item.children.length"
                          style="border-right: 1px solid rgba(0,0,0,0.2);">{{ item.product_name }}
                      </th>
                      <td style="font-family: fangsong;">{{ item2.product_name }}</td>
                      <th v-if="index2 == 0" width="100px" :rowspan="item.children.length"
                          style="font-family: fangsong;border-left: 1px solid rgba(0,0,0,0.2);">
                        {{ formatMonthCount(item.month_count) }}
                      </th>
                    </tr>
                  </template>
                </table>
              </el-collapse-item>
            </el-collapse>

          </el-col>
          <el-col :span="12" style="padding: 0 0 0 10px;">
            <el-form-item label="维保月数:">
              <el-select :disabled="Data.order_info.status == 1 || !Auth('order/update-device')"
                         v-model="Data.device_info.maintenance" placeholder="时长选择">
                <el-option v-for="item in Data.maintenance_options" :label="item.label" :value="item.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维保开始时间" prop="maintenance_time">
              <el-date-picker
                :disabled="Data.order_info.status == 1 || !Auth('order/update-device')"
                v-model="Data.device_info.maintenance_time"
                type="datetime"
                :disabled-date="disabledDate"
                placeholder="开始时间"/>
              <el-button v-if="Auth('order/update-device') && Data.order_info.status != 1"
                         style="margin-left: 10px" type="text" @click="updateMaintenanceInfo(Data.device_info)">保存
              </el-button>
            </el-form-item>
          </el-col>
          <el-col :span="24" style="padding: 0 0 0 10px;">
            <el-form-item label="设备备注:" prop="remark">
              <el-input disabled v-model="Data.device_info.remark" type="textarea" show-word-limit maxlength="500"/>
            </el-form-item>
          </el-col>


        </el-row>
      </el-form>
    </el-dialog>

    <el-dialog v-model="Data.dialogVisible_CreateOrderUpgrade" title="创建升级订单" draggable
               :close-on-click-modal="false"
               custom-class="upgrade-device upgrade-order" width="1700px" top="50px"
               @close="clearCreateOrderCache(ruleFormRef_create_upgrade)">
      <el-form
        ref="ruleFormRef_create_upgrade"
        :model="Data.data_from"
        :rules="Data.rules"
        label-width="110px">
        <el-row :gutter="24">
          <el-col :span="24">
            <el-span style="color:#04BAB1;font-size:20px;">订单信息</el-span>
          </el-col>
          <el-col :span="6">
            <el-form-item label="销售" prop="sale_name">
              {{ Data.data_from.sale_name }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="销售电话" prop="sale_phone">
              {{ Data.data_from.sale_phone }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="代理商区域" prop="region_id">
              <el-cascader :props="propsSearch" :options="Data.bind_region_tree" v-model="Data.data_from.region_id"
                           @change="changeBindRegionHandle" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="代理商:" prop="agent_id">
              <el-select v-model="Data.data_from.agent_id" placeholder="请选择代理商" @change="changeAgentHandle" clearable>
                <el-option v-for="(item,index) in Data.agent_list"
                           :key="item.group"
                           :label="item?.date_status>0?(item.date_status==1?item.name+'（临近）':item.name+'（过期）'):item.name"
                           :value="item.group"/>
              </el-select>
              <el-button v-if="Auth('agent/add')" type="primary" @click="handleCreateAgent">新建</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="代理商负责人" prop="principal">
              <el-input v-model="Data.data_from.principal" show-word-limit maxlength="20"/>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="负责人电话" prop="principal_phone">
              <el-input v-model="Data.data_from.principal_phone" show-word-limit maxlength="20"/>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="默认医院区域" prop="hospital_region_id">
              <el-cascader :props="propsSearch" :options="Data.user_region_tree"
                           v-model="Data.data_from.hospital_region_id"
                           @change="changeUserRegionHandle" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="默认医院名称:" prop="hospital_id">
              <el-select v-model="Data.data_from.hospital_id" placeholder="请选择医院"
                         @change="changeUserHospitalHandle(true)" clearable>
                <el-option v-for="(item,index) in Data.user_hospital_list" :key="item.id" :label="item.name"
                           :value="item.id"/>
              </el-select>
              <el-button v-if="Auth('hospital/add')" type="primary" @click="handleCreateHospital(-1, 'add-default')">
                新建
              </el-button>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="医院联系人" prop="contact">
              <el-input v-model="Data.data_from.contact" show-word-limit maxlength="20"/>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="联系人电话" prop="contact_phone">
              <el-input v-model="Data.data_from.contact_phone" show-word-limit maxlength="20"/>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="receiver" label="收货人" prop="receiver">
              <el-input v-model="Data.data_from.receiver" show-word-limit maxlength="20"/>
              <el-button type="text" :size="size" @click="selectAgentHandle">套用代理商信息</el-button>
              <el-button type="text" :size="size" @click="selectHospitalHandle">套用医院信息</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="收货人电话" prop="receiver_phone">
              <el-input v-model="Data.data_from.receiver_phone" show-word-limit maxlength="20"/>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="收货地址" prop="receiver_region_arr">
              <el-cascader :options="Data.region_tree" v-model="Data.data_from.receiver_region_arr"/>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="详细地址" prop="receiver_address">
              <el-input v-model="Data.data_from.receiver_address" show-word-limit maxlength="50"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <div style="display:flex;">
              <div style="flex:1;">
                <el-span style="color:#04BAB1;font-size:20px;">升级设备</el-span>
              </div>
              <div style="flex:1;display:flex;justify-content:center;" :data="total=getTotalPrice()">
                <div style="font-size: 16px;font-weight: bold;">
                  <p>合计金额:￥{{ total.toString().match(/\d+\.?\d{0,2}/) ? total : '' }}</p>
                  <p>{{ formatPrice(total.toString()) }}</p>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="24">
            <div class="device-list" style="height:400px;overflow-y: auto;display:flex;flex-direction:column;">
              <EpTableProBar
                title="升级设备"
                :columnList="[
                  {label: '设备号', show: true},
                  {label: '原套餐', show: true},
                  {label: '升级套餐', show: true},
                  {label: '状态', show: true},
                  {label: '设备用途', show: true},
                  {label: '部署区域', show: true},
                  {label: '部署医院', show: true},
                  {label: '授权时间', show: true},
                  {label: '激活时间', show: true},
                  {label: '设置升级单价', show: true},
                  {label: '维保月数', show: true},
                  {label: '维保开始时间', show: true},
                  ]"
                :dataList="Data.data_from.devices"
                @refresh="onSearchUpgrade">
                <template #buttons>
                </template>
                <template v-slot="{ size, checkList }">
                  <el-table
                    border
                    table-layout="auto"
                    :size="size"
                    :data="Data.data_from.devices"
                    :header-cell-style="{ background: '#fafafa', color: '#606266' }">
                    <el-table-column v-if="checkList.includes('设备号')" label="设备号" align="center" prop="serial_number">
                      <template #default="scope">
                      </template>
                    </el-table-column>
                    <el-table-column v-if="checkList.includes('原套餐')" label="原套餐" align="center" prop="set_meal">
                      <template #default="scope">
                        <div style="display:flex;flex-direction:row;align-items: center;justify-content: center;">
                          <span>{{ Data.meal_options[scope.row.set_meal] }}</span>
                          <div v-if="scope.row.is_append==1"
                               style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                            <span
                              style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">加</span>
                          </div>
                          <div v-if="scope.row.is_append==2"
                               style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                            <span
                              style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">赠</span>
                          </div>
                          <div v-if="scope.row.is_append==3" style="display: flex;flex-direction:row;">
                            <div
                              style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                              <span
                                style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">加</span>
                            </div>
                            <div
                              style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                              <span
                                style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">赠</span>
                            </div>
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column v-if="checkList.includes('升级套餐')" label="升级套餐" align="center"
                                     prop="set_meal_upgrade">
                      <template #default="scope">
                        <div style="display:flex;flex-direction:row;align-items: center;justify-content: center;">
                          <span>{{ Data.meal_options[scope.row.set_meal_upgrade] }}</span>
                          <div v-if="scope.row.is_append_upgrade==1"
                               style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                            <span
                              style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">加</span>
                          </div>
                          <div v-if="scope.row.is_append_upgrade==2"
                               style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                            <span
                              style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">赠</span>
                          </div>
                          <div v-if="scope.row.is_append_upgrade==3" style="display: flex;flex-direction:row;">
                            <div
                              style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                              <span
                                style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">加</span>
                            </div>
                            <div
                              style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                              <span
                                style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">赠</span>
                            </div>
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column v-if="checkList.includes('状态')" label="状态" align="center" prop="status">
                      <template #default="scope">
                        <el-tag v-if="scope.row.status == 1" type="success">正常</el-tag>
                        <el-tag v-else type="danger">无效</el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column v-if="checkList.includes('设备用途')" label="设备用途" align="center" prop="use_type">
                      <template #default="scope">
                        {{ formatOptions(scope.row.use_type, Data.use_type_options) }}
                      </template>
                    </el-table-column>
                    <el-table-column v-if="checkList.includes('部署区域')" label="部署区域" align="center" width="250"
                                     prop="region_name">
                      <template #default="scope">
                        <span
                          v-if="scope.row.is_pair>0 || scope.row.activate_time>0 || scope.row.upgrade_hospital_id>0">{{
                            scope.row.region_name
                          }}</span>
                        <el-cascader v-else v-model="Data.data_from.devices[scope.$index].deploy_region"
                                     :options="Data.region_tree"
                                     @change="(value) => {changeRegionHandel(value, scope.$index, Data.data_from.devices[scope.$index])}"
                                     clearable/>
                      </template>
                    </el-table-column>
                    <el-table-column v-if="checkList.includes('部署医院')" label="部署医院" align="center" width="200"
                                     prop="hospital_name">
                      <template #default="scope">
                        <span
                          v-if="scope.row.is_pair>0 || scope.row.activate_time>0 || scope.row.upgrade_hospital_id>0">{{
                            scope.row.hospital_name
                          }}</span>
                        <div v-else>
                          <el-select v-model="Data.data_from.devices[scope.$index].hospital_id" placeholder="请选择部署医院"
                                     @change="handleHospitalAddress(Data.data_from.devices[scope.$index], Data.data_from.devices[scope.$index].hospital_id)"
                                     clearable>
                            <el-option v-for="(item,index) in Data.data_from.devices[scope.$index].hospital_list"
                                       :key="item.id" :label="item.name" :value="item.id"/>
                            <el-option v-if="Auth('hospital/add')"
                                       style="color: #00bfb5; display: flex;justify-content: center;" label="新建"
                                       value="0" @click="handleCreateHospital(scope.$index, 'add')"/>
                          </el-select>
                          <el-space
                            v-if="(!checkDeviceRegionInAgent(Data.data_from?.region_ids, Data.data_from.devices[scope.$index]?.hospital_id, Data.data_from.devices[scope.$index]?.deploy_region))"
                            style="color: red">
                            {{ '设备部署医院不在用户区域内，请慎重选择!!!' }}
                          </el-space>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column v-if="checkList.includes('授权时间')" label="授权时间" align="center"
                                     prop="authorize_time">
                      <template #default="scope">
                        <span v-if="scope.row.authorize_time > 0">
                        {{ dayjs(scope.row.authorize_time * 1000).format("YYYY-MM-DD HH:mm:ss") }}
                        </span>
                        <span v-else>未授权</span>
                      </template>
                    </el-table-column>
                    <el-table-column v-if="checkList.includes('激活时间')" label="激活时间" align="center" prop="activate_time">
                      <template #default="scope">
                        <span v-if="scope.row.activate_time > 0">
                        {{ dayjs(scope.row.activate_time * 1000).format("YYYY-MM-DD HH:mm:ss") }}
                        </span>
                        <span v-else>未授权</span>
                      </template>
                    </el-table-column>
                    <el-table-column v-if="checkList.includes('设置升级单价')" label="设置升级单价" align="center" width="150"
                                     prop="price">
                      <template #default="scope">
                        <el-input v-model="Data.data_from.devices[scope.$index].price" style="width:150px"
                                  maxlength="15"
                                  onkeyup="this.value=this.value.match(/\d+\.?\d{0,2}/)"
                                  :prefix-icon="useRenderIcon('rmb')"/>
                      </template>
                    </el-table-column>
                    <el-table-column v-if="checkList.includes('维保月数')" label="维保月数" align="center" width="120"
                                     prop="price">
                      <template #default="scope">
                        <el-select v-model="Data.data_from.devices[scope.$index].maintenance" placeholder="时长选择">
                          <el-option v-for="item in Data.maintenance_options" :label="item.label" :value="item.value"/>
                        </el-select>
                      </template>
                    </el-table-column>
                    <el-table-column v-if="checkList.includes('维保开始时间')" label="维保开始时间" align="center" width="160"
                                     prop="price">
                      <template #default="scope">
                        <el-date-picker
                          v-model="Data.data_from.devices[scope.$index].maintenance_time"
                          type="datetime"
                          style="width:160px"
                          :disabled-date="disabledDate"
                          placeholder="开始时间"/>
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="150" align="center">
                      <template #default="scope">
                        <el-button type="text" @click="addDeviceHandel(scope.$index, 'edit-meal')">更换套餐</el-button>
                        <el-button type="text" @click="removeUpgradeDevice(scope.$index)">移除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </template>
              </EpTableProBar>
              <div style="display: flex;justify-content: center;">
                <el-button style="width:100px;" type="primary" @click="handleSelectUpgradeDevice">选择设备</el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-checkbox style="margin-right:50px" v-model="Data.data_from.is_urgent" true-label="1" false-label="0"
                       label="设为紧急升级" size="large"/>
          <el-button style="width:100px" type="primary"
                     @click="submitCreateUpgradeOrderlForm(ruleFormRef_create_upgrade)">提交</el-button>
          <el-button style="width:100px"
                     @click="canceCreateUpgradeOrderlForm(ruleFormRef_create_upgrade)">取消</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="Data.dialogVisible_SelectUpgradeDevice" title="选择升级设备" draggable
               custom-class="upgrade-device select-device" width="1700px" top="50px"
               :close-on-click-modal="false"
               @close="cancleUpgradeDevices">
      <el-form ref="formUpgradeRef" :inline="true" :model="Data.search_from_upgrade"
               class="bg-white w-99/100 pl-8 pt-4">
        <el-form-item label="关键词搜索：" prop="keyword">
          <el-input style="width: 200px;" v-model="Data.search_from_upgrade.keyword" placeholder="单位名称、销售、电话、U-Key"
                    clearable/>
        </el-form-item>
        <el-form-item label="订单号：" prop="order_number">
          <el-input style="width: 200px;" v-model="Data.search_from_upgrade.order_number" placeholder="请输入订单号"
                    clearable/>
        </el-form-item>
        <el-form-item label="合同号：" prop="contract_number">
          <el-input style="width: 200px;" v-model="Data.search_from_upgrade.contract_number" placeholder="请输入合同号"
                    clearable/>
        </el-form-item>
        <el-form-item label="选择区域:" prop="region_ids">
          <el-cascader :options="Data.region_tree" v-model="Data.search_from_upgrade.region_ids"
                       @change="changeRegionHandel"/>
        </el-form-item>
        <el-form-item label="绑定单位:" prop="hospital_id">
          <el-select v-model="Data.search_from_upgrade.hospital_id" placeholder="请选择授权单位" clearable>
            <el-option v-for="(item,index) in Data.hospital_list" :label="item.name" :value="item.id"/>
          </el-select>
        </el-form-item>
        <el-form-item label="设备序列号：" prop="serial_number">
          <el-input style="width: 200px;" v-model="Data.search_from_upgrade.serial_number" placeholder="请输入设备序列号"
                    clearable/>
        </el-form-item>
        <el-form-item label="设备套餐：" prop="set_meal">
          <el-select v-model="Data.search_from_upgrade.set_meal" placeholder="请选择设备套餐" clearable>
            <template v-for="(item, index) in Data.meal_options_array">
              <el-option v-if="item.id != 1000 && item.id != 18" :label="item.name" :value="item.id"/>
            </template>
          </el-select>
        </el-form-item>
        <el-form-item label="设备用途：" prop="use_type">
          <el-select v-model="Data.search_from_upgrade.use_type" placeholder="请选择设备用途" clearable>
            <el-option v-for="(item,index) in Data.use_type_options" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="订单类型：" prop="use_type">
          <el-select v-model="Data.search_from_upgrade.order_type" placeholder="请选择订单" clearable>
            <el-option v-for="(item,index) in Data.order_type_options" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="更新时间：" prop="time">
          <el-date-picker
            v-model="Data.search_from_upgrade.time"
            type="datetimerange"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('search')"
            :loading="Data.loading"
            @click="onSearchUpgrade">搜索
          </el-button>
          <el-button :icon="useRenderIcon('refresh')" @click="resetUpgradeForm(formUpgradeRef)">重置</el-button>
        </el-form-item>
      </el-form>
      <EpTableProBar
        title="选择升级设备"
        class="select-upgrade"
        :loading="Data.upgrade_loading"
        :columnList="[
          {label: '订单类型', show: true},
          {label: '订单编号', show: true},
          {label: '升级前订单编号', show: true},
          {label: '合同号', show: true},
          {label: '创建者', show: true},
          {label: '单位', show: true},
          {label: '销售', show: true},
          {label: 'UKey代码', show: true},
          {label: '设备数量', show: true},
          {label: '订单状态', show: true},
          {label: '更新时间', show: true}]"
        :dataList="Data.upgrade_data_list"
        @refresh="onSearchUpgrade">
        <template #buttons>
        </template>
        <template v-slot="{ size, checkList }">
          <el-table
            border
            table-layout="auto"
            :size="size"
            row-key="id"
            :reserve-selection="true"
            :data="Data.upgrade_data_list"
            :header-cell-style="{ background: '#fafafa', color: '#606266' }">
            <el-table-column type="expand">
              <template #default="props">
                <div style="margin: 5px 20px;">
                  <p style="font-weight: 700;">设备列表</p>
                  <el-table :data="props.row.devices"
                            @selection-change="(selections) => {handleSelectionChange(selections, props.$index, props.row)}"
                            border>
                    <el-table-column
                      type="selection"
                      width="55">
                    </el-table-column>
                    <el-table-column prop="serial_number" label="设备号" width="200" align="center">
                      <template #default="scope2">
                        <div v-if="props.row.status == 5 || props.row.status == 9 || props.row.status == 1">
                          {{ scope2.row.serial_number }}
                          <a href="javascript:void(0);"
                             style="color: cornflowerblue;font-family: -webkit-body;font-size: 12px;"
                             @click="copy(scope2.row.serial_number)">复制</a>
                        </div>
                        <div v-else></div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="set_meal" label="原套餐" width="100" align="center">
                      <template #default="scope2">
                        <div style="display:flex;flex-direction:row;align-items: center;">
                          <span>{{ Data.meal_options[scope2.row.set_meal] }}</span>
                          <div v-if="scope2.row.is_append==1"
                               style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                            <span
                              style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">加</span>
                          </div>
                          <div v-if="scope2.row.is_append==2"
                               style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                            <span
                              style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">赠</span>
                          </div>
                          <div v-if="scope2.row.is_append==3" style="display: flex;flex-direction:row;">
                            <div
                              style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                              <span
                                style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">加</span>
                            </div>
                            <div
                              style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                              <span
                                style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">赠</span>
                            </div>
                          </div>
                          <div v-if="scope2.row.is_across"
                               style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                            <span
                              style="background-color:#154bea;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">跨</span>
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="set_meal" label="升级套餐" width="100" align="center">
                      <template #default="scope2">
                        <div style="display:flex;flex-direction:row;align-items: center;">
                          <span>{{ Data.meal_options[scope2.row.set_meal_upgrade] }}</span>
                          <div v-if="scope2.row.is_append_upgrade==1"
                               style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                            <span
                              style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">加</span>
                          </div>
                          <div v-if="scope2.row.is_append_upgrade==2"
                               style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                            <span
                              style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">赠</span>
                          </div>
                          <div v-if="scope2.row.is_append_upgrade==3" style="display: flex;flex-direction:row;">
                            <div
                              style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                              <span
                                style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">加</span>
                            </div>
                            <div
                              style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                              <span
                                style="background-color:#F59A23;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">赠</span>
                            </div>
                          </div>
                        </div>
                        <!--                      {{formatMealOptions(scope2.row.set_meal)}}-->
                      </template>
                    </el-table-column>
                    <el-table-column prop="status" label="状态" width="50" align="center">
                      <template #default="scope2">
                        <el-tag v-if="scope2.row.status == 1" type="success">正常</el-tag>
                        <el-tag v-else type="danger">无效</el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="use_type" label="设备用途" width="150" align="center">
                      <template #default="scope2">
                        {{ formatOptions(scope2.row.use_type, Data.use_type_options) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="region_name" label="地区" width="200" align="center"/>
                    <el-table-column prop="hospital_name" label="单位" width="150" align="center"/>
                    <el-table-column prop="authorize_time" label="授权时间" width="180" align="center">
                      <template #default="scope2">
                        <span v-if="scope2.row.authorize_time > 0">
                        {{ dayjs(scope2.row.authorize_time * 1000).format("YYYY-MM-DD HH:mm:ss") }}
                        </span>
                        <span v-else>未授权</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="activate_time" label="激活时间" width="180" align="center">
                      <template #default="scope2">
                        <span v-if="scope2.row.activate_time > 0">
                        {{ dayjs(scope2.row.activate_time * 1000).format("YYYY-MM-DD HH:mm:ss") }}
                        </span>
                        <span v-else>未激活</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </template>
            </el-table-column>
            <el-table-column v-if="checkList.includes('订单类型')" label="订单类型" align="center" prop="order_type">
              <template #default="scope">
                <div style="display:flex;flex-direction:row;align-items: center;">
                  <span v-if="scope.row.is_upgrade || scope.row.prev_order_number != ''">升级订单</span>
                  <span v-else>销售订单</span>
                  <div v-if="scope.row.is_urgent"
                       style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                    <span
                      style="background-color:red;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">急</span>
                  </div>
                  <!--                  <div v-if="!checkOrderHospitalIn(scope.row)" style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">-->
                  <!--                    <span style="background-color:#154bea;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">跨</span>-->
                  <!--                  </div>-->
                  <div v-if="scope.row.is_across"
                       style="height:30px;width:30px;margin-left:3px;display:flex;align-items:center;justify-content:center;">
                    <span
                      style="background-color:#154bea;color:white;width:20px;height:20px;border-radius:3px;display:flex;align-items:center;justify-content:center;">跨</span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column v-if="checkList.includes('订单编号')" label="订单编号" align="center" prop="order_number"/>
            <el-table-column v-if="checkList.includes('升级前订单编号')" label="升级前订单编号" align="center"
                             prop="prev_order_number"/>
            <el-table-column v-if="checkList.includes('合同号')" label="合同号" align="center" prop="contract_number"/>
            <el-table-column v-if="checkList.includes('创建者')" label="创建者" align="center" prop="operator_name"/>
            <el-table-column v-if="checkList.includes('单位')" label="单位" align="center" prop="hospital_name"/>
            <el-table-column v-if="checkList.includes('销售')" label="销售" align="center" prop="sale_name"/>
            <el-table-column v-if="checkList.includes('UKey代码')" label="UKey代码" align="center" prop="ukey_code"/>
            <el-table-column v-if="checkList.includes('设备数量')" label="设备数量" align="center" prop="count"/>
            <el-table-column v-if="checkList.includes('订单状态')" label="订单状态" align="center" prop="status">
              <template #default="scope">
                <el-tag v-if="scope.row.status == 1 || scope.row.status == 5">{{
                    formatOptions(scope.row.status,
                      Data.status_options)
                  }}
                </el-tag>
                <el-tag v-if="scope.row.status == 9" type="success">{{
                    formatOptions(scope.row.status,
                      Data.status_options)
                  }}
                </el-tag>
                <el-tag v-if="scope.row.status == 0 || scope.row.status == 2" type="warning">
                  {{ formatOptions(scope.row.status, Data.status_options) }}
                </el-tag>
                <el-tag v-if="scope.row.status == 3 || scope.row.status == 6" type="danger">
                  {{ formatOptions(scope.row.status, Data.status_options) }}
                </el-tag>
                <el-tag v-if="scope.row.status == 7 || scope.row.status == 8" type="info">
                  {{ formatOptions(scope.row.status, Data.status_options) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column v-if="checkList.includes('更新时间')" label="更新时间" align="center" prop="update_at"/>
          </el-table>
          <el-pagination
            class="flex justify-end mt-4"
            :small="size === 'small' ? true : false"
            v-model:page-size="Data.search_from_upgrade.page_size"
            :page-sizes="[10, 20, 30, 50]"
            :background="true"
            layout="total, sizes, prev, pager, next, jumper"
            :total="Data.search_from_upgrade.total"
            @size-change="handleSizeChangeUpgrade"
            @current-change="handleCurrentChangeUpgrade"/>
        </template>
      </EpTableProBar>
      <template #footer>
        <span class="dialog-footer">
          <el-button style="width:100px" type="primary" @click="submitUpgradeDevices">确认设备</el-button>
          <el-button style="width:100px" @click="cancleUpgradeDevices">取消</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="Data.dialogVisible_CreateHospital"
               title="添加医院"
               width="370px"
               draggable
               :close-on-click-modal="false"
               @close="cancelHospitalForm(ruleFormRef_create_hospital)">
      <el-form
        ref="ruleFormRef_create_hospital"
        :model="Data.hospital_data_from"
        :rules="Data.hospital_rules"
        label-width="80px">
        <el-form-item label="医院名称" prop="name">
          <el-input v-model="Data.hospital_data_from.name" @change="checkHospitalName" show-word-limit maxlength="20"/>
        </el-form-item>
        <el-form-item label="详细地址" prop="address">
          <el-input v-model="Data.hospital_data_from.address" show-word-limit maxlength="50"/>
        </el-form-item>
        <el-form-item label="联系人" prop="liaison">
          <el-input v-model="Data.hospital_data_from.liaison" show-word-limit maxlength="20"/>
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="Data.hospital_data_from.phone"/>
        </el-form-item>
        <el-form-item label="所属区域" prop="region_id">
          <el-cascader style="width: 250px;" :options="Data.agent_region_tree" @change="checkHospitalName"
                       v-model="Data.hospital_data_from.region_id"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitHospitalForm(ruleFormRef_create_hospital)">提交</el-button>
          <el-button @click="cancelHospitalForm(ruleFormRef_create_hospital)">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog v-model="Data.dialogVisible_CreateAgent"
               title="添加代理商"
               width="500px"
               top="50px"
               draggable
               :close-on-click-modal="false"
               @close="cancelAgentForm(ruleFormRef_create_agent)">
      <el-form
        ref="ruleFormRef_create_agent"
        :model="Data.agent_data_from"
        :rules="Data.agent_rules"
        label-width="120px">
        <el-form-item label="代理商名称" prop="name">
          <el-input v-model="Data.agent_data_from.name" show-word-limit maxlength="20" @change="checkAgentName"/>
        </el-form-item>
        <el-form-item label="代理商类型" prop="type">
          <el-radio-group v-model="Data.agent_data_from.type">
            <el-radio v-for="(item,index) in Data.agent_type_options" :label="item.value" :value="item.value"
                      @click="handleAgentType(item.value)">{{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="代理商时限" prop="time">
          <el-date-picker
            v-model="Data.agent_data_from.time"
            type="daterange"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </el-form-item>
        <el-form-item label="通讯地址" prop="address">
          <el-input v-model="Data.agent_data_from.address" show-word-limit maxlength="50"/>
        </el-form-item>
        <el-form-item label="联系人" prop="liaison">
          <el-input v-model="Data.agent_data_from.liaison" show-word-limit maxlength="20"/>
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="Data.agent_data_from.phone"/>
        </el-form-item>
        <el-form-item label="联系邮箱" prop="email">
          <el-input v-model="Data.agent_data_from.email"/>
        </el-form-item>
        <el-form-item :label="Data.agent_data_from.region_id.length>1?'代理商区域1':'代理商区域'" prop="region_id">
          <div class="regions">
            <el-cascader :props="propsSearch" :options="Data.agent_region_tree"
                         v-model="Data.agent_data_from.region_id[0]" style="width:200px" clearable/>
            <el-button v-if="Data.agent_data_from.region_id?.length>1" type="primary"
                       size="small" circle :icon="useRenderIcon('remove')" @click="handleDelRegion(0)"></el-button>
            <el-button type="primary" size="small" circle :icon="useRenderIcon('adds')"
                       @click="handleAddRegion()"></el-button>
          </div>
        </el-form-item>
        <div v-for="(v,k) in Data.agent_data_from.region_id">
          <el-form-item v-if="k>0" :label="'代理商区域'+(k+1)" prop="region_id">
            <div class="regions">
              <el-cascader :props="propsSearch" :options="Data.agent_region_tree"
                           v-model="Data.agent_data_from.region_id[k]" style="width:200px" clearable/>
              <el-button type="primary" size="small" circle :icon="useRenderIcon('remove')"
                         @click="handleDelRegion(k)"></el-button>
            </div>
          </el-form-item>
        </div>
        <el-form-item>
          <el-button type="primary" @click="submitAgentForm(ruleFormRef_create_agent)">提交</el-button>
          <el-button @click="cancelAgentForm(ruleFormRef_create_agent)">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-drawer v-model="Data.EditDialogModify" title="修改信息" :show-close="true" :modal="false">

      <el-form label-width="120px">

        <el-form-item label="代理商区域">
          <el-cascader :props="propsSearch" :options="Data.bind_region_tree" v-model="_self.data_from.region_id"
                       @change="changeBindRegionHandle" clearable/>
        </el-form-item>
        <el-form-item label="代理商">
          <el-select v-model="_self.data_from.agent_id" placeholder="请选择代理商" @change="changeAgentHandle" clearable>
            <el-option v-for="(item,index) in Data.agent_list"
                       :key="item.group"
                       :label="item?.date_status>0?(item.date_status==1?item.name+'（临近）':item.name+'（过期）'):item.name"
                       :value="item.group"/>
          </el-select>
        </el-form-item>

        <el-form-item label="代理商负责人" prop="principal">
          <el-input v-model="_self.data_from.principal" show-word-limit maxlength="20"/>
        </el-form-item>

        <el-form-item label="负责人电话" prop="principal_phone">
          <el-input  v-model="_self.data_from.principal_phone"  @input="_self.data_from.principal_phone=_self.data_from.principal_phone.replace(/[^1(\d)]/g, '')" maxlength="11"/>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleEditServer(_self.data_from)">提交</el-button>
          <el-button @click="Data.EditDialogModify=false">取消</el-button>
        </el-form-item>
      </el-form>

    </el-drawer>


  </el-row>
</template>
<style scoped lang="scss">
.hongquanIs{
  opacity: 0.5;
  pointer-events: none;
  cursor: not-allowed;
}
.mag {
  margin-left: 10px;
}

.feed {
  background-color: rgba(0, 168, 148, 0.2);
  color: #00A894;
  border: solid 1px #00A894;
}

.group {
  background-color: rgba(112, 0, 196, 0.2);
  color: #7000C4;
  border: solid 1px #7000C4;
}

.hq-bnt {
  width: 20px;
  height: 20px;
  border-radius: 3px;
  padding: 0px 2px;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}


.el-form-item {
  padding: 5px;
}

.device-item .el-form-item {
  padding: 0;
}

.divice-tab {
  margin: 10px 0 30px 30px;
}

.divice-tab > .el-tabs__content {
  margin: 5px;
}

.item-title {
  border-left: 3px solid var(--el-color-primary);
  height: 17px;
  line-height: 17px;
  padding-left: 5px;
  font-size: 15px;
  margin: 20px 0 10px 0;
  font-size: 17px;
  color: var(--el-color-primary);
}

.item-line {
  height: 15px;
  width: calc(100% + 40px);
  background-color: rgba(59, 130, 246, 0.1);
  margin-left: -20px;
  margin-top: 15px;
}

.order-details .el-row .el-col {
  padding: 10px 0;
}

:deep(.el-collapse .el-collapse-item__header) {
  color: #00bfb5;
  font-weight: bold;
}

:deep(.order-create-collapse .el-collapse-item__header) {
  padding-left: 20px;
}

:deep(.device-details-collapse .el-collapse-item__header) {
  padding-left: 16px;
}

:deep(.order-details-collapse .el-collapse-item__header) {
  padding-left: 12px;
}

:deep(.upgrade-device) {
  height: 900px !important;
}

:deep(.device-list .bg-white .w-full) {
  display: none;
}

:deep(.device-list .bg-white .el-empty) {
  padding: 10px 0 !important;
}

:deep(.order-create-collapse .el-collapse-item__arrow) {
  margin-right: 600px;
}

:deep(.device-details-collapse .el-collapse-item__arrow) {
  margin-right: 730px;
}

:deep(.order-details-collapse .el-collapse-item__arrow) {
  margin-right: 730px;
}

:deep(.receiver) {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

:deep(.select-upgrade) {
  margin-top: 0 !important;
}

.regions {
  display: flex;
  align-items: center;

  .el-button {
    margin-left: 10px;
  }
}

</style>
