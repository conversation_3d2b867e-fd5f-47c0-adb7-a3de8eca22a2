<script setup lang="ts">
import { getCurrentInstance } from "vue";
import favicon from "/@/assets/favicon.png";

const props = defineProps({
  collapse: Boolean
});

const title =
  getCurrentInstance().appContext.config.globalProperties.$config?.Title;
</script>

<template>
  <div class="sidebar-logo-container" :class="{ collapse: props.collapse }">
    <transition name="sidebarLogoFade">
      <router-link
        v-if="props.collapse"
        key="props.collapse"
        :title="title"
        class="sidebar-logo-link"
        to="/"
      >
<!--        <FontIcon icon="team-iconlogo" svg style="width: 35px; height: 35px" />-->
        <img class="logo-img" v-if="favicon" :src="favicon" alt="">
        <!-- <span class="sidebar-title">{{ title }}</span> -->
      </router-link>
      <router-link
        v-else
        key="expand"
        :title="title"
        class="sidebar-logo-link"
        to="/"
      >
        <img class="logo-img" v-if="favicon" :src="favicon" alt="">
        <!-- <span class="sidebar-title">{{ title }}</span> -->
      </router-link>
    </transition>
  </div>
</template>

<style lang="scss" scoped>
  .logo-img{
    height: 70%;
    margin: 3px 10px 0 0;
  }
.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 48px;
  text-align: center;
  overflow: hidden;

  .sidebar-logo-link {
    height: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-top: 5px;

    .sidebar-title {
      color: #FFFFFF;
      font-weight: 600;
      font-size: 20px;
      margin-top: 5px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
    }
  }

  .collapse {
    .sidebar-logo {
      margin-right: 0;
    }
  }
}
</style>
