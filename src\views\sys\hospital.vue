<script setup lang="ts">
  import dayjs from "dayjs";
  import { FormInstance, ElMessageBox } from "element-plus";
  import { reactive, ref, onMounted } from "vue";
  import { EpTableProBar } from "/@/components/ReTable";
  import { Switch, message } from "@pureadmin/components";
  import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
  import {
    hospitalListApi,
    hospitalAddApi,
    hospitalEditApi,
    hospitalDeleteApi,
    hospitalSetStatusApi,
    hospitalPersonalApi,
    agentListApi, agentPersonalApi, getUserRegionApi
  } from "/@/api/sys"
  import { getRegionApi } from "/@/api/public"
  import { Auth, AuthCount } from "/@/utils/auth";
  import {storageSession} from "/@/utils/storage";

  const formRef = ref<FormInstance>();
  const ruleFormRef = ref<FormInstance>()
  let switchLoadMap = ref({});
  const Data =  reactive({
    dialogVisible: false,
    search_from: {
      name: "",
      region_id: "",
      status: "",
      page_size: 10,
      page: 1,
      total: 0,
    },
    loading: true,
    data_list: [],
    region_tree: [],
    edit_region_tree: [],
    user_region_tree: [],
    data_from: {
      id: 0,
      name: "",
      license_code: "",
      address: "",
      liaison: "",
      phone: "",
      region_id: [],
    },
    rules: {
      name: [
        {required: true, message: '医院名称必填', trigger: 'blur'},
        {min: 1, max: 20, message: '医院名称长度限定为20个字符以内', trigger: ['blur', 'change']},
      ],
      region_id: [
        {required: true, message: '所属区域必填', trigger: ['blur', 'change']},
      ],
      address: [
        {required: true, message: '详细地址必填', trigger: ['blur', 'change']},
      ],
      phone: [
        {validator: checkPhone, trigger: ['blur', 'change']},
      ]
    },
    user_info: null,
  })

  //校验联系电话
  function checkPhone(rule, value, callback) {
    if (value) {
      let isPhone = new RegExp("^1[34578][0-9]{9}$",'i')
      let isLand = new RegExp("^(0[0-9]{2,3}-)([0-9]{7,8})$",'i')
      if( isPhone.test(value) || isLand.test(value) ){
        callback();
      }else{
        callback(new Error('电话格式异常'));
      }
    }else {
      callback();
    }
  }

  async function handleDelete(row) {
    let { code } = await hospitalDeleteApi({ids: [row.id]});
    if(code === 0){
      message.success("删除成功")
      onSearch()
    }
  }
  function handleCurrentChange(val: number) {
    Data.search_from.page = val
    onSearch()
  }
  function handleSizeChange(val: number) {
    Data.search_from.page_size = val
    onSearch()
  }
  function handleSelectionChange(val) {
    console.log("handleSelectionChange", val);
  }

  function onChange(checked, { $index, row }) {
    ElMessageBox.confirm(
      `确认要<strong>${
        row.status === 0 ? "停用" : "启用"
      }</strong><strong style='color:var(--el-color-primary)'>${
        row.name
      }</strong>吗?`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    )
      .then(() => {
        switchLoadMap.value[$index] = Object.assign({}, switchLoadMap.value[$index], {loading: true});
        hospitalSetStatusApi({id: row.id, status: row.status}).then(ret => {
          switchLoadMap.value[$index] = Object.assign({}, switchLoadMap.value[$index], {loading: false});
          if(ret.code === 0){
            message.success("修改成功");
          }
          onSearch()
        })

      })
      .catch(() => {
        row.status === 0 ? (row.status = 1) : (row.status = 2);
      });
  }
  async function onSearch() {
    Data.loading = true;
    //根据用户权限，判断查询全部医院，还是用户个人创建医院
    let response
    if(Auth('hospital/list')) {
      response = await hospitalListApi(Data.search_from);
    }else {
      response = await hospitalPersonalApi(Data.search_from);
    }
    // let { data } = await hospitalListApi(Data.search_from);
    let data = response.data
    if(data.total <= Data.search_from.page_size &&  Data.search_from.page > 1){
      Data.search_from.page = 1
      onSearch()
    }
    Data.data_list = data.list;
    Data.search_from.total = data.total;
    Data.loading = false;
  }
  const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async(valid, fields) => {
      if (valid) {
         if(Data.data_from.id > 0){
           let { code, msg } = await hospitalEditApi(Data.data_from);
           if(code === 0){
              message.success(msg)
              Data.dialogVisible = false
              onSearch()
           }
         }else{
           let { code, msg } = await hospitalAddApi(Data.data_from);
           if(code === 0){
             message.success(msg)
             Data.dialogVisible = false
             onSearch()
           }
         }
      }
    })
  }

  const cancelForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    Data.dialogVisible = false
    formEl.resetFields()
  }

  function handleUpdate(row) {
    Data.edit_region_tree = JSON.parse(JSON.stringify(Data.region_tree))
    if(row != null ) {
      Data.data_from = JSON.parse(JSON.stringify(row))
      if(typeof Data.data_from.region_id == "string") {
        let region_bit_arr = row.region_id.split("")
        if(region_bit_arr.length != 6){
          return
        }

        Data.data_from.region_id = [
          region_bit_arr[0]+region_bit_arr[1]+"0000",
          region_bit_arr[0]+region_bit_arr[1]+region_bit_arr[2]+region_bit_arr[3]+'00',
          row.region_id
        ]
      }
    } else {
      Data.data_from = {
        id: 0,
        name: "",
        license_code: "",
        address: "",
        liaison: "",
        phone: "",
        region_id: [],
      }
    }
    Data.dialogVisible = true
  }

  async function getRegion() {
    let { data } = await getRegionApi();
    Data.region_tree = data;
    getUserRegion()
  }

  //获取用户区域树
  async function getUserRegion(){
    //显示用户区域
    let { code, data } = await getUserRegionApi();
    if(code === 0) {
      Data.user_region_tree = data?.tree
      if(!Auth("hospital/list")){
        Data.region_tree = JSON.parse(JSON.stringify(data?.tree))
      }
    }
  }

  onMounted(() => {
    Data.user_info = storageSession.getItem("info")
    getRegion()
    onSearch();
  });
</script>

<template>
  <div class="main">
    <el-form ref="formRef" :inline="true" :model="Data.search_from" class="bg-white w-99/100 pl-8 pt-4">
      <el-form-item label="医院名称：" prop="name">
        <el-input style="width: 200px;" v-model="Data.search_from.name" placeholder="请输入用户名称" clearable />
      </el-form-item>
      <el-form-item label="所属区域" prop="region_id">
        <el-cascader :options="Data.region_tree"  v-model="Data.search_from.region_id" />
      </el-form-item>
      <el-form-item label="状态：" prop="status">
        <el-select v-model="Data.search_from.status" placeholder="请选择状态" clearable>
          <el-option label="已开启" value="1" />
          <el-option label="已关闭" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('search')"
          :loading="Data.loading"
          @click="onSearch">搜索</el-button>
        <el-button :icon="useRenderIcon('refresh')" @click="resetForm(formRef)">重置</el-button>
      </el-form-item>
    </el-form>

    <EpTableProBar
      title="医院列表"
      :loading="Data.loading"
      :columnList="[
        {label: '医院编号', show: true},
        {label: '医院名称', show: true},
        {label: '所属区域', show: true},
        {label: '详细地址', show: true},
        {label: '营业执照', show: false},
        {label: '状态', show: true},
        {label: '创建时间', show: true},
        {label: '更新时间', show: false},
        {label: '创建者', show: true},
        ]"
      :dataList="Data.data_list"
      @refresh="onSearch">
      <template #buttons>
        <el-button v-if="Auth('hospital/add')" type="primary" :icon="useRenderIcon('add')" @click="handleUpdate(null)">新增医院</el-button>
      </template>
      <template v-slot="{ size, checkList }">
        <el-table
          border
          table-layout="auto"
          :size="size"
          :data="Data.data_list"
          :header-cell-style="{ background: '#fafafa', color: '#606266' }"
          @selection-change="handleSelectionChange">
          <el-table-column v-if="checkList.includes('医院编号')" label="医院编号" align="center" prop="id" />
          <el-table-column v-if="checkList.includes('医院名称')" label="医院名称" align="center" prop="name" />
          <el-table-column v-if="checkList.includes('所属区域')" label="所属区域" align="center" width="250" prop="region_name" />
          <el-table-column v-if="checkList.includes('详细地址')" label="详细地址" align="center" width="400" prop="address" />
          <el-table-column v-if="checkList.includes('营业执照')" label="营业执照" align="center" prop="phone" />
          <el-table-column v-if="checkList.includes('状态')" label="状态" align="center" width="130" prop="status">
            <template #default="scope">
              <Switch
                :disabled="!Auth('hospital/set-status')"
                :size="size === 'small' ? 'small' : 'default'"
                :loading="switchLoadMap[scope.$index]?.loading"
                v-model:checked="scope.row.status"
                :checkedValue="1"
                :unCheckedValue="0"
                checked-children="已开启"
                un-checked-children="已关闭"
                @change="checked => onChange(checked, scope)"
              />
            </template>
          </el-table-column>
          <el-table-column v-if="checkList.includes('创建时间')" label="创建时间" align="center" width="170" prop="create_at"/>
          <el-table-column v-if="checkList.includes('更新时间')" label="更新时间" align="center" width="180" prop="update_at"/>
          <el-table-column v-if="checkList.includes('创建者')" label="创建者" align="center" prop="create_name"/>
          <el-table-column fixed="right" label="操作" width="180" align="center">
            <template #default="scope">
              <el-button
                v-if="Auth('hospital/edit')"
                class="reset-margin"
                type="text"
                :size="size"
                @click="handleUpdate(scope.row)"
                :icon="useRenderIcon('edits')">
                修改
              </el-button>
              <el-popconfirm title="是否确认删除?" @confirm="handleDelete(scope.row)">
                <template #reference>
                  <el-button
                    v-if="Auth('hospital/delete')"
                    class="reset-margin"
                    type="text"
                    :size="size"
                    :icon="useRenderIcon('delete')">
                    删除
                  </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          class="flex justify-end mt-4"
          :small="size === 'small' ? true : false"
          v-model:page-size="Data.search_from.page_size"
          :page-sizes="[10, 20, 30, 50]"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="Data.search_from.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"/>
      </template>
    </EpTableProBar>

    <el-dialog v-model="Data.dialogVisible"
      :title="Data.data_from.id == 0?'添加医院':'编辑医院'"
      width="370px"
      draggable
      :close-on-click-modal="false"
      @close="cancelForm(ruleFormRef)"
    >
      <el-form
        ref="ruleFormRef"
        :model="Data.data_from"
        :rules="Data.rules"
        label-width="80px">
        <el-form-item label="医院名称" prop="name">
          <el-input v-model="Data.data_from.name" show-word-limit maxlength="20"/>
        </el-form-item>
        <el-form-item label="详细地址" prop="address">
          <el-input v-model="Data.data_from.address" show-word-limit maxlength="50"/>
        </el-form-item>
        <el-form-item label="联系人" prop="liaison">
          <el-input v-model="Data.data_from.liaison" show-word-limit maxlength="20"/>
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="Data.data_from.phone" />
        </el-form-item>
        <el-form-item label="所属区域" prop="region_id">
          <el-cascader style="width: 250px;" :options="Data.user_region_tree"  v-model="Data.data_from.region_id" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm(ruleFormRef)">提交</el-button>
          <el-button @click="cancelForm(ruleFormRef)">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

  </div>
</template>
<style scoped lang="scss">
  :deep(.el-dropdown-menu__item i) {
    margin: 0;
  }

</style>
