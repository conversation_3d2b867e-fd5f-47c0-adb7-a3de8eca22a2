<script setup lang="ts">
  import dayjs from "dayjs";
  import { Auth } from "/@/utils/auth"
  import { FormInstance, ElMessageBox, ElTree } from "element-plus";
  import { reactive, ref, onMounted } from "vue";
  import { EpTableProBar } from "/@/components/ReTable";
  import { message } from "@pureadmin/components";
  import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
  import ElTreeLine from "/@/components/ReTreeLine";
  import E from 'wangeditor'
  import {
    featureTreeApi,
    featureDetailsApi,
    featureAddApi,
    featureEditApi,
    featureDeleteApi,
    featureSetStatusApi,
    featureChildListApi,
    featureSetSortApi
  } from "/@/api/kl"
  import {uploadApi} from "/@/api/sys";


  const formRef = ref<FormInstance>();
  const ruleFormRef = ref<FormInstance>();
  const treeRef = ref<InstanceType<typeof ElTree>>();
  let switchLoadMap = ref({});
  let edit = ref({});

  let level_title = {
    1: '系统',
    2: '部位',
    3: '超声特征',
    4: '病理特征',
  }

  const Data =  reactive({
    dialogVisible_imgPreview: false,
    dialogVisible: false,
    dialog_title: '',
    search_from: {
      keyword: "",
      status: "",
    },
    loading: true,
    data_list: [],
    data_from: {
      id: 0,
      pid: 0,
      level: 0,
      invisible: false,
      p_name: "",
      name: "",
      name_en: "",
      define: "",
      define_en: "",
      diagnose: "",
      diagnose_en: "",
      consult: "",
      consult_en: "",
      other: "",
      other_en : "",
      img_list: []
    },
    file_list:[],
    file: null,
    curr_node: null,
    curr_node_row: {}, //记录当前编辑节点数据
    imgPreview_data: {},
  })

  async function handleDelete(node) {
    let { code } = await featureDeleteApi({ids: [node.data.id]});
    if(code === 0){
      message.success("删除成功")
      node.visible = false
    }
  }


  function onChange(row) {
    ElMessageBox.confirm(
      `确认要<strong>${
        row.status === 1 ? "停用" : "启用"
      }</strong><strong style='color:var(--el-color-primary)'>${
        row.name
      }</strong>吗?`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    )
      .then(() => {
        featureSetStatusApi({id: row.id, status: row.status == 1?0:1}).then(ret => {
          if(ret.code === 0){
            message.success("修改成功");
          }
          row.status === 0 ? (row.status = 1) : (row.status = 0);
        })

      })
      .catch(() => {
        // row.status === 0 ? (row.status = 1) : (row.status = 0);
      });
  }
  async function onSearch() {
    Data.loading = true;
    let { data } = await featureTreeApi(Data.search_from);
    if(data && data.length > 0){
      Data.data_list = data;
    }
    Data.loading = false;
  }

  const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async(valid, fields) => {
      if (valid) {
          if(Data.data_from.invisible ){
            Data.data_from.invisible = true
          }else{
            Data.data_from.invisible = false
          }
         if(Data.data_from.id > 0){
           let { code, msg } = await featureEditApi(Data.data_from);
           if(code === 0){
              message.success(msg)
              Data.dialogVisible = false
              Data.curr_node_row.name = Data.data_from.name
           }
         }else{
           let { code, data, msg } = await featureAddApi(Data.data_from);
           if(code === 0){
             message.success(msg)
             Data.dialogVisible = false

             if(Data.curr_node_row?.children && Data.curr_node_row.children.length > 0){
               let curr_index = 0
               Data.curr_node_row.children.forEach((item,index) =>{
                 if(item.sort == 0){
                   curr_index = index + 1
                 }
               })
               Data.curr_node_row.children.splice(curr_index, 0, {
                    'id': data.id,
                    'name': Data.data_from.name,
                    'name_en': Data.data_from.name_en,
                    'status': 1,
                    'children': null,
                    'level': Data.data_from.level,
                    'invisible': Data.data_from.invisible
               })

             }else{

               // Data.data_list.push({
               //   'id': data.id,
               //   'name': Data.data_from.name,
               //   'name_en': Data.data_from.name_en,
               //   'status': 1,
               //   'children': null,
               //   'level': Data.data_from.level,
               //   'invisible': Data.data_from.invisible
               // })

                onSearch()
             }
           }

           // Data.curr_node.expanded = true

         }
      }
    })
  }

  const cancelForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    Data.dialogVisible = false
    // formEl.resetFields()
  }

  function handleUpdate(row, type:string, node) {
    Data.curr_node = node
    Data.curr_node_row = row
    console.dir("Data.curr_node_row", Data.curr_node_row)

    cancelForm(ruleFormRef.value)
    Data.dialogVisible = true
    treeRef.value!.setCheckedKeys([], false)

    if(row != null ) {
      if(type == "edit"){
        Data.data_from = row
        Data.data_from.p_name = ""
        if(row.invisible == 1){
          Data.data_from.invisible = true
        }else{
          Data.data_from.invisible = false
        }
        featureDetails(row.id)
      }else{
        // 添加子节点
        Data.data_from = {
          id: 0,
          pid: row.id,
          level: row.level + 1,
          invisible: false,
          p_name: row.name,
          name: "",
          name_en: "",
          define: "",
          define_en: "",
          diagnose: "",
          diagnose_en: "",
          consult: "",
          consult_en: "",
          other: "",
          other_en : "",
          img_list:[]
        }
      }
    } else {
      Data.data_from = {
        id: 0,
        pid: 0,
        level: 0,
        invisible: false,
        p_name: "",
        name: "",
        name_en: "",
        define: "",
        define_en: "",
        diagnose: "",
        diagnose_en: "",
        consult: "",
        consult_en: "",
        other: "",
        other_en : "",
        img_list:[]
      }
      Data.file_list = []
    }

    let t =  Data.data_from.id == 0?'添加':'编辑'
    let title = '系统'
    if(row != null){
      if(type == "edit"){
        title = level_title[row.level]
      }else{
        title = level_title[row.level + 1]
      }

    }
    Data.dialog_title = t + title
  }


  function featureDetails(id) {
    featureDetailsApi({id: id}).then(ret =>{
      if(ret.code !== 0){
        return
      }
      let img_list = []
      if(ret.data.atlas && ret.data.atlas.length > 0){
        ret.data.atlas.forEach(item => {
          img_list.push({
            base_url:  item.base_url,
            ext:  item.ext,
            name: item.name,
            path: item.url,
            size: item.size,
            type: item.type + "",
          })
        })
      }
      Data.data_from.img_list = img_list
    })
  }

  onMounted(() => {
    onSearch();
  });

  function handleDrop(draggingNode, dropNode, dropType, ev){
    featureSetSortApi({pid: draggingNode.data.pid, id: draggingNode.data.id, compare_id: dropNode.data.id, t: dropType}).then(ret => {})
    console.log('tree drop:', dropNode.data.id, dropType)
  }

  function clickNodeHandel(node) {
    if(node.data.level != 3){
      return
    }
    const expanded = node.expanded
    featureChildListApi({pid: node.data.id}).then(ret=> {
      node.data.children = ret.data
      node.expanded = !expanded
    })
  }

  function handleBeforeFile(file){
    Data.file = file
  }

  function fileUpload(){
    console.dir(Data.file)
    if(["image/jpeg", "image/jpg", "image/png", "video/mp4"].indexOf(Data.file.type) === -1){
      message.error("不支持的文件格式")
      return
    }

    if(Data.file.size >= 10 * 1024 * 1024){
      message.error("上传文件不超过10M")
      return
    }
    const formData = new FormData();
    formData.append("scene", 'attachment/feature_ledge');
    formData.append('file',  Data.file);
    Data.loading = true
    uploadApi(formData).then(ret => {
      Data.loading = false

      //如果是视频文件  就读取缩略图并上传到服务器
      if(["video/mp4"].indexOf(Data.file.type) !== -1){
        getVideoImage(Data.file, (file)=>{
          Data.data_from.img_list.push({
            base_url: ret.data.base_url,
            ext:  ret.data.ext,
            name: ret.data.name,
            path: ret.data.path,
            size: ret.data.size,
            type: "3",
            base64: file.url
          })
        })
      }else{

        Data.data_from.img_list.push({
          base_url: ret.data.base_url,
          ext:  ret.data.ext,
          name: ret.data.name,
          path: ret.data.path,
          size: ret.data.size,
          type: "1",
        })
      }
    }).catch(err => {
      Data.loading = false
      message.error("文件上传失败")
    })
  }

  // 文件转 图片 关键函数  异步
  function getVideoImage(file, call) {
    if (file && file.type.indexOf('video/') == 0) {
      var video = document.createElement('video');
      video.src = URL.createObjectURL(file);
      video.addEventListener('loadeddata', function() {
        this.currentTime = 1
      })
      video.addEventListener('seeked', function () {
        this.width = this.videoWidth;
        this.height = this.videoHeight;
        var canvas = document.createElement('canvas');
        var ctx = canvas.getContext('2d');
        canvas.width = this.width;
        canvas.height = this.height;
        ctx.drawImage(this, 0, 0, canvas.width, canvas.height);
        var image = {
          url: canvas.toDataURL('image/jpeg', 1),
          width: this.width,
          height: this.height,
          currentTime: this.currentTime,
          duration: this.duration
        };
        canvas.toBlob(function(blob) {
          image.blob = blob;
          typeof call == 'function' ? call.call(file, image) : console.log(image);
        }, 'image/jpeg');
      });
    }
  }

  function handleRemoveFile(index) {
    Data.data_from.img_list.splice(index, 1)
  }

  const allowDrop = (draggingNode, dropNode, type) => {
    if (type === "inner") return;
    const draggingLabel = draggingNode.data.id;
    const dropNodeLabel = dropNode.data.id;
    let a = [];
    loop(Data.data_list, draggingLabel, (it, index, arr) => { a = arr; });
    if (a.some((it) => it.id === dropNodeLabel)) {
      return true;
    }
    return false;
  };

  function loop(data, key, callback) {
    data.forEach((item, index, arr) => {
      if (item.id === key) {
        return callback(item, index, arr);
      }
      if (item.children) {
        return loop(item.children, key, callback);
      }
    });
  }

  function imgPreviewHandel(row) {
      console.dir(row)
      Data.dialogVisible_imgPreview = true
      Data.imgPreview_data = row
  }
</script>

<template>
  <div class="main">
<!--    <el-form ref="formRef" :inline="true" :model="Data.search_from" class="bg-white w-99/100 pl-8 pt-4">-->
<!--      <el-form-item label="特征名称：" prop="keyword">-->
<!--        <el-input style="width: 200px;" v-model="Data.search_from.keyword" placeholder="请输入特征名称" clearable />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="状态：" prop="status">-->
<!--        <el-select v-model="Data.search_from.status" placeholder="请选择状态" clearable>-->
<!--          <el-option label="已开启" value="1" />-->
<!--          <el-option label="已关闭" value="0" />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item>-->
<!--        <el-button-->
<!--          type="primary"-->
<!--          :icon="useRenderIcon('search')"-->
<!--          :loading="Data.loading"-->
<!--          @click="onSearch">搜索</el-button>-->
<!--        <el-button :icon="useRenderIcon('refresh')" @click="resetForm(formRef)">重置</el-button>-->
<!--      </el-form-item>-->
<!--    </el-form>-->

    <EpTableProBar
      title="特征树"
      :loading="Data.loading"
      :columnList="[]"
      :dataList="Data.data_list"
      @refresh="onSearch">
      <template #buttons>
        <p style="position:relative;color: orangered;">提示：允许同级拖拽排序，禁止跨级拖拽。一级：系统，二级：部位，三级：超声特征，四级：病理特征</p>
        <el-button v-if="Auth('kl-feature/add')" type="primary" :icon="useRenderIcon('add')" @click="handleUpdate(null, 'add', null)">新增系统</el-button>
      </template>
      <template v-slot="{ size, checkList }">
<!--        default-expand-all-->
        <el-tree style="width: 100%"
                 class="tree"
                 draggable
                 :allow-drop="allowDrop"
                 @node-drop="handleDrop"
                 ref="treeRef"
                 :data="Data.data_list"
                 :props="{children: 'children',label: 'name'}"
                 node-key="id"
                 :indent="20"
        >
          <template v-slot:default="{ node }">
            <el-tree-line :node="node" :showLabelLine="true" @click="clickNodeHandel(node)">
              <template v-slot:node-label>
                <span v-if="node.data.status == 1" class="text-sm"><span v-if="node.data.invisible" style="color: rebeccapurple;margin-right: 20px;font-size: 12px;">未归类病理特征</span> {{node.label}}  </span>
                <span v-else style="color: rgba(0,0,0,.2);cursor: not-allowed;" class="text-sm"><span v-if="node.data.invisible" style="color: rebeccapurple;margin-right: 20px;font-size: 12px;">未归类病理特征</span>{{node.label}}</span>

                <div style="position:absolute;right: 0;cursor: pointer;">
                  <el-button
                    v-if="Auth('kl-feature/add') && node.data.level < 4 && node.data.invisible == 0 "
                    class="reset-margin"
                    type="text"
                    :size="size"
                    @click.stop="handleUpdate(node.data, 'add', node)"
                    :icon="useRenderIcon('add')">
                    添加
                  </el-button>
                  <el-button
                    class="reset-margin"
                    type="text"
                    :size="size"
                    @click.stop="handleUpdate(node.data, 'edit')"
                    :icon="useRenderIcon('edits')">
                    修改
                  </el-button>
                  <el-button
                    v-if="Auth('kl-feature/set-status')"
                    class="reset-margin"
                    type="text"
                    :size="size"
                    @click.stop="onChange(node.data)"
                    :icon="useRenderIcon('key2Line')">
                    <template v-if="node.data.status == 1">禁用</template>
                    <template v-else>启用</template>
                  </el-button>
                  <el-popconfirm v-if="Auth('kl-feature/delete')" title="是否确认删除?" @confirm="handleDelete(node)">
                    <template #reference>
                      <el-button
                        class="reset-margin"
                        type="text"
                        :size="size"
                        @click.stop="()=>{}"
                        :icon="useRenderIcon('delete')">
                        删除
                      </el-button>
                    </template>
                  </el-popconfirm>
                </div>
              </template>
            </el-tree-line>
          </template>
        </el-tree>
      </template>
    </EpTableProBar>

    <el-dialog v-model="Data.dialogVisible"
      :title="Data.dialog_title"
      width="900px"
      top="50px"
      draggable>
      <el-form
        v-loading="Data.loading"
        ref="ruleFormRef"
        :model="Data.data_from"
        :rules="{
              name: [
                {required: true, message: '特征中文名称必填', trigger: 'blur'},
                {min: 1, max: 50, message: '特征中文名称限定50字符长度', trigger: ['blur', 'change']},
              ],
               name_en: [
                {required: true, message: '特征英文名称必填', trigger: 'blur'},
                {min: 1, max: 50, message: '特征英文名称限定50字符长度', trigger: ['blur', 'change']},
              ]}"
        label-width="100px">
        <el-form-item label="上级名称" prop="p_name" v-if="Data.data_from.p_name != ''">
          <el-input disabled v-model="Data.data_from.p_name" />
        </el-form-item>
        <el-form-item label="中文名称" prop="name">
          <el-input  v-model="Data.data_from.name" show-word-limit maxlength="50" />
        </el-form-item>
        <el-form-item label="英文名称" prop="name_en">
          <el-input v-model="Data.data_from.name_en" show-word-limit maxlength="50" />
        </el-form-item>
        <el-form-item v-if="(Data.data_from.id == 0 && Data.data_from.level <= 2) || Data.data_from.invisible" label="病理特征">
          <el-checkbox :disabled="Data.data_from.id > 0" v-model="Data.data_from.invisible" label="未归类病理特征" border />
        </el-form-item>

        <el-form-item v-if="(Data.data_from.id > 0 && Data.data_from.level == 4) ||(Data.data_from.id==0 && Data.data_from.level == 4) || Data.data_from.invisible" label="图例" prop="define">
          <el-upload
            :multiple="false"
            :show-file-list="false"
            v-model:file-list="Data.file_list"
            :http-request="fileUpload"
            :before-upload="handleBeforeFile">
            <el-button type="primary">点击上传图片或短视频</el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持jpg、jpeg、png、mp4等文件格式，单个文件大小不超过10M
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <div style="margin-left: 100px;margin-bottom: 30px;">
          <table v-if="Data.data_from.img_list && Data.data_from.img_list.length > 0">
            <tr v-for="(item,index) in Data.data_from.img_list" style="height: 35px;">
              <td>{{item.name}}</td>
              <td>
                <el-select v-model="item.type" style="margin-left: 10px;width: 120px;" size="small"  placeholder="请选择">
                  <el-option label="典型超声图" value="1" />
                  <el-option label="病例解刨图" value="2" />
                  <el-option label="动态视频" value="3"/>
                </el-select>
              </td>
              <td>
                <el-button
                  style="margin-left: 10px;"
                  class="reset-margin"
                  type="text"
                  size="small"
                  @click.stop="imgPreviewHandel(item)"
                  :icon="useRenderIcon('ppt')">
                  预览
                </el-button>
              </td>
              <td>
                <el-popconfirm title="是否确认删除?" @confirm="handleRemoveFile(index)">
                  <template #reference>
                    <el-button
                      style="margin-left: 10px;"
                      class="reset-margin"
                      type="text"
                      size="small"
                      @click.stop="()=>{}"
                      :icon="useRenderIcon('delete')">
                      删除
                    </el-button>
                  </template>
                </el-popconfirm>
              </td>
            </tr>
          </table>
        </div>

        <el-form-item v-if="(Data.data_from.id > 0 && Data.data_from.level == 4) ||(Data.data_from.id==0 && Data.data_from.level == 4) || Data.data_from.invisible" label="定义" prop="define">
          <table style="width: 100%">
            <tr>
              <td style="padding-right: 5px;"><el-input type="textarea" v-model="Data.data_from.define" :autosize="{ minRows: 4, maxRows: 6}"  placeholder="定义(中文)" show-word-limit maxlength="2000" /></td>
              <td style="padding-left: 5px;"><el-input type="textarea" v-model="Data.data_from.define_en" :autosize="{ minRows: 4, maxRows: 6}" placeholder="定义(英文)" show-word-limit maxlength="2000" /></td>
            </tr>
          </table>
        </el-form-item>
        <el-form-item v-if="(Data.data_from.id > 0 && Data.data_from.level == 4) ||(Data.data_from.id==0 && Data.data_from.level == 4) || Data.data_from.invisible" label="超声诊断要点" prop="diagnose">
          <table style="width: 100%">
            <tr>
              <td style="padding-right: 5px;"><el-input type="textarea" v-model="Data.data_from.diagnose" :autosize="{ minRows: 4, maxRows: 6}"  placeholder="超声诊断要点(中文)" show-word-limit maxlength="2000" /></td>
              <td style="padding-left: 5px;"><el-input type="textarea" v-model="Data.data_from.diagnose_en" :autosize="{ minRows: 4, maxRows: 6}" placeholder="超声诊断要点(英文)" show-word-limit maxlength="2000" /></td>
            </tr>
          </table>
        </el-form-item>
        <el-form-item v-if="(Data.data_from.id > 0 && Data.data_from.level == 4) ||(Data.data_from.id==0 && Data.data_from.level == 4) || Data.data_from.invisible" label="预后咨询" prop="consult">
          <table style="width: 100%">
            <tr>
              <td style="padding-right: 5px;"><el-input type="textarea" v-model="Data.data_from.consult" :autosize="{ minRows: 4, maxRows: 6}"  placeholder="预后咨询(中文)" show-word-limit maxlength="2000" /></td>
              <td style="padding-left: 5px;"><el-input type="textarea" v-model="Data.data_from.consult_en" :autosize="{ minRows: 4, maxRows: 6}" placeholder="预后咨询(英文)" show-word-limit maxlength="2000" /></td>
            </tr>
          </table>
        </el-form-item>
        <el-form-item v-if="(Data.data_from.id > 0 && Data.data_from.level == 4) ||(Data.data_from.id==0 && Data.data_from.level == 4) || Data.data_from.invisible" label="备注" prop="other">
          <table style="width: 100%">
            <tr>
              <td style="padding-right: 5px;"><el-input type="textarea" v-model="Data.data_from.other" :autosize="{ minRows: 4, maxRows: 6}"  placeholder="备注(中文)" show-word-limit maxlength="500" /></td>
              <td style="padding-left: 5px;"><el-input type="textarea" v-model="Data.data_from.other_en" :autosize="{ minRows: 4, maxRows: 6}" placeholder="备注(英文)" show-word-limit maxlength="500" /></td>
            </tr>
          </table>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
           <el-button @click="cancelForm(ruleFormRef)">取消</el-button>
           <el-button type="primary" @click="submitForm(ruleFormRef)">提交</el-button>
        </span>
      </template>
    </el-dialog>


    <el-dialog v-model="Data.dialogVisible_imgPreview" :title="Data.imgPreview_data?.name">
      <img v-if="Data.imgPreview_data?.type!=3" :src="Data.imgPreview_data.base_url+Data.imgPreview_data.path" alt="">
      <video autoplay loop controls v-else :src="Data.imgPreview_data.base_url+Data.imgPreview_data.path"></video>
    </el-dialog>

  </div>
</template>
<style scoped lang="scss">
  :deep(.el-dropdown-menu__item i) {
    margin: 0;
  }
  .element-tree-node-label-wrapper{
    margin-right: 220px;
    cursor: move;
  }

</style>
