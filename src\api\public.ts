import { http } from "../utils/http";

// 获取验证码
export const getCaptchaApi = () => {
  return http.request("get", "public/captcha");
};

// 登录
export const loginApi = (data: object) => {
  return http.request("post", "public/login", { data });
};

export const refreshTokenApi = (data: object) => {
  return http.request("post", "public/refresh-token", { data });
};

//获取区域数据
export const getRegionApi = () => {
  return http.request("get", "public/get-region");
};

//获取ukey列表
export const ukeyListApi = () => {
  return http.request("get", "public/ukey-list");
};

//登录模式
export const loginQueryApi = (params?: object) => {
  return http.request("get", "public/query-login", { params });
};

// 获取短信验证码
export const getSmsApi = (params?: object) => {
  return http.request("get", "public/get-sms", { params });
};

// 短信登录
export const loginSmsApi = (data: object) => {
  return http.request("post", "public/sms-login", { data });
};

//获取用户绑定代理商区域数据
export const getBindRegionApi = () => {
  return http.request("get", "public/bind-region-tree");
};