import { $t } from "/@/plugins/i18n";
const Layout = () => import("/@/layout/index.vue");

const authorRouter = {
  path: "/author",
  name: "author",
  component: Layout,
  api: [],
  redirect: "/author/order",
  meta: {
    icon: "key2Line",
    title: "授权管理",
    rank: 2
  },
  children: [
    {
      path: "/order",
      name: "order",
      component: () => import("/@/views/auth/order.vue"),
      api: ['order/list', 'order/personal', 'order/create', 'order/details', 'order/upgrade', 'order/review', 'order/manager_review', 'order/deploy', 'order/transfer',  'order/revoke', 'order/delete', 'order/device_details', 'order/update-device'],
      meta: {
        title: "订单管理",
        icon: "attachment2"
      }
    },
    {
      path: "/licence",
      name: "licence",
      component: () => import("/@/views/auth/licence.vue"),
      api: ['licence/list', 'licence/set-status', 'licence/delete'],
      meta: {
        title: "授权记录",
        icon: "historyFill"
      }
    }
  ]
};

export default authorRouter;
