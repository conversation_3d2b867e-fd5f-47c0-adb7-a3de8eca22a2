<template>
  <el-config-provider :locale="currentLocale">
    <router-view />
  </el-config-provider>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import { ElConfigProvider } from "element-plus";
//import zhCn from "element-plus/lib/locale/lang/zh-cn";
//import en from "element-plus/lib/locale/lang/en";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import en from "element-plus/es/locale/lang/en";
export default defineComponent({
  name: "app",
  components: {
    [ElConfigProvider.name]: ElConfigProvider
  },
  computed: {
    currentLocale() {
      return this.$storage.locale?.locale === "zh" ? zhCn : en;
    }
  }
});
</script>

<style lang="scss">
.el-dialog__body{
  border-top: .5px solid rgba(0,0,0,.2);
}
.ant-message{
  z-index: 10000!important;
}
.el-loading-spinner .el-loading-text {
  margin: 110px 0!important;
  font-size: 20px!important;
  font-weight: bolder;
}
.el-loading-mask{
  background-image: url("/@/assets/loading.gif");
  background-repeat: no-repeat;
  background-position: center;
  /*background-position-y: calc(50vh - 180px);*/
}
.el-loading-spinner .circular{
  display: none!important;
}
.order-create .el-tabs__content{
  padding: 0!important;
  width: 100%;
}
.el-tabs--border-card>.el-tabs__header .el-tabs__item.is-disabled{
  color: lightgrey!important;
  cursor: not-allowed;
}
.el-tabs--border-card>.el-tabs__header .el-tabs__item.is-active{
  color: white!important;
  background-color: var(--el-color-primary)!important;
}
.el-dialog__body{
  max-height: calc(100vh - 230px) !important;
  overflow-y: auto!important;
  overflow-x: hidden;
}
/* 设置滚动条的样式 */
.el-dialog__body::-webkit-scrollbar {
  width:5px;
}
/* 滚动槽 */
/*.el-dialog__body::-webkit-scrollbar-track {*/
/*  -webkit-box-shadow:inset 0 0 6px rgba(0,0,0,0.3);*/
/*  border-radius:8px;*/
/*}*/
/* 滚动条滑块 */
.el-dialog__body::-webkit-scrollbar-thumb {
  border-radius:6px;
  background:rgba(0,0,0,0.1);
  -webkit-box-shadow:inset 0 0 6px rgba(0,0,0,0.2);
}
.el-dialog__body::-webkit-scrollbar-thumb:window-inactive {
  background:rgba(255,0,0,0.1);
}
</style>
