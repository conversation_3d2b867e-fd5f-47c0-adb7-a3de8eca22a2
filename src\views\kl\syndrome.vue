<script setup lang="ts">
  import dayjs from "dayjs";
  import { FormInstance, ElMessageBox, ElTree } from "element-plus";
  import { reactive, ref, onMounted } from "vue";
  import { EpTableProBar } from "/@/components/ReTable";
  import { message } from "@pureadmin/components";
  import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
  import ElTreeLine from "/@/components/ReTreeLine";
  import E from 'wangeditor'
  import { Auth } from "/@/utils/auth"
  import {
    syndromeTreeApi,
    syndromeAddApi,
    syndromeEditApi,
    syndromeDeleteApi,
    syndromeSetStatus<PERSON>pi,
    syndromeSetSortApi,
    featureGroupList<PERSON><PERSON>,
    syndromeType<PERSON><PERSON>,
    syndromeDetailsApi,
  } from "/@/api/kl"


  const formRef = ref<FormInstance>();
  const ruleFormRef = ref<FormInstance>();
  const treeRef = ref<InstanceType<typeof ElTree>>();
  let switchLoadMap = ref({});
  let edit = ref({});
  const Data =  reactive({
    dialogVisible: false,
    dialogVisible_feature_group: false,
    search_from: {
      keyword: "",
      status: "",
    },
    loading: true,
    data_list: [],
    feature_group: [],
    type_options: [],
    data_from: {
      id: 0,
      type: "",
      name: "",
      name_en: "",
      genetics_desc: "",
      genetics_desc_en: "",
      gene_location: "",
      gene_location_en: "",
      diagnose: "",
      diagnose_en: "",
      consult: "",
      consult_en: "",
      features: [],
    },
    feature_ids: [],
    feature_ids_type_map: {},
    modalKey: 0,
    curr_node_row: {} //记录当前编辑节点数据
  })

  async function handleDelete(node) {
    console.dir(node)
    let { code } = await syndromeDeleteApi({ids: [node.data.id]});
    if(code === 0){
      message.success("删除成功")
      node.visible = false
      // Data.data_list.forEach(item =>{
      //   item.children.forEach(item2 => {
      //     if()
      //   })
      // })
    }
  }


  function onChange(row) {
    ElMessageBox.confirm(
      `确认要<strong>${
        row.status === 1 ? "停用" : "启用"
      }</strong><strong style='color:var(--el-color-primary)'>${
        row.name
      }</strong>吗?`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    )
      .then(() => {
        syndromeSetStatusApi({id: row.id, status: row.status==1?0:1}).then(ret => {
          if(ret.code === 0){
            message.success("修改成功");
            row.status === 0 ? (row.status = 1) : (row.status = 0);
          }
        })

      })
      .catch(() => {
        // row.status === 0 ? (row.status = 1) : (row.status = 0);
      });
  }
  async function onSearch() {
    Data.loading = true;
    let { data } = await syndromeTreeApi(Data.search_from);
    if(data && data.length > 0){
      Data.data_list = data;
    }
    Data.loading = false;
  }

  const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async(valid, fields) => {
      if (valid) {
        const features = []
        Data.data_from.features.forEach(item => {
          if(item){
            features.push(item)
          }
        })
        Data.data_from.features = features
         if(Data.data_from.id > 0){
           let { code, msg } = await syndromeEditApi(Data.data_from);
           if(code === 0){
              message.success(msg)
              Data.dialogVisible = false
              Data.curr_node_row.name = Data.data_from.name
           }
         }else{
           let { code, data, msg } = await syndromeAddApi(Data.data_from);
           if(code === 0){
             message.success(msg)
             Data.dialogVisible = false

             if(Data.data_from.type.length >= 2 ){
                Data.data_list.forEach((item,index) =>{
                  item.children.forEach((item2,index2) => {
                    if(item.type == Data.data_from.type[0] && item2.sub_type == Data.data_from.type[1]){
                      let curr_index = 0
                      item2.children.forEach((item3,index3) => {
                        if(item3.sort == 0){
                          curr_index = index3 + 1
                        }
                      })
                      curr_index = item2.children?.length + 1
                      Data.data_list[index].children[index2].children.splice(curr_index, 0, {"id":data.id, 'name': Data.data_from.name, 'status': 1, 'children':null})
                      return
                    }
                  })
                })
             }else{
               Data.data_list.forEach((item,index) =>{
                 if(item.type == Data.data_from.type[0]){
                   let curr_index = 0
                   item.children.forEach((item2,index2) => {
                     if(item2.sort == 0){
                       curr_index = index3 + 1
                     }
                   })
                   curr_index = item.children?.length + 1
                   Data.data_list[index].children.splice(curr_index, 0, {"id":data.id, 'name': Data.data_from.name, 'status': 1, 'children':null})
                   return
                 }
               })
             }
           }
         }
      }
    })
  }

  const cancelForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    Data.dialogVisible = false
    formEl.resetFields()
  }

  function handleUpdate(row, type:string) {
    Data.curr_node_row = row
    cancelForm(ruleFormRef.value)
    Data.dialogVisible = true
    treeRef.value!.setCheckedKeys([], false)
    Data.feature_ids = []
    if(row != null && row.id > 0) {
      syndromeDetailsApi({id: row.id}).then(ret => {
        if(ret.code !== 0){
          return
        }
        Data.data_from = ret.data

        if(ret.data.sub_type && ret.data.sub_type > 0){
          Data.data_from.type = [ret.data.type, ret.data.sub_type]
        }else{
          Data.data_from.type = [ret.data.type]
        }

        Data.feature_ids = []
        Data.feature_ids_type_map = {}
        if(ret.data.syndrome_features && ret.data.syndrome_features.length > 0){
          ret.data.syndrome_features.forEach(item => {
            Data.feature_ids.push([item.feature_root_id, item.feature_id])
            Data.feature_ids_type_map[item.feature_id] = item.type
          })
        }
        Data.modalKey++
        featureIdsChangesHandel()
      })
    } else {
      Data.data_from = {
        id: 0,
        type: "",
        name: "",
        name_en: "",
        genetics_desc: "",
        genetics_desc_en: "",
        gene_location: "",
        gene_location_en: "",
        diagnose: "",
        diagnose_en: "",
        consult: "",
        consult_en: "",
        features: [],
      }
      Data.feature_ids = []
      Data.feature_ids_type_map = {}
    }
  }


  // function addFeatureHandel() {
  //   Data.dialogVisible_feature_group = true
  // }
  //
  // function submitFormFeature() {
  //   Data.dialogVisible_feature_group = false
  //   console.dir(Data.feature_group)
  // }

  function featureIdsChangesHandel() {
    Data.data_from.features = []
    Data.feature_group.forEach(item => {
      let features = []
      Data.feature_ids.forEach(item2 => {
        if(item2[0] == item['value']) {
          item.children.forEach(item3 => {
            if(item2[1] == item3.value){
              features.push({
                id: item3.value,
                name: item3.label,
                type: Data.feature_ids_type_map[item2[1]]??'2',
              })
            }
          })

          Data.data_from.features[item.value] = {
            id: item.value,
            name: item.label,
            features: features,
          }
        }
      })
    })
  }

  function featureTypeChangeHandel(id, type){
    Data.feature_ids_type_map[id] = type
  }

  function handleDeleteFeature(row, id){

    let index = -1
    Data.feature_ids.forEach((item,i) => {
      if(item[0] == row.id && item[1] == id){
        index = i
      }
    })

    if(index >= 0){
      Data.feature_ids.splice(index, 1)
      Data.modalKey++
      featureIdsChangesHandel()
    }
  }

  onMounted(() => {
    onSearch();
    featureGroupListApi().then(ret => {
      Data.feature_group = ret.data
    })
    syndromeTypeApi().then(ret => {
      Data.type_options = ret.data
    })
  });

  function handleDrop(draggingNode, dropNode, dropType, ev){
    syndromeSetSortApi({type: draggingNode.data.type, sub_type: draggingNode.data.sub_type, id: draggingNode.data.id, compare_id: dropNode.data.id, t: dropType}).then(ret => {})
    console.log('tree drop:', dropNode.data.id, dropType)
  }

  const allowDrop = (draggingNode, dropNode, type) => {
    if (type === "inner") return;
    const draggingLabel = draggingNode.data.id;
    const dropNodeLabel = dropNode.data.id;
    let a = [];
    loop(Data.data_list, draggingLabel, (it, index, arr) => { a = arr; });
    if (a.some((it) => it.id === dropNodeLabel)) {
      return true;
    }
    return false;
  };

  function loop(data, key, callback) {
    data.forEach((item, index, arr) => {
      if (item.id === key) {
        return callback(item, index, arr);
      }
      if (item.children) {
        return loop(item.children, key, callback);
      }
    });
  }
</script>

<template>
  <div class="main">
<!--    <el-form ref="formRef" :inline="true" :model="Data.search_from" class="bg-white w-99/100 pl-8 pt-4">-->
<!--      <el-form-item label="综合征名称：" prop="keyword">-->
<!--        <el-input style="width: 200px;" v-model="Data.search_from.keyword" placeholder="请输入综合征名称" clearable />-->
<!--      </el-form-item>-->
<!--      <el-form-item label="状态：" prop="status">-->
<!--        <el-select v-model="Data.search_from.status" placeholder="请选择状态" clearable>-->
<!--          <el-option label="已开启" value="1" />-->
<!--          <el-option label="已关闭" value="0" />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
<!--      <el-form-item>-->
<!--        <el-button-->
<!--          type="primary"-->
<!--          :icon="useRenderIcon('search')"-->
<!--          :loading="Data.loading"-->
<!--          @click="onSearch">搜索</el-button>-->
<!--        <el-button :icon="useRenderIcon('refresh')" @click="resetForm(formRef)">重置</el-button>-->
<!--      </el-form-item>-->
<!--    </el-form>-->

    <EpTableProBar
      title="综合征列表"
      :loading="Data.loading"
      :columnList="[]"
      :dataList="Data.data_list"
      @refresh="onSearch">
      <template #buttons>
        <p style="position:relative;color: orangered;">提示：允许同级拖拽排序，禁止跨级拖拽。</p>
        <el-button v-if="Auth('kl-syndrome/add')" type="primary" :icon="useRenderIcon('add')" @click="handleUpdate(null)">新增综合征</el-button>
      </template>
      <template v-slot="{ size, checkList }">
<!--        default-expand-all-->
        <el-tree style="width: 100%"
                 class="tree"
                 draggable
                 :allow-drop="allowDrop"
                 @node-drop="handleDrop"
                 ref="treeRef"
                 :data="Data.data_list"
                 :props="{children: 'children',label: 'name'}"
                 node-key="id"
                 :indent="20"
        >
          <template v-slot:default="{ node }">
            <el-tree-line :node="node" :showLabelLine="true">
              <template v-slot:node-label>
                <span v-if="node.data.status == 1" class="text-sm"> {{node.label}}  </span>
                <span v-else class="text-sm" style="color: rgba(0,0,0,.2);cursor: not-allowed;">{{node.label}}</span>

                <div v-if="node.data.id > 0" style="position:absolute;right: 0;cursor: pointer;">
                  <el-button
                    class="reset-margin"
                    type="text"
                    :size="size"
                    @click.stop="handleUpdate(node.data, 'edit')"
                    :icon="useRenderIcon('edits')">
                    修改
                  </el-button>
                  <el-button
                    v-if="Auth('kl-syndrome/set-status')"
                    class="reset-margin"
                    type="text"
                    :size="size"
                    @click.stop="onChange(node.data)"
                    :icon="useRenderIcon('key2Line')">
                    <template v-if="node.data.status == 1">禁用</template>
                    <template v-else>启用</template>
                  </el-button>
                  <el-popconfirm  v-if="Auth('kl-syndrome/delete')" title="是否确认删除?" @confirm="handleDelete(node)">
                    <template #reference>
                      <el-button
                        class="reset-margin"
                        type="text"
                        :size="size"
                        @click.stop="()=>{}"
                        :icon="useRenderIcon('delete')">
                        删除
                      </el-button>
                    </template>
                  </el-popconfirm>
                </div>
              </template>
            </el-tree-line>
          </template>
        </el-tree>
      </template>
    </EpTableProBar>

    <el-dialog v-model="Data.dialogVisible"
      :title="Data.data_from.id == 0?'添加综合征':'编辑综合征'"
      width="900px"
      top="50px"
      draggable>
      <el-form
        ref="ruleFormRef"
        :model="Data.data_from"
        :rules="{
              type: [
                {required: true, message: '类型必填', trigger: ['blur', 'change']},
              ],
              name: [
                {required: true, message: '特征中文名称必填', trigger: 'blur'},
                {min: 1, max: 50, message: '特征中文名称限定50字符长度', trigger: ['blur', 'change']},
              ],
               name_en: [
                {required: true, message: '特征英文名称必填', trigger: 'blur'},
                {min: 1, max: 50, message: '特征英文名称限定50字符长度', trigger: ['blur', 'change']},
              ]}"
        label-width="100px">
        <el-form-item label="类型" prop="type">
          <el-cascader :disabled="Data.data_from.id > 0"  v-model="Data.data_from.type" :options="Data.type_options" />
        </el-form-item>
        <el-form-item label="中文名称" prop="name">
          <el-input  v-model="Data.data_from.name" show-word-limit maxlength="50" />
        </el-form-item>
        <el-form-item label="英文名称" prop="name_en">
          <el-input v-model="Data.data_from.name_en" show-word-limit maxlength="50" />
        </el-form-item>
        <el-form-item label="遗传类型" prop="genetics_desc">
          <table style="width: 100%">
            <tr>
              <td style="padding-right: 5px;"><el-input v-model="Data.data_from.genetics_desc" placeholder="遗传类型（中文）" show-word-limit maxlength="50" /></td>
              <td style="padding-left: 5px;"><el-input v-model="Data.data_from.genetics_desc_en" placeholder="遗传类型（英文）" show-word-limit maxlength="50" /></td>
            </tr>
          </table>
        </el-form-item>
        <el-form-item label="基因点位" prop="gene_location">
          <table style="width: 100%">
            <tr>
              <td style="padding-right: 5px;"><el-input v-model="Data.data_from.gene_location" placeholder="基因点位(中文)" show-word-limit maxlength="50" /></td>
              <td style="padding-left: 5px;"><el-input v-model="Data.data_from.gene_location_en" placeholder="基因点位(英文)" show-word-limit maxlength="50" /></td>
            </tr>
          </table>
        </el-form-item>
        <el-form-item label="形态学异常" prop="features">
<!--          <el-button type="primary" plain @click="addFeatureHandel(ruleFormRef)">添加特征</el-button>-->
          <el-cascader :key="Data.modalKey" @change="featureIdsChangesHandel" v-model="Data.feature_ids" style="width: 100%" :options="Data.feature_group" :props="{multiple:true}" collapse-tags clearable />
          <el-table
            border
            table-layout="auto"
            :data="Data.data_from.features"
            :header-cell-style="{ background: '#fafafa', color: '#606266' }">
            <el-table-column label="系统" align="center" prop="name" />
            <el-table-column label="特征" align="left" prop="features">
              <template #default="scope">
                <div style="margin: 5px 0" v-for="item in scope.row.features">
                  <el-tag v-if="item.type==1" type="danger"  style="width: 300px;" effect="plain">{{item.name}}</el-tag>
                  <el-tag v-else  style="width: 300px;" effect="plain">{{item.name}}</el-tag>
                  <el-select @change="featureTypeChangeHandel(item.id, item.type)" v-model="item.type" style="margin-left: 10px;width: 120px;" size="small"  placeholder="请选择">
                    <el-option label="特征病变" value="1"/>
                    <el-option label="其它可见病变" value="2"/>
                  </el-select>

                  <el-popconfirm title="是否确认删除?" @confirm="handleDeleteFeature(scope.row, item.id)">
                    <template #reference>
                      <el-button
                        style="margin-left: 10px;"
                        class="reset-margin"
                        type="text"
                        size="small"
                        @click.stop="()=>{}"
                        :icon="useRenderIcon('delete')">
                        删除
                      </el-button>
                    </template>
                  </el-popconfirm>

                </div>
              </template>
            </el-table-column>
          </el-table>

        </el-form-item>
        <el-form-item label="超声诊断要点" prop="diagnose">
          <table style="width: 100%">
            <tr>
              <td style="padding-right: 5px;"><el-input type="textarea" :autosize="{ minRows: 4, maxRows: 6}" v-model="Data.data_from.diagnose" placeholder="超声诊断要点（中文）" show-word-limit maxlength="2000" /></td>
              <td style="padding-left: 5px;"><el-input type="textarea" :autosize="{ minRows: 4, maxRows: 6}" v-model="Data.data_from.diagnose_en" placeholder="超声诊断要点（英文）" show-word-limit maxlength="2000" /></td>
            </tr>
          </table>
        </el-form-item>
        <el-form-item label="预后咨询" prop="consult">
          <table style="width: 100%">
            <tr>
              <td style="padding-right: 5px;"><el-input type="textarea" :autosize="{ minRows: 4, maxRows: 6}" v-model="Data.data_from.consult" placeholder="预后咨询（中文）" show-word-limit maxlength="2000" /></td>
              <td style="padding-left: 5px;"><el-input type="textarea" :autosize="{ minRows: 4, maxRows: 6}" v-model="Data.data_from.consult_en" placeholder="预后咨询（英文）" show-word-limit maxlength="2000" /></td>
            </tr>
          </table>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
           <el-button @click="cancelForm(ruleFormRef)">取消</el-button>
           <el-button type="primary" @click="submitForm(ruleFormRef)">提交</el-button>
        </span>
      </template>
    </el-dialog>


<!--    <el-dialog v-model="Data.dialogVisible_feature_group"-->
<!--               title="选择特征"-->
<!--               width="600px"-->
<!--               top="50px"-->
<!--               draggable>-->
<!--      <el-form v-loading="Data.loading">-->
<!--        <el-collapse accordion>-->
<!--        <el-collapse-item v-for="item in Data.feature_group">-->
<!--          <template #title>-->
<!--            {{item.label}} （{{item.check.length}} / {{item.children.length}}）-->

<!--          </template>-->
<!--          <table style="width: 100%">-->
<!--            <tr v-for="item2 in item.children" style="height: 40px;">-->
<!--              <td>-->
<!--                <el-checkbox style="width: 400px;" v-model="item.check" :model-value="item2.value" :label="item2.label" border />-->
<!--              </td>-->
<!--              <td>-->
<!--                <el-select  v-model="item2.type" placeholder="请选择">-->
<!--                  <el-option label="特征病变" value="1"/>-->
<!--                  <el-option label="其它可见病变" value="2"/>-->
<!--                </el-select>-->
<!--              </td>-->
<!--            </tr>-->
<!--          </table>-->

<!--        </el-collapse-item>-->
<!--      </el-collapse>-->
<!--      </el-form>-->
<!--      <template #footer>-->
<!--        <span class="dialog-footer">-->
<!--           <el-button @click="Data.dialogVisible_feature_group = false">取消</el-button>-->
<!--           <el-button type="primary" @click="submitFormFeature()">提交</el-button>-->
<!--        </span>-->
<!--      </template>-->
<!--    </el-dialog>-->


  </div>
</template>
<style scoped lang="scss">
  :deep(.el-dropdown-menu__item i) {
    margin: 0;
  }
  .element-tree-node-label-wrapper{
    margin-right: 160px;
    cursor: move;
  }

</style>
