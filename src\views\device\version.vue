<script setup lang="ts">
  import dayjs from "dayjs";
  import {FormInstance, ElMessageBox} from "element-plus";
  import {reactive, ref, onMounted} from "vue";
  import {EpTableProBar} from "/@/components/ReTable";
  import {Switch, message} from "@pureadmin/components";
  import {useRenderIcon} from "/@/components/ReIcon/src/hooks";
  import {
    versionListApi,
    versionAddApi,
    versionEditApi,
    versionDeleteApi,
    versionSetStatusApi,
    versionSwitchApi
  } from "/@/api/version"
  import {uploadApi} from "/@/api/sys";
  import {formatOptions} from "/@/utils/tool";
  import {Auth} from "/@/utils/auth";
  import OSS from 'ali-oss'
  import {errorMessage} from "/@/utils/message";
  import {orderMealOptionsApi} from "/@/api/order";
  import router from "/@/router";

  const formRef = ref<FormInstance>();
  const ruleFormRef = ref<FormInstance>()
  let switchLoadMap = ref({});
let client
let selectedIds = [];
let selectedMeals = ref([]);

let selectedId = null
let selectedMeal = ref(null);
const new_meal_options_select = {
  multiple: true,
  value: 'id', // 对应数据中的值
  label: 'name', // 对应显示的名称
  children: 'm_val' // 对应子级数据
}
  const Data = reactive({
    dialogVisible: false,
    channel_options: [
      {value: 0, label: '全部'},
      {value: 1, label: '正式版'},
      {value: 2, label: '试用版'},
    ],
    // channel_options_: [
    //   {value: 1, label: '正式'},
    //   {value: 2, label: '测试'},
    // ],
    status_options: [
      {value: 0, label: '全部'},
      {value: 1, label: '启用'},
      {value: 2, label: '禁用'},
    ],
    search_from: {
      keyword: "",
      channel: 0,
      status: undefined,
      page_size: 10,
      page: 1,
      total: 0,
    },
    loading: true,
    data_list: [],

    version_options_official: [],
    version_options_test: [],
    version_options_all: [],
    new_meal_options: [],  // 新增
    new_meal_options_array: [], // 新增
    data_from: {
      id: 0,
      channel: 0,
      version_number: '',
      info: '',
      bug_fix: '',
      remark: '',
      update_range: '',
      update_range_official: [],
      update_range_test: [],
      update_range_all: [],
      full_url: '',
      package_url: '',
      progress: 0,
    },
    meal_options: {},
    file: null,
    rules: {
      channel: [
        {required: true, message: '发布渠道必填', trigger: 'change'},
      ],
      version_number: [
        {required: true, message: '版本名称必填', trigger: 'change'},
        {
          pattern: /^([1-9]\d|[1-9])(\.([1-9]\d|\d)){2}(\.([0-9]{6}))$/,
          message: '版本号格式异常，形如：1.0.17.000420',
          trigger: 'blur'
        }
      ],
      info: [
        {required: true, message: '发布信息必填', trigger: 'change'},
        {max: 1000, message: '发布信息不能超过1000个字符', trigger: 'blur'},
      ],
      bug_fix: [
        {max: 1000, message: '不能超过1000个字符', trigger: 'blur'},
      ],
      remark: [
        {max: 500, message: '不能超过500个字符', trigger: 'blur'}
      ],
      // update_range: [
      //   { required: true, message: '更新版本范围必填', trigger: 'blur' },
      // ],
      package_url: [
        {required: true, message: '必须上传更新文件', trigger: 'change'}
      ],
    },
    rules_edit: {
      channel: [
        {required: true, message: '发布渠道必填', trigger: 'change'},
      ],
      version_number: [
        {required: true, message: '版本名称必填', trigger: 'change'},
        {
          pattern: /^([1-9]\d|[1-9])(\.([1-9]\d|\d)){2}(\.([0-9]{6}))$/,
          message: '版本号格式异常，形如：1.0.17.000420',
          trigger: 'blur'
        }
      ],
      info: [
        {required: true, message: '发布信息必填', trigger: 'change'},
        {max: 1000, message: '发布信息不能超过1000个字符', trigger: 'blur'},
      ],
      bug_fix: [
        {max: 1000, message: '不能超过1000个字符', trigger: 'blur'},
      ],
      remark: [
        {max: 500, message: '不能超过500个字符', trigger: 'blur'}
      ],
      // update_range: [
      //   { required: true, message: '更新版本范围必填', trigger: 'blur' },
      // ],
      package_url: [
        {required: true, message: '必须上传更新文件', trigger: 'change'}
      ],
    }
  })

  async function handleDelete(row) {
    let {code} = await versionDeleteApi({ids: [row.id]});
    if (code === 0) {
      message.success("删除成功")
      // 删除指定的文件
      client.delete("setup_package/" + row.package_url)
        .then((result) => {
          console.log('文件删除成功', result);
        })
        .catch((err) => {
          console.error('文件删除失败', err);
        });
      onSearch()
    }
  }

  function handleCurrentChange(val: number) {
    onSearch()
  }

  function handleSizeChange(val: number) {
    onSearch()
  }

  function handleSelectionChange(val) {
    console.log("handleSelectionChange", val);
  }

  function onChange(checked, {$index, row}) {
    ElMessageBox.confirm(
      `确认要<strong>${
        row.status === 0 ? "停用" : "启用"
      }</strong><strong style='color:var(--el-color-primary)'>${
        row.version_number
      }</strong>版本吗?`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    )
      .then(() => {
        switchLoadMap.value[$index] = Object.assign({}, switchLoadMap.value[$index], {loading: true});
        versionSetStatusApi({id: row.id, status: row.status}).then(ret => {
          switchLoadMap.value[$index] = Object.assign({}, switchLoadMap.value[$index], {loading: false});
          if (ret.code === 0) {
            message.success("修改成功");
          }
          onSearch()
        })

      })
      .catch(() => {
        row.status === 0 ? (row.status = 1) : (row.status = 0);
      });
  }

  async function onSearch() {
    Data.loading = true;
    let {data} = await versionListApi(Data.search_from);
    if(data.total <= Data.search_from.page_size &&  Data.search_from.page > 1){
      Data.search_from.page = 1
      onSearch()
    }
    Data.data_list = data.list;
    Data.search_from.total = data.total;
    Data.loading = false;
  }

  const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
      if (valid) {
        if(Data.data_from.channel == 1) {
          Data.data_from.update_range = Data.data_from.update_range_official.toString()
        }else if(Data.data_from.channel == 2) {
          Data.data_from.update_range = Data.data_from.update_range_test.toString()
        }else {
          Data.data_from.update_range = Data.data_from.update_range_all.toString()
        }
        if (Data.data_from.id > 0) {
          let {code, msg} = await versionEditApi(Data.data_from);
          if (code === 0) {
            message.success(msg)
            Data.dialogVisible = false
            onSearch()
          }
        } else {
          let {code, msg} = await versionAddApi(Data.data_from);
          if (code === 0) {
            message.success(msg)
            Data.dialogVisible = false
            onSearch()
          }
        }
      }
    })
  }

  const cancelForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    Data.dialogVisible = false
    formEl.resetFields()
  }

  async function handleSwitch(row) {
    let {code, msg} = await versionSwitchApi({version_id: row.id});
    if (code === 0) {
      message.success(msg)
      onSearch()
    }
  }

  function handleUpdate(row) {
    cancelForm(ruleFormRef.value)
    if (row != null) {
      Data.data_from = JSON.parse(JSON.stringify(row))
      let update_range = []
      if(Data.data_from.update_range == 0 || Data.data_from.update_range == '0') {
        Data.data_from.update_range_official = []
        Data.data_from.update_range_test = []
        Data.data_from.update_range_all = []
      }else {
        if(Data.data_from.channel == 1) {
          Data.data_from.update_range_official = Data.data_from.update_range.split(",").map(Number)
        }else if(Data.data_from.channel == 2) {
          Data.data_from.update_range_test = Data.data_from.update_range.split(",").map(Number)
        }else {
          Data.data_from.update_range_all = Data.data_from.update_range.split(",").map(Number)
        }
      }
      console.log("range", Data.meal_options, Data.data_from.update_range_test)
    } else {
      Data.data_from = {
        id: 0,
        channel: 0,
        version_number: '',
        info: '',
        bug_fix: '',
        remark: '',
        update_range: '',
        update_range_official: [],
        update_range_test: [],
        full_url: '',
        package_url: '',
        progress: 0,
      }
    }
    Data.dialogVisible = true
  }


  onMounted(() => {
    // client = new OSS({
    //   region: "guangzhou",
    //   accessKeyId: "LTAI5t5fJKXCXU6wfoeUohrX",
    //   accessKeySecret: "******************************",
    //   bucket: "remote-qc-center",
    //   secure: false,
    //   endpoint: "oss-cn-guangzhou.aliyuncs.com",
    // })
    client = new OSS({
      region: "hangzhou",
      accessKeyId: "LTAI5t5fJKXCXU6wfoeUohrX",
      accessKeySecret: "******************************",
      bucket: "aiyunji-versin-mgr",
      secure: true,
      endpoint: "oss-cn-hangzhou.aliyuncs.com",
    })
    getMealOptions()
    onSearch();
  });

  async function getMealOptions() {
    let {data} = await orderMealOptionsApi();
    // Data.version_options_all = data.meal_options.slice(7);
    // Data.version_options_test = data.meal_options.slice(7,8).concat(data.meal_options.slice(17,18));
    // Data.version_options_official = data.meal_options.slice(8, 17).concat(data.meal_options.slice(18));
    ///  后续加
    // data.new_meal_options.unshift({
    //   "name": "全选",
    //   "m_val": [{ id: "all",name:"全选" }]
    // })

    Data.new_meal_options = data.new_meal_options
    Data.version_options_all = data.new_meal_options;
    Data.version_options_test = data.new_meal_options.slice(0,1);
    Data.version_options_official = data.new_meal_options.slice(1, data.new_meal_options.length);
  }

  function formatUpdateRange(update_range) {
    if (update_range.length == 0) {
      return "全部"
    }
    let meals = update_range.split(",")
    let names = ""
    meals.forEach(id => {
      Data.version_options_all.forEach(item1 => {
        if (id == item1.id) {
          Data.meal_options[item1.id] = item1
          names = names + ", " + item1.name
        }
      })
    })
    return names.slice(1)
  }

  async function queryDeviceList(row) {
    router.push({'name': 'device-list', query: {version_id: row.id}})
  }

  function handleBeforeFile(file) {
    Data.file = file
  }
  function formatPackageUrl(package_url) {
    if(package_url.search(/(\d{13}-)/g) != -1) {
      var index = package_url.indexOf('-')
      return package_url.slice(index+1)
    }
    return package_url
  }

  function selectAll(model) {
    if(model == 1) {
      if(Data.data_from.update_range_official.length < Data.version_options_official.length) {
        Data.data_from.update_range_official = []
        Data.version_options_official.forEach(item => {
          Data.data_from.update_range_official.push(item.id)
        })
      }else {
        Data.data_from.update_range_official = []
      }
    }else if(model == 2) {
      if(Data.data_from.update_range_test.length < Data.version_options_test.length) {
        Data.data_from.update_range_test = []
        Data.version_options_test.forEach(item => {
          Data.data_from.update_range_test.push(item.id)
        })
      }else {
        Data.data_from.update_range_test = []
      }
    }else {
      if(Data.data_from.update_range_all.length < Data.version_options_all.length) {
        Data.data_from.update_range_all = []
        Data.version_options_all.forEach(item => {
          Data.data_from.update_range_all.push(item.id)
        })
      }else {
        Data.data_from.update_range_all = []
      }
    }
  }

  async function fileUpload() {
    var date = new Date().getTime()
    var filename =  date+ '-' + Data.file.name
    await client.multipartUpload("setup_package/" + filename, Data.file, {
      trafficLimit: 819200,
      partSize: 5 * 1024 * 1024,
      headers: {"x-oss-forbid-overwrite": true},
      'progress': (p, cpt, res) => {
        let percent = (p * 100).toFixed(2)
        Data.data_from.progress = parseFloat(percent)
      }
    }).then(res => {
      Data.data_from.package_url = filename
      Data.data_from.full_url = 'https://aiyunji-versin-mgr.oss-cn-hangzhou.aliyuncs.com/setup_package/' + filename
      // https://remote-qc-center.oss-cn-guangzhou.aliyuncs.com/setup_package/lx-music-desktop-v2.2.0-win_x64-green.7z
    }).catch(err => {
      if (err.name == 'FileAlreadyExistsError') {
        errorMessage("服务器已经存在同名文件，请修改文件名在上传")
      } else {
        errorMessage("文件上传失败：" + err.name)
      }
    })


    // const formData = new FormData();
    // formData.append("scene", 'pkg');
    // formData.append('file', Data.file);
    // Data.loading = true
    // uploadApi(formData).then(ret => {
    //   Data.loading = false
    //   if(ret.code == 0) {
    //     Data.data_from.package_url = ret.data.path
    //   }
    // })
  }

  // function formatUpdateRange(row){
  //   if(row.update_range != ""){
  //     let update_range_str = []
  //     row.update_range .forEach(item => {
  //       Data.data_list.forEach(item2 => {
  //         if(item == item2.version_number){
  //           if(item2.channel == 1){
  //             update_range_str.push('<span class="el-tag el-tag--success el-tag--plain el-tag--mini">' + item2.version_number + '</span>')
  //           }
  //           if(item2.channel == 2){
  //             update_range_str.push('<span class="el-tag el-tag--warning el-tag--plain el-tag--mini">' + item2.version_number + '</span>')
  //           }
  //         }
  //
  //         if(update_range_str.length > 1 && update_range_str.length % 3 == 0){
  //           update_range_str.push("<br/>")
  //         }
  //       })
  //     })
  //
  //     return update_range_str.join('  ')
  //   }
  //   return ''
  // }


// 选择产品
const handleNewMealOptionChangeAll = (val) => {
  if (val.length > 0) {
    // 检查是否存在 "all" 的选项
    const hasAll = val.some(path => path.includes('all'));
    if (hasAll) {
      selectedIds = Data.new_meal_options.map(option => option.m_val);
      selectedMeals.value = Data.new_meal_options.map(option => findSelectedMealById(Data.new_meal_options, option.m_val));
    } else {
      selectedIds = val.map(item => item[item.length - 1]);
      selectedMeals.value = val.map(path => findSelectedMealById(Data.new_meal_options, path[path.length - 1]));
    }
    Data.data_from.update_range_all = selectedIds
    Data.data_from.update_range = selectedIds.join(','); 
  } else {
    selectedMeals.value = [];
    selectedIds = [];
    Data.data_from.update_range = '';
    Data.data_from.update_range_all = [];
  }
  Data.data_from.update_range_official = [];
  Data.data_from.update_range_test = [];
};

const handleNewMealOptionChangeOfficial = (val) => {
  if (val.length > 0) {
    // 检查是否存在 "all" 的选项
    const hasAll = val.some(path => path.includes('all'));
    if (hasAll) {
      selectedIds = Data.new_meal_options.map(option => option.m_val);
      selectedMeals.value = selectedIds.map(id => findSelectedMealById(Data.version_options_official, id));
    } else {
      selectedIds = val.map(item => item[item.length - 1]);
      selectedMeals.value = val.map(path => findSelectedMealById(Data.version_options_official, path[path.length - 1]));
    }
console.log(selectedIds,"iiiiiiii")
    Data.data_from.update_range_official = selectedIds
     Data.data_from.update_range = selectedIds.join(','); 
  } else {
    selectedMeals.value = [];
    selectedIds = [];
    Data.data_from.update_range = '';
    Data.data_from.update_range_official = [];
  }
  Data.data_from.update_range_all = [];
  Data.data_from.update_range_test = [];
};

const handleNewMealOptionChangeTest = (val) => {
  if (val.length > 0) {
    // 检查是否存在 "all" 的选项
    const hasAll = val.some(path => path.includes('all'));
    if (hasAll) {
      selectedIds = Data.new_meal_options.map(option => option.m_val);
      selectedMeals.value = Data.new_meal_options.map(option => findSelectedMealById(Data.new_meal_options, option.m_val));
    } else {
      selectedIds = val.map(item => item[item.length - 1]);
      selectedMeals.value = val.map(path => findSelectedMealById(Data.new_meal_options, path[path.length - 1]));
    }
    Data.data_from.update_range_test = selectedIds
    Data.data_from.update_range = selectedIds.join(',');
  } else {
    selectedMeals.value = [];
    selectedIds = [];
    Data.data_from.update_range = '';
    Data.data_from.update_range_test = [];
  }

  Data.data_from.update_range_official = [];
  Data.data_from.update_range_all = [];
};


const findSelectedMealById = (options, id) => {
  for (const option of options) {
    if (option.id === id) {
      return option;
    } else if (option.m_val && option.m_val.length > 0) {
      const found = findSelectedMealById(option.m_val, id);
      if (found) return found;
    }
  }
  return null;
};
</script>

<template>
  <div class="main">
    <el-form ref="formRef" :inline="true" :model="Data.search_from" class="bg-white w-99/100 pl-8 pt-4">
      <el-form-item label="渠道" prop="channel">
        <el-select v-model="Data.search_from.channel" placeholder="请选择">
          <el-option
            v-for="item in Data.channel_options"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="Data.search_from.status" placeholder="请选择">
          <el-option
            v-for="item in Data.status_options"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('search')"
          :loading="Data.loading"
          @click="onSearch">搜索
        </el-button>
        <el-button :icon="useRenderIcon('refresh')" @click="resetForm(formRef)">重置</el-button>
      </el-form-item>
    </el-form>

    <EpTableProBar
      :title="Data.data_list.length>0?'版本列表,共'+Data.data_list.length+'个版本':'版本列表,共0个版本'"
      :loading="Data.loading"
      :columnList="[
        {label: '版本号', show: true},
        {label: '渠道', show: true},
        // {label: '对应更新版本配置', show: true},
        {label: '发布信息', show: true},
        {label: 'Bug修复信息', show: true},
        {label: '备注', show: true},
        {label: '套餐', show: true},
        {label: '更新包', show: true},
        {label: '状态', show: true},
        {label: '更新时间', show: true},
        {label: '操作人', show: false}]"
      :dataList="Data.data_list"
      @refresh="onSearch">
      <template #buttons>
        <el-button v-if="Auth('version/add')" type="primary" :icon="useRenderIcon('add')" @click="handleUpdate(null)">
          添加版本
        </el-button>
      </template>
      <template v-slot="{ size, checkList }">
        <el-table
          border
          table-layout="auto"
          :size="size"
          :data="Data.data_list"
          :header-cell-style="{ background: '#fafafa', color: '#606266' }"
          @selection-change="handleSelectionChange">
          <el-table-column v-if="checkList.includes('版本号')" label="版本号" align="center" width="120"
                           prop="version_number">
            <template #default="scope">
              <span style="color: #00BFB5;">{{scope.row.version_number}}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="checkList.includes('渠道')" label="渠道" align="center" prop="channel">
            <template #default="scope">
              {{formatOptions(scope.row.channel, Data.channel_options)}}
            </template>
          </el-table-column>
          <!--          <el-table-column v-if="checkList.includes('对应更新版本配置')" label="对应更新版本配置" align="center" prop="update_range">-->
          <!--            <template #default="scope">-->
          <!--              <div v-html="formatUpdateRange(scope.row)"></div>-->
          <!--            </template>-->
          <!--          </el-table-column>-->
          <el-table-column class="text-line" v-if="checkList.includes('发布信息')" label="发布信息" align="left" width="150"
                           prop="info">
            <template #default="scope">
              <div class="text-line">{{scope.row.info}}</div>
            </template>
          </el-table-column>
          <el-table-column v-if="checkList.includes('Bug修复信息')" label="Bug修复信息" align="left" width="200" prop="bug_fix">
            <template #default="scope">
              <div class="text-line">{{scope.row.bug_fix}}</div>
            </template>
          </el-table-column>
          <el-table-column v-if="checkList.includes('备注')" label="备注" align="left" width="200" prop="remark">
            <template #default="scope">
              <div class="text-line">{{scope.row.remark}}</div>
            </template>
          </el-table-column>
          <el-table-column v-if="checkList.includes('套餐')" label="套餐" align="left" width="200" prop="update_range">
            <template #default="scope">
              <div class="text-line">{{formatUpdateRange(scope.row.update_range)}}</div>
            </template>
          </el-table-column>
          <el-table-column v-if="checkList.includes('更新包')" label="更新包" align="left" width="200" prop="package_url">
            <template #default="scope">
              <a :style="scope.row.status==0?'pointer-events:none;':'color: #04bab1;'" target="downloadFile"
                 :href="'https://aiyunji-versin-mgr.oss-cn-hangzhou.aliyuncs.com/setup_package/' + scope.row.package_url">{{formatPackageUrl(scope.row.package_url)}}</a>
            </template>
          </el-table-column>
          <el-table-column v-if="checkList.includes('状态')" label="状态" align="center" width="130" prop="status">
            <template #default="scope">
              <Switch
                :disabled="!Auth('version/set-status')"
                :size="size === 'small' ? 'small' : 'default'"
                :loading="switchLoadMap[scope.$index]?.loading"
                v-model:checked="scope.row.status"
                :checkedValue="1"
                :unCheckedValue="0"
                checked-children="已开启"
                un-checked-children="已关闭"
                @change="checked => onChange(checked, scope)"
              />
            </template>
          </el-table-column>
          <el-table-column v-if="checkList.includes('更新时间')" label="更新时间" align="center" width="180" prop="update_at"/>
          <el-table-column v-if="checkList.includes('操作人')" label="操作人" align="center" width="180"
                           prop="operator_name"/>
          <el-table-column fixed="right" label="操作" width="200" align="center">
            <template #default="scope">
              <!--              <el-popconfirm v-if="Auth('version/switch')" title="是否确认切换版本渠道?" @confirm="handleSwitch(scope.row)">-->
              <!--                <template #reference>-->
              <!--                  <el-button-->
              <!--                    class="reset-margin"-->
              <!--                    type="text"-->
              <!--                    :size="size"-->
              <!--                    :icon="useRenderIcon('guide')">-->
              <!--                    切换-->
              <!--                  </el-button>-->
              <!--                </template>-->
              <!--              </el-popconfirm>-->
              <el-button
                v-if="Auth('version/edit')"
                class="reset-margin"
                type="text"
                :size="size"
                @click="handleUpdate(scope.row)"
                :icon="useRenderIcon('edits')">
                修改
              </el-button>
              <el-popconfirm v-if="Auth('version/delete')" title="是否确认删除?" @confirm="handleDelete(scope.row)">
                <template #reference>
                  <el-button
                    class="reset-margin"
                    type="text"
                    :size="size"
                    :icon="useRenderIcon('delete')">
                    删除
                  </el-button>
                </template>
              </el-popconfirm>
              <el-button
                v-if="Auth('version/device-list')"
                class="reset-margin"
                type="text"
                :size="size"
                @click="queryDeviceList(scope.row)"
                :icon="useRenderIcon('list-check')">
                设备列表
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          class="flex justify-end mt-4"
          :small="size === 'small' ? true : false"
          v-model:page-size="Data.search_from.page_size"
          :page-sizes="[10, 20, 30, 50]"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="Data.search_from.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"/>
      </template>
    </EpTableProBar>

    <el-dialog v-model="Data.dialogVisible"
               :title="Data.data_from.id == 0?'添加版本':'编辑版本'"
               width="600px"
               draggable>
      <el-form
        v-loading="Data.loading"
        ref="ruleFormRef"
        :model="Data.data_from"
        :rules="Data.data_from.id == 0?Data.rules:Data.rules_edit"
        label-width="80px">
        <el-form-item label="发布渠道" prop="channel">
          <el-select v-model="Data.data_from.channel" placeholder="请选择">
            <el-option
              v-for="item in Data.channel_options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="版本号" prop="version_number">
          <el-input v-model="Data.data_from.version_number" show-word-limit maxlength="15"></el-input>
        </el-form-item>
        <el-form-item label="发布信息" prop="info">
          <el-input type="textarea" v-model="Data.data_from.info" placeholder="请输入更新内容" show-word-limit maxlength="1000"
                    :rows="4"></el-input>
        </el-form-item>
        <el-form-item label="BUG修复" prop="bug_fix">
          <el-input type="textarea" v-model="Data.data_from.bug_fix" placeholder="请输入修复内容" show-word-limit
                    maxlength="1000" :rows="4"></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" v-model="Data.data_from.remark" placeholder="请输入备注内容" show-word-limit
                    maxlength="500" :rows="4"></el-input>
        </el-form-item>
        <el-form-item label="选择套餐" prop="update_range">


          <el-cascader
            v-if="Data.data_from.channel == 1" 
            v-model="selectedIds"
            :options="Data.version_options_official"
            :props="new_meal_options_select"
            placeholder="请选择产品系列和型号"
            filterable
            show-all-levels
            clearable
            @change="handleNewMealOptionChangeOfficial"
          ></el-cascader>
          <el-cascader
            v-else-if="Data.data_from.channel == 2" 
            v-model="selectedIds"
            :options="Data.version_options_test"
            :props="new_meal_options_select"
            placeholder="请选择产品系列和型号"
            filterable
            show-all-levels
            clearable
            @change="handleNewMealOptionChangeTest"
          ></el-cascader>
          <el-cascader
            v-else
            v-model="selectedIds"
            :options="Data.version_options_all"
            :props="new_meal_options_select"
            placeholder="请选择产品系列和型号"
            filterable
            show-all-levels
            clearable
            @change="handleNewMealOptionChangeAll"
          ></el-cascader>


          <!-- <el-select 
            v-if="Data.data_from.channel == 1" 
            multiple 
            clearable 
            v-model="Data.data_from.update_range_official" 
            placeholder="请选择"
          >
            <el-option label="全选" value="-1" @click="selectAll(1)"/>
            <el-option
              v-for="item in Data.version_options_official"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
          <el-select
            v-else-if="Data.data_from.channel == 2" multiple clearable v-model="Data.data_from.update_range_test" placeholder="请选择">
            <el-option label="全选" value="-1" @click="selectAll(2)"/>
            <el-option
              v-for="item in Data.version_options_test"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
          <el-select v-else multiple clearable v-model="Data.data_from.update_range_all" placeholder="请选择">
            <el-option label="全选" value="-1" @click="selectAll(0)"/>
            <el-option
              v-for="item in Data.version_options_all"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select> -->




        </el-form-item>


        <el-form-item label="更新包" prop="package_url">
          <el-upload
            action=""
            :http-request="fileUpload"
            :before-upload="handleBeforeFile"
            :show-file-list="false">
            <el-button type="primary">点击上传</el-button>
          </el-upload>

          <div style="margin-left: 10px;margin-top: -5px;min-width: 380px;">
            <a style="" target="downloadFile" v-if="Data.data_from.package_url != ''" :href="Data.data_from.full_url">
              {{Data.data_from.package_url}}</a>

            <el-progress v-if="Data.data_from.progress>0" :percentage="Data.data_from.progress"
                         :color="'#f56c6c'"></el-progress>
          </div>
        </el-form-item>

        <el-form-item>
          <div style="padding-top: 10px;">
            <el-button type="primary" @click="submitForm(ruleFormRef)">提交</el-button>
            <el-button @click="cancelForm(ruleFormRef)">取消</el-button>
          </div>

        </el-form-item>
      </el-form>
    </el-dialog>

  </div>
</template>
<style scoped lang="scss">
  :deep(.el-dropdown-menu__item i) {
    margin: 0;
  }

  .text-line {
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
  }
</style>
