<script setup lang="ts">
  import dayjs from "dayjs";
  import {Auth, AuthCount} from "/@/utils/auth";
  import { FormInstance, ElMessageBox } from "element-plus";
  import { reactive, ref, onMounted } from "vue";
  import { EpTableProBar } from "/@/components/ReTable";
  import { Switch, message } from "@pureadmin/components";
  import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
  import {
    adminListApi,
    adminDepartmentApi,
    adminAddApi,
    adminEditApi,
    adminDeleteApi,
    departmentTreeApi,
    adminSetStatusApi,
    roleListApi,
    agentAllApi,
    agentHadBindApi,
    agentBindApi,
    agentCancelBindApi,
    loginQueryApi,
    loginSetStatusApi,
    getUserRegionApi
  } from "/@/api/sys"
  import { getRegionApi } from "/@/api/public"
  import { dateFormat } from "/@/utils/tool";
  import {orderList<PERSON>pi, order<PERSON>ersonal<PERSON><PERSON>} from "/@/api/order";
  import {storageSession} from "/@/utils/storage";

  const formRef = ref<FormInstance>();
  const ruleFormRef = ref<FormInstance>()
  let switchLoadMap = ref({});
  const Data =  reactive({
    dialogVisible: false,
    dialogBindVisible: false,
    regionAdd: true,
    search_from: {
      keyword: "",
      role_id: "",
      status: "",
      page_size: 10,
      page: 1,
      total: 0,
    },
    loading: true,
    data_list: [],
    role_list: [],
    department_options: [],
    data_from: {
      id: 0,
      role_id: "",
      role_ids: [],
      region_id: "",
      region_ids: [],
      username: "",
      password: "",
      email: "",
      phone: "",
      avatar: "",
      status: 1,
    },
    agent_type_options: [
      { value: "1", label: "区域代理商"},
      { value: "2", label: "报单代理商"},
    ],
    rules: {
      username: [
        {required: true, message: '请输入用户名', trigger: 'blur'},
        {min: 5, max: 20, message: '用户名长度限定为5到20个字符', trigger: ['blur', 'change']},
      ],
      password: [
        {required: true, message: '请输入密码', trigger: 'blur'},
        {pattern:/^(?![0-9]+$)(?![a-z]+$)(?![A-Z]+$)(?!([^(0-9a-zA-Z)]|[()])+$)(?!^.*[\u4E00-\u9FA5].*$)([^(0-9a-zA-Z)]|[()]|[a-z]|[A-Z]|[0-9]){8,18}$/,
          message: '密码格式应为8-18位数字、字母、符号的任意两种组合', trigger: ['blur', 'change']}
      ],
      role_ids: [
        {required: true, message: '角色名称必填', trigger: 'blur'},
      ],
      phone: [
        {required: true, message: '手机号必填', trigger: 'blur'},
        {validator: checkPhone, trigger: ['blur', 'change']},
      ],
    },
    rules_edit: {
      username: [
        {required: true, message: '请输入用户名', trigger: 'blur'},
        {min: 5, max: 20, message: '用户名长度限定为5到20个字符', trigger: ['blur', 'change']},
      ],
      password: [
        {pattern:/^(?![0-9]+$)(?![a-z]+$)(?![A-Z]+$)(?!([^(0-9a-zA-Z)]|[()])+$)(?!^.*[\u4E00-\u9FA5].*$)([^(0-9a-zA-Z)]|[()]|[a-z]|[A-Z]|[0-9]){8,18}$/,
          message: '密码格式应为8-18位数字、字母、符号的任意两种组合', trigger: ['blur', 'change']}
      ],
      role_ids: [
        {required: true, message: '角色名称必填', trigger: 'blur'},
      ],
      phone: [
        {required: true, message: '手机号必填', trigger: 'blur'},
        {validator: checkPhone, trigger: ['blur', 'change']},
      ],
    },
    sale_info_sel: null,
    region_tree: [],
    agent_region_tree: [],
    bound_region_tree: [],
    had_bind: [],
    agent_search_from: {
      region_id: "",
      name: "",
      is_all: "",
    },
    bound_search_from: {
      user_id: 0,
      region_id: "",
      name: "",
      is_all: "",
    },
    agent_list: [],
    need_bind: [],
    need_remove: [],
    sms: {
      id: 0,
      status: 0,
    },
    user_info: null,
  })

  //校验联系电话
  function checkPhone(rule, value, callback) {
    if (value) {
      let isPhone = new RegExp("^1[34578][0-9]{9}$",'i')
      let isLand = new RegExp("^(0[0-9]{2,3}-)([0-9]{7,8})$",'i')
      if( isPhone.test(value) || isLand.test(value) ){
        callback();
      }else{
        callback(new Error('电话格式异常'));
      }
    }else {
      callback();
    }
  }

  async function handleDelete(row) {
    let { code } = await adminDeleteApi({ids: [row.id]});
    if(code === 0){
      message.success("删除成功")
      onSearch()
    }
  }
  function handleCurrentChange(val: number) {
    Data.search_from.page = val
    onSearch()
  }
  function handleSizeChange(val: number) {
    Data.search_from.page_size = val
    onSearch()
  }
  function handleSelectionChange(val) {
    console.log("handleSelectionChange", val);
  }

  function onChange(checked, { $index, row }) {
    ElMessageBox.confirm(
      `确认要<strong>${
        row.status === 0 ? "停用" : "启用"
      }</strong><strong style='color:var(--el-color-primary)'>${
        row.username
      }</strong>角色吗?`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    )
      .then(() => {
        switchLoadMap.value[$index] = Object.assign({}, switchLoadMap.value[$index], {loading: true});
        adminSetStatusApi({id: row.id, status: row.status}).then(ret => {
          switchLoadMap.value[$index] = Object.assign({}, switchLoadMap.value[$index], {loading: false});
          if(ret.code === 0){
            message.success("修改成功");
          }
          onSearch()
        })

      })
      .catch(() => {
        row.status === 0 ? (row.status = 1) : (row.status = 0);
      });
  }
  async function onSearch() {
    Data.loading = true;
    //根据用户权限，判断查询全部列表，还是部门列表
    let response
    if(Auth('sys-admin/list')) {
      response = await adminListApi(Data.search_from);
    }else {
      response = await adminDepartmentApi(Data.search_from);
    }
    let data = response.data
    if(data.total <= Data.search_from.page_size &&  Data.search_from.page > 1){
      Data.search_from.page = 1
      onSearch()
    }
    data.list.forEach(item => {
      item['department_id'] = item['department_id'].split(',')
    })
    Data.data_list = data.list;
    Data.search_from.total = data.total;
    Data.role_list = data.role
    checkRoleList()
    Data.loading = false;
  }

  //判断角色列表
  function checkRoleList() {
    //部门列表角色
    if(!Auth('sys-admin/list')) {
      Data.role_list?.forEach(item => {
        if(Data.user_info?.role_id != item?.id){
          item.disabled = true
        }
      })
    }
  }

  const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async(valid, fields) => {
      if (valid) {
        if(Data.data_from.role_ids.length > 0){
          Data.data_from.role_id = Data.data_from.role_ids[Data.data_from.role_ids.length-1]
        }
        var err_msg = ''
        var region_ids = []
        Data.data_from.region_ids.forEach((item, index) => {
          if(item?.length > 0){
            region_ids.push(item[item.length-1])
          }
        })
        if(err_msg){
          message.error(err_msg)
          return
        }
        Data.data_from.region_id = region_ids.join(",")
         if(Data.data_from.id > 0){
           let { code, msg } = await adminEditApi(Data.data_from);
           if(code === 0){
              message.success(msg)
              Data.dialogVisible = false
              onSearch()
           }
         }else{
           let { code, msg } = await adminAddApi(Data.data_from);
           if(code === 0){
             message.success(msg)
             Data.dialogVisible = false
             onSearch()
           }
         }
      }
    })
  }

  const cancelForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    Data.dialogVisible = false
    formEl.resetFields()
  }

  function handleUpdate(row) {
    cancelForm(ruleFormRef.value)
    if(row != null ) {
      Data.data_from = JSON.parse(JSON.stringify(row))
      if(row.up_id > 0){
        Data.data_from.role_ids = [row.up_id, row.role_id]
      }else {
        Data.data_from.role_ids = [row.role_id]
      }
      Data.data_from.region_ids = []
      Data.regionAdd = true
      row.region_ids?.forEach(item => {
        let region_bit_arr = item.split("")
        if(region_bit_arr.length == 6){
          if(!(region_bit_arr[4]==0 && region_bit_arr[5] == 0)) {
            Data.data_from.region_ids.push([
              region_bit_arr[0]+region_bit_arr[1]+"0000",
              region_bit_arr[0]+region_bit_arr[1]+region_bit_arr[2]+region_bit_arr[3]+'00',
              item
            ])
          }else if(region_bit_arr[4]==0 && region_bit_arr[5] == 0 && (!(region_bit_arr[2]==0 && region_bit_arr[3] == 0))) {
            Data.data_from.region_ids.push([
              region_bit_arr[0]+region_bit_arr[1]+"0000",
              item
            ])
          }else {
            Data.data_from.region_ids.push([item])
          }
        }else {
          //全国
          Data.data_from.region_ids.push([item])
          Data.regionAdd = false
        }
      })
    } else {
      Data.data_from = {
        id: 0,
        role_id: "",
        role_ids: [],
        region_id: "",
        region_ids: [],
        username: "",
        password: "",
        email: "",
        phone: "",
        avatar: "",
        status: 1,
      }
      Data.data_from.region_ids.push("")
    }
    Data.dialogVisible = true
  }

  function getDepartmentTree(){
    departmentTreeApi().then(ret => {
      Data.department_options = ret.data
    })
  }

  function DapartmentInfo(ids) {
    let str = ""
    if (ids.length == 2) {
      Data.department_options.forEach(item => {
        item.children.forEach(item2 => {
           if(item.value == ids[0] && item2.value == ids[1]){
             str = item.label + ' / ' + item2.label
           }
        })
      })
    }
    return str
  }

  onMounted(() => {
    Data.user_info = storageSession.getItem("info")
    getRegion()
    getDepartmentTree()
    getLoginStatus()
    onSearch();
  });

  async function getLoginStatus() {
    if(!Auth('sys-admin/set-login-status')){
      return
    }
    let { code, data } = await loginQueryApi({type: 'SMS'});
    if(code === 0) {
      if(data?.length>0){
        Data.sms.id = data[0].id
        Data.sms.status = data[0].status
      }
    }
  }

  function onLoginChange(checked) {
    ElMessageBox.confirm(
      `确认要<strong>${
        Data.sms.status === 0 ? "停用" : "启用"
      }</strong>短信登录验证吗?`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    )
      .then(() => {
        loginSetStatusApi(Data.sms).then(ret => {
          if(ret.code === 0){
            message.success("修改成功");
          }
          onSearch()
        })

      })
      .catch(() => {
        Data.sms.status === 0 ? (Data.sms.status = 1) : (Data.sms.status = 0);
      });
  }

  function handleBind(row) {
    Data.sale_info_sel = row
    getAllAgent()
    getHadBindAgent()
    Data.dialogBindVisible = true
  }

  //取消销售绑定缓存
  function cancelBindForm(){
    Data.dialogBindVisible = false
    Data.sale_info_sel = null
    Data.agent_list = []
    Data.had_bind=[]
    // Data.is_bind=[]
    Data.need_bind=[]
    Data.need_remove=[]
    Data.agent_search_from = {
      region_id: "",
      name: "",
      is_all: "",
    }
    Data.bound_search_from = {
      user_id: 0,
      region_id: "",
      name: "",
      is_all: "",
    }
  }

  async function submitBindForm(){
    let { code, msg } = await agentBindApi({user_id:Data.sale_info_sel.id, bind_groups: Data.need_bind, remove_groups: Data.need_remove});
    if(code === 0){
      message.success(msg)
    }
  }

  async function getAllAgent() {
    let { code, data } = await agentAllApi(Data.agent_search_from);
    if(code === 0){
      Data.agent_list = data
    }
  }

  async function getAllAgentSearch() {
    await getAllAgent()
    await checkBind()
  }

  async function getHadBindAgent() {
    Data.bound_search_from.user_id = Data.sale_info_sel.id
    let { code, data } = await agentHadBindApi(Data.bound_search_from);
    if(code === 0){
      Data.had_bind = data
    }
  }

  async function getHadBindAgentSearch() {
    await getHadBindAgent()
    await checkBind()
  }

  function checkBind(){
    Data.agent_list.forEach((item, index) => {
      var index1 = Data.had_bind.findIndex(item1 => {
        return item1.group == item.group
      })
      if(index1 != -1){
        Data.agent_list[index].is_bind = 1
      }
    })
    Data.agent_list.sort(function(a, b){
        return b.is_bind-a.is_bind
    })
  }

  //用户关联全部代理商
  function allBind() {
    Data.agent_list.forEach((item, index) => {
      var index1 = Data.had_bind.findIndex(item1 => {
        return item1?.group == item.group
      })
      Data.had_bind[index] = item
      // Data.is_bind[index] = true
      item.is_bind = 1
      if(index1 == -1){
        //判断是否是新移除的，是则从新移除列表移除，不是则加入新绑定列表
        var index2 = Data.need_remove.indexOf(item.group)
        if(index2 != -1){
          Data.need_remove.splice(index2, 1)
        }else {
          Data.need_bind.push(item.group)
        }
      }
    })
  }

  //用户关联自己区域下的代理商
  function regionBind() {
    Data.agent_list.forEach((item, index) => {
      //判断代理商区域是否在用户区域下
      var is_in = false
      item.region_ids?.forEach((item1, index1) => {
        Data.sale_info_sel.region_ids?.forEach((item2,index2) => {
          let region_bit_arr2 = item2.split("")
          if(region_bit_arr2.length == 6){
            if(!(region_bit_arr2[4]==0 && region_bit_arr2[5] == 0)) {
              if(item1 == item2) {
                is_in = true
                return
              }
            }else if(region_bit_arr2[4]==0 && region_bit_arr2[5] == 0 && (!(region_bit_arr2[2]==0 && region_bit_arr2[3] == 0))) {
              if(item1.slice(0,4) == (region_bit_arr2[0]+region_bit_arr2[1] + region_bit_arr2[2]+region_bit_arr2[3])){
                is_in = true
                return
              }
            }else {
              if(item1.slice(0,2) == (region_bit_arr2[0]+region_bit_arr2[1])){
                is_in = true
                return
              }
            }
          }
        })
        if(is_in) {
          return
        }
      })

      if(is_in){
        var index1 = Data.had_bind.findIndex(item1 => {
          return item1?.group == item.group
        })
        if(index1 == -1){
          //已绑定列表不在则绑定
          Data.had_bind.push(item)
          item.is_bind = 1
          //判断是否是新移除的，是则从新移除列表移除，不是则加入新绑定列表
          var index2 = Data.need_remove.indexOf(item.group)
          if(index2 != -1){
            Data.need_remove.splice(index2, 1)
          }else {
            Data.need_bind.push(item.group)
          }
        }
      }
    })
  }

  function regionBindRemove() {
    Data.had_bind = Data.had_bind.filter((item,index) =>{
      //判断代理商区域是否在用户区域下
      var is_in = false
      Data.had_bind[index].region_ids?.forEach((item1, index1) => {
        Data.sale_info_sel.region_ids?.forEach((item2, index2) => {
          let region_bit_arr2 = item2.split("")
          if (region_bit_arr2.length == 6) {
            if (!(region_bit_arr2[4] == 0 && region_bit_arr2[5] == 0)) {
              if (item1 == item2) {
                is_in = true
                return
              }
            } else if (region_bit_arr2[4] == 0 && region_bit_arr2[5] == 0 && (!(region_bit_arr2[2] == 0 && region_bit_arr2[3] == 0))) {
              if (item1.slice(0, 4) == (region_bit_arr2[0] + region_bit_arr2[1] + region_bit_arr2[2] + region_bit_arr2[3])) {
                is_in = true
                return
              }
            } else {
              if (item1.slice(0, 2) == (region_bit_arr2[0] + region_bit_arr2[1])) {
                is_in = true
                return
              }
            }
          }
        })
        if (is_in) {
          return
        }
      })

      if (is_in) {
        Data.agent_list.forEach((item1, index1) => {
          if (item1.group == Data.had_bind[index].group) {
            item1.is_bind = 0
          }
        })
        // Data.had_bind.splice(index, 1)
        //判断是否是新绑定的，是则从新绑定列表移除，不是则加入新移除列表
        var index2 = Data.need_bind.indexOf(Data.had_bind[index].group)
        if (index2 != -1) {
          Data.need_bind.splice(index2, 1)
        } else {
          Data.need_remove.push(Data.had_bind[index].group)
        }
      }else {
        return item
      }
    })
  }

  function allRemove() {
    Data.had_bind.forEach((item) => {
      Data.agent_list.forEach((item1, index1) => {
        if(item.group == item1.group) {
          // Data.is_bind[index1] = false
          item1.is_bind = 0
        }
      })
      //判断是否是新绑定的，是则从新绑定列表移除，不是则加入新移除列表
      var index2 = Data.need_bind.indexOf(item.group)
      if(index2 != -1){
        Data.need_bind.splice(index2, 1)
      }else {
        Data.need_remove.push(item.group)
      }
    })
    Data.had_bind = []
  }

  function addBind(row, index) {
    var i = Data.had_bind.findIndex(item1 => {
      return item1.group == row.group
    })
    if(i != -1) {
      return
    }
    Data.had_bind.push(row)
    row.is_bind = 1
    //判断是否是新移除的，是则从新移除列表移除，不是则加入新绑定列表
    var index2 = Data.need_remove.indexOf(row.group)
    if(index2 != -1){
      Data.need_remove.splice(index2, 1)
    }else {
      Data.need_bind.push(row.group)
    }
  }

  function removeBind(row, index) {
    Data.agent_list.forEach((item, index1) => {
      if(item.group == row.group) {
        // Data.is_bind[index1] = false
        item.is_bind = 0
      }
    })
    // Data.tem_had_bind.splice(index, 1)
    Data.had_bind.splice(index, 1)
    //判断是否是新绑定的，是则从新绑定列表移除，不是则加入新移除列表
    var index2 = Data.need_bind.indexOf(row.group)
    if(index2 != -1){
      Data.need_bind.splice(index2, 1)
    }else {
      Data.need_remove.push(row.group)
    }
  }

  async function getRegion() {
    let { data } = await getRegionApi();
    Data.region_tree = data;
    Data.region_tree.unshift({
      label: '全国',
      type: 0,
      value: '0',
    })
    Data.agent_region_tree = JSON.parse(JSON.stringify(Data.region_tree))
    Data.bound_region_tree = JSON.parse(JSON.stringify(Data.region_tree))
    getUserRegion()
  }

  //获取用户区域树
  async function getUserRegion(){
    //显示用户区域
    let { code, data } = await getUserRegionApi();
    if(code === 0){
      Data.region_tree = data?.tree
      let user = data?.user
      if(user?.region_id == "0"){
        Data.region_tree.unshift({
          label: '全国',
          type: 0,
          value: '0',
        })
      }
    }
  }

  function formatOptionLabel(options, value) {
    var label = ''
    options?.forEach((item, index) => {
      if(item.value == value) {
        label = item.label
        return
      }
    })
    return label
  }

  const propsSearch = {
    checkStrictly: true,
  }

  const propsRoleSearch = {
    checkStrictly: true,
    label:'name',
    value:'id',
    children:'groups'
  }

  function formatTitle() {
    if(Auth('sys-admin/list')) {
      return '用户列表'
    }else {
      if(Data.role_list.length > 0) {
        return Data.role_list[0].name
      }
    }
  }

  function handleAddRegion() {
    Data.data_from.region_ids.push("")
  }
  function handleDelRegion(index) {
    Data.data_from.region_ids.splice(index, 1)
  }

  //检查用户是否选择全国
  function checkRegionAdd() {
    var isAll = false
    Data.data_from.region_ids?.forEach(item => {
      if(item?.[0] == "0"){
        isAll = true
        return
      }
    })
    Data.regionAdd = true
    if(isAll){
      Data.data_from.region_ids = [["0"]]
      Data.regionAdd = false
    }
  }
</script>

<template>
  <div class="main">
    <el-form ref="formRef" :inline="true" :model="Data.search_from" class="bg-white w-99/100 pl-8 pt-4">
      <el-form-item label="用户名称：" prop="keyword">
        <el-input style="width: 200px;" v-model="Data.search_from.keyword" placeholder="请输入用户名称" clearable />
      </el-form-item>
      <el-form-item label="状态：" prop="status">
        <el-select v-model="Data.search_from.status" placeholder="请选择状态" clearable>
          <el-option label="已开启" value="1" />
          <el-option label="已关闭" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('search')"
          :loading="Data.loading"
          @click="onSearch">搜索</el-button>
        <el-button :icon="useRenderIcon('refresh')" @click="resetForm(formRef)">重置</el-button>
      </el-form-item>
      <el-form-item v-if="Auth('sys-admin/set-login-status')" label="短信登录状态：" prop="login-status">
        <Switch
          :size="size === 'small' ? 'small' : 'default'"
          v-model:checked="Data.sms.status"
          :checkedValue="1"
          :unCheckedValue="0"
          checked-children="已开启"
          un-checked-children="已关闭"
          @change="checked => onLoginChange(checked)"
        />
      </el-form-item>
    </el-form>

    <EpTableProBar
      :title="formatTitle()"
      :loading="Data.loading"
      :columnList="[
        {label: '用户ID', show: true},
        {label: '用户名', show: true},
        {label: '角色', show: true},
        {label: '部门', show: false},
        {label: '手机号', show: true},
        {label: '区域', show: true},
        {label: '邮箱', show: false},
        {label: '角色名称', show: false},
        {label: '状态', show: true},
        {label: '创建时间', show: true},
        {label: '创建者', show: true},
        ]"
      :dataList="Data.data_list"
      @refresh="onSearch">
      <template #buttons>
        <el-button v-if="Auth('sys-admin/add')" type="primary" :icon="useRenderIcon('add')" @click="handleUpdate(null)">新增用户</el-button>
      </template>
      <template v-slot="{ size, checkList }">
        <el-table
          border
          table-layout="auto"
          :size="size"
          :data="Data.data_list"
          :header-cell-style="{ background: '#fafafa', color: '#606266' }"
          @selection-change="handleSelectionChange">
          <el-table-column v-if="checkList.includes('用户ID')" label="用户ID" align="center" prop="id" />
          <el-table-column v-if="checkList.includes('用户名')" label="用户名" align="center" prop="username" />
          <el-table-column v-if="checkList.includes('角色')" label="角色" align="center" prop="role_name" />
          <el-table-column v-if="checkList.includes('部门')" label="部门" align="center" width="250" prop="department_id">
            <template #default="scope">
                {{DapartmentInfo(scope.row.department_id)}}
            </template>
          </el-table-column>
          <el-table-column v-if="checkList.includes('手机号')" label="手机号" align="center" width="200" prop="phone" />
          <el-table-column v-if="checkList.includes('区域')" label="区域" align="center">
            <template #default="scope">
              <div v-if="scope.row.region_names" v-for="(v, k) in scope.row.region_names">{{ v }}</div>
            </template>
          </el-table-column>
          <el-table-column v-if="checkList.includes('邮箱')" label="邮箱" align="center" prop="email" />
          <el-table-column v-if="checkList.includes('角色名称')" label="角色名称" align="center" prop="role_id" />
          <el-table-column v-if="checkList.includes('状态')" label="状态" align="center" width="130" prop="status">
            <template #default="scope">
              <Switch
                :disabled="!Auth('sys-admin/set-status') || scope.row.id==1 || scope.row.role_status == 0"
                :size="size === 'small' ? 'small' : 'default'"
                :loading="switchLoadMap[scope.$index]?.loading"
                v-model:checked="scope.row.status"
                :checkedValue="1"
                :unCheckedValue="0"
                checked-children="已开启"
                un-checked-children="已关闭"
                @change="checked => onChange(checked, scope)"
              />
            </template>
          </el-table-column>
          <el-table-column v-if="checkList.includes('创建时间')" label="创建时间" align="center" width="180" prop="create_at"/>
          <el-table-column v-if="checkList.includes('创建者')" label="创建者" align="center" prop="create_name"/>
          <el-table-column fixed="right" label="操作" width="250" align="center">
            <template #default="scope">
              <el-button
                v-if="Auth('sys-admin/sales-bind') && scope.row.role_id != '1'"
                class="reset-margin"
                type="text"
                :size="size"
                @click="handleBind(scope.row)"
                :icon="useRenderIcon('edits')">
                销售设置
              </el-button>
              <el-button
                v-if="Auth('sys-admin/edit') && (scope.row.id != 1 || (scope.row.id==1 && Data.user_info?.id==1))"
                class="reset-margin"
                type="text"
                :size="size"
                @click="handleUpdate(scope.row)"
                :icon="useRenderIcon('edits')">
                修改
              </el-button>
              <el-popconfirm v-if="Auth('sys-admin/delete') && scope.row.id != 1" title="是否确认删除?" @confirm="handleDelete(scope.row)">
                <template #reference>
                  <el-button
                    class="reset-margin"
                    type="text"
                    :size="size"
                    :icon="useRenderIcon('delete')">
                    删除
                  </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          class="flex justify-end mt-4"
          :small="size === 'small' ? true : false"
          v-model:page-size="Data.search_from.page_size"
          :page-sizes="[10, 20, 30, 50]"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="Data.search_from.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"/>
      </template>
    </EpTableProBar>

    <el-dialog v-model="Data.dialogVisible"
               :title="Data.data_from.id == 0?'添加用户':'编辑用户'"
               width="450px"
               :close-on-click-modal="false"
               draggable>
      <el-form
        ref="ruleFormRef"
        :model="Data.data_from"
        :rules="Data.data_from.id == 0?Data.rules:Data.rules_edit"
        label-width="80px">
        <el-form-item label="用户名" prop="username">
          <el-input :disabled="Data.data_from.id == 1" v-model="Data.data_from.username" show-word-limit maxlength="20"/>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="Data.data_from.password" show-word-limit maxlength="18"/>
        </el-form-item>
        <el-form-item label="角色" prop="role_ids">
          <el-cascader :disabled="Data.data_from.id==1" :props="propsRoleSearch" :options="Data.role_list"  v-model="Data.data_from.role_ids" style="width:200px" clearable/>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="Data.data_from.phone" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="Data.data_from.email" />
        </el-form-item>
        <el-form-item :label="Data.data_from.region_ids.length>1?'区域1':'区域'" prop="region_ids">
          <div class="regions">
            <el-cascader :disabled="Data.data_from.id == 1" @change="checkRegionAdd" :props="propsSearch" :options="Data.region_tree" v-model="Data.data_from.region_ids[0]" style="width:200px" clearable/>
            <el-button v-if="Data.data_from.region_ids?.length>1" type="primary"
                       size="small" circle :icon="useRenderIcon('remove')" @click="handleDelRegion(0)"></el-button>
            <el-button :disabled="!Data.regionAdd" type="primary" size="small" circle :icon="useRenderIcon('adds')" @click="handleAddRegion()"></el-button>
          </div>
        </el-form-item>
        <div  v-for="(v,k) in Data.data_from.region_ids">
          <el-form-item v-if="k>0"  :label="'区域'+(k+1)" prop="region_ids">
            <div class="regions">
              <el-cascader @change="checkRegionAdd" :props="propsSearch" :options="Data.region_tree"  v-model="Data.data_from.region_ids[k]" style="width:200px" clearable/>
              <el-button type="primary" size="small" circle :icon="useRenderIcon('remove')" @click="handleDelRegion(k)"></el-button>
            </div>
          </el-form-item>
        </div>
        <el-form-item>
          <el-button type="primary" @click="submitForm(ruleFormRef)">提交</el-button>
          <el-button @click="cancelForm(ruleFormRef)">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog v-model="Data.dialogBindVisible" title="销售设置" :close-on-click-modal="false" draggable width="1600px" custom-class="sales-binding"
      @opened="checkBind" @close="cancelBindForm">
      <div class="sale-info">
        <el-span>账户名称：{{ Data.sale_info_sel?.username }}</el-span>
        <el-span>角色：{{ Data.sale_info_sel?.role_name }}</el-span>
        <el-span>
          <div>区域：</div>
          <div>
            <div v-for="(v, k) in Data.sale_info_sel?.region_names">{{ v }}</div>
          </div>
        </el-span>
        <el-span>电话：{{ Data.sale_info_sel?.phone }}</el-span>
        <el-span>创建时间：{{ Data.sale_info_sel?.create_at }}</el-span>
      </div>
      <div class="sales-bind">
        <div class="list agent-list">
          <div class="list-title">
            <div class="text-info">
              <el-span>关联代理商</el-span>
            </div>
            <div class="bind-btn">
              <el-button type="primary" @click="allBind">全部关联</el-button>
            </div>
          </div>
          <div class="list-content">
            <div class="list-search">
              <el-span>地区筛选</el-span>
              <el-cascader :props="propsSearch" style="width: 250px;" :options="Data.agent_region_tree"
                v-model="Data.agent_search_from.region_id" clearable @change="getAllAgentSearch"/>
              <el-input v-model="Data.agent_search_from.name" placeholder="请输入名称搜索" @change="getAllAgentSearch" clearable></el-input>
              <el-button v-if="Data.sale_info_sel?.region_ids!=null" style="margin-left: 100px" type="primary" @click="regionBind">区域关联</el-button>
            </div>
            <div class="list-table">
              <el-table :data="Data.agent_list" style="width: 100%">
                <el-table-column label="代理商类型" align="center" width="100">
                  <template #default="scope">
                    <el-span :style="scope.row.is_bind?'color:#04BAB1':''">{{ formatOptionLabel(Data.agent_type_options, scope.row.type) }}</el-span>
                  </template>
                </el-table-column>
                <el-table-column prop="region_names" label="区域" align="center">
                  <template #default="scope">
                    <div :style="scope.row.is_bind?'color:#04BAB1':''">
                      <div>
                        <div v-if="scope.row.is_all==0" v-for="(v, k) in scope.row.region_names">{{ v }}</div>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="name" label="代理商名称" align="center" width="100">
                  <template #default="scope">
                    <div :style="scope.row.is_bind?'color:#04BAB1':''">{{ scope.row.name }}</div>
                  </template>
                </el-table-column>
                <el-table-column label="代理时长" align="center">
                  <template #default="scope">
                    <el-space :style="scope.row.is_bind?'color:#04BAB1':''">
                      {{ dateFormat(scope.row.start_time, 'YYYY-mm-dd') + ' 至 ' + dateFormat(scope.row.end_time, 'YYYY-mm-dd')}}
                    </el-space>
                    <el-space v-if="scope.row.date_status==1" style="color: #00bfb5">{{'(临近)'}}</el-space>
                    <el-space v-if="scope.row.date_status==2" style="color: red">{{'(过期)'}}</el-space>
                  </template>
                </el-table-column>
                <el-table-column prop="is_bind" label="是否关联" align="center" width="120">
                  <template #default="scope">
                    <div v-if="scope.row.is_bind" style="color: #04BAB1;">已关联</div>
                    <div v-else><el-button @click="addBind(scope.row, scope.$index)" color="#04BAB1" style="color: white;" round>添加关联</el-button></div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
        <div class="list bind-list">
          <div class="list-title">
            <div class="text-info">
              <el-span>账户已关联</el-span>
            </div>
            <div class="bind-btn">
              <el-button type="danger" @click="allRemove">全部移除</el-button>
            </div>
          </div>
          <div class="list-content">
            <div class="list-search">
              <el-span>地区筛选</el-span>
              <el-cascader :props="propsSearch" style="width: 250px;" :options="Data.bound_region_tree"
                v-model="Data.bound_search_from.region_id" @change="getHadBindAgentSearch" clearable/>
              <el-input v-model="Data.bound_search_from.name" placeholder="请输入名称搜索" @change="getHadBindAgentSearch" clearable></el-input>
              <el-button v-if="Data.sale_info_sel?.region_ids!=null" style="margin-left: 100px" type="primary" @click="regionBindRemove">取消区域关联</el-button>
            </div>
            <div class="list-table">
              <el-table :data="Data.had_bind" style="width: 100%">
                <el-table-column label="代理商类型" align="center" width="100">
                  <template #default="scope">
                    {{ formatOptionLabel(Data.agent_type_options, scope.row.type) }}
                  </template>
                </el-table-column>
                <el-table-column prop="region_names" label="区域" align="center">
                  <template #default="scope">
                    <div>
                      <div v-if="scope.row.is_all==0" v-for="(v, k) in scope.row.region_names">{{ v }}</div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="name" label="代理商名称" align="center" width="100"/>
                <el-table-column label="代理时长" align="center">
                  <template #default="scope">
                    <el-space>
                      {{ dateFormat(scope.row.start_time, 'YYYY-mm-dd') + ' 至 ' + dateFormat(scope.row.end_time, 'YYYY-mm-dd')}}
                    </el-space>
                    <el-space v-if="scope.row.date_status==1" style="color: #00bfb5">{{'(临近)'}}</el-space>
                    <el-space v-if="scope.row.date_status==2" style="color: red">{{'(过期)'}}</el-space>
                  </template>
                </el-table-column>
                <el-table-column prop="opt" label="操作" align="center" width="120">
                  <template #default="scope">
                    <el-button @click="removeBind(scope.row, scope.$index)" style="color: #04BAB1;font-weight: bold;" text>移除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
      <div class="save-bind">
        <el-button type="primary" @click="submitBindForm()" style="width: 100px; height: 40px;">保存设置</el-button>
        <el-button @click="cancelBindForm()" style="width: 100px; height: 40px;">取消</el-button>
      </div>
    </el-dialog>

  </div>
</template>
<style scoped lang="scss">
  :deep(.el-dropdown-menu__item i) {
    margin: 0;
  }
  :deep(.sales-binding .el-dialog__body){
    padding: 10px 0 !important;
  }

  .regions{
    display: flex;
    align-items: center;
    .el-button{
      margin-left: 10px;
    }
  }

  .sale-info{
    /*height: 50px;*/
    min-height: 50px;
    margin: 0 20px;
    background-color: #f2f2f2;
    display: flex;
    justify-content: center;
    align-items: center;
    el-span{
      width: 20%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
    }
  }

  .sales-bind {
    display: flex;
    top: 50px;
    height: 500px;
    margin-bottom: 10px;
    border-bottom: 1px solid #e5e7eb;
    .sale-info{
      width: 100%;
      background-color: #f2f2f2;
    }
    .list {
      margin: 10px 20px;
      .list-title{
        height: 15%;
        display: flex;
        .text-info{
          flex: 1;
          display: flex;
          align-items: center;
          font-size: 20px;
          color: #04BAB1;
        }
        .bind-btn{
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: end;
        }
      }
      .list-content{
        height: 80%;
        border-radius: 10px;
        border: 1px solid black;
        .list-search{
          margin: 10px 5px;
          width: 100%;
          height: 15%;
          display: flex;
          align-items: center;
          el-span{
            width: 70px;
            font-weight: bold;
          }
          // :deep(.el-button-group){
          //   width: 120px !important;
          // }
          :deep(.el-cascader){
            width: 170px !important;
          }
          .el-input{
            width: auto;
          }
        }
        .list-table{
          height: 75%;
          overflow-y: auto;
        }
      }
    }

    .agent-list {
      flex: 1;
      width: 35%;
    }

    .bind-list {
      flex: 1;
      width: 65%;
    }
  }
  .save-bind{
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: end;
    margin-right: 20px;
  }

  .had-bind{
    color: #04BAB1;
  }

</style>
