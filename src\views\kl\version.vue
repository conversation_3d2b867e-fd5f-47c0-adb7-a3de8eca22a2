<script setup lang="ts">
  import dayjs from "dayjs";
  import { Auth } from "/@/utils/auth"
  import { FormInstance, ElMessageBox } from "element-plus";
  import { reactive, ref, onMounted } from "vue";
  import { EpTableProBar } from "/@/components/ReTable";
  import { Switch, message } from "@pureadmin/components";
  import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
  import { versionListApi, versionAddApi, versionDeleteApi } from "/@/api/kl"

  const formRef = ref<FormInstance>();
  const ruleFormRef = ref<FormInstance>()
  const Data =  reactive({
    dialogVisible: false,
    search_from: {
      keyword: "",
      order: "id",
      sort: "DESC",
      page_size: 10,
      page: 1,
      total: 0,
    },
    loading_from: false,
    loading: true,
    data_list: [],
    data_from: {
      id: 0,
      name: '',
    },
    rules: {
      name: [
        { required: true, message: '版本名称必填', trigger: 'change' },
      ]
    }
  })

  async function handleDelete(row) {
    let { code } = await versionDeleteApi({ids: [row.id]});
    if(code === 0){
      message.success("删除成功")
      onSearch()
    }
  }
  function handleCurrentChange(val: number) {
    Data.search_from.page = val
    onSearch()
  }
  function handleSizeChange(val: number) {
    Data.search_from.page_size = val
    onSearch()
  }
  function handleSelectionChange(val) {
    console.log("handleSelectionChange", val);
  }

  function handleUpdate(row) {
    cancelForm(ruleFormRef.value)
    Data.data_from.name = ""
    Data.dialogVisible = true
  }

  async function onSearch() {
    Data.loading = true;
    let { data } = await versionListApi(Data.search_from);
    Data.data_list = data.list;
    Data.search_from.total = data.total
    Data.loading = false;
  }
  const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async(valid, fields) => {
      if (valid) {
        Data.loading_from = true
        let { code, msg } = await versionAddApi(Data.data_from);
        Data.loading_from = false
        if(code === 0){
          message.success(msg)
          Data.dialogVisible = false
          onSearch()
        }
      }
    })
  }

  const cancelForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    Data.dialogVisible = false
    formEl.resetFields()
  }

  onMounted(() => {
    onSearch();
  });
</script>

<template>
  <div class="main">
    <el-form ref="formRef" :inline="true" :model="Data.search_from" class="bg-white w-99/100 pl-8 pt-4">
      <el-form-item label="版本名称：" prop="keyword">
        <el-input style="width: 200px;" v-model="Data.search_from.keyword" placeholder="请输入版本名称" clearable />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('search')"
          :loading="Data.loading"
          @click="onSearch">搜索</el-button>
        <el-button :icon="useRenderIcon('refresh')" @click="resetForm(formRef)">重置</el-button>
      </el-form-item>
    </el-form>

    <EpTableProBar
      :title="'版本列表,共'+Data.data_list.length+'个版本'"
      :loading="Data.loading"
      :columnList="[
        {label: '版本名称', show: true},
        {label: '数据包', show: true},
        {label: '创建时间', show: true},
        {label: '操作人', show: true}]"
      :dataList="Data.data_list"
      @refresh="onSearch">
      <template #buttons>
        <el-button v-if="Auth('kl-version/add')" type="primary" :icon="useRenderIcon('add')" @click="handleUpdate(null)">添加版本</el-button>
      </template>
      <template v-slot="{ size, checkList }">
        <el-table
          border
          table-layout="auto"
          :size="size"
          :data="Data.data_list"
          :header-cell-style="{ background: '#fafafa', color: '#606266' }"
          @selection-change="handleSelectionChange">
          <el-table-column v-if="checkList.includes('版本名称')" label="版本名称" align="center" prop="name" />
          <el-table-column v-if="checkList.includes('数据包')" label="更新包" align="center" prop="data_path"/>
          <el-table-column v-if="checkList.includes('创建时间')" label="创建时间" align="center" prop="create_at"/>
          <el-table-column v-if="checkList.includes('操作人')" label="操作人" align="center" prop="operator_name"/>
          <el-table-column fixed="right" label="操作" align="center">
            <template #default="scope">
              <el-button
                v-if="Auth('kl-version/download')"
                class="reset-margin"
                type="text"
                :size="size"
                :icon="useRenderIcon('edits')">
                <a target="downloadFile" :href="scope.row.data_full_path">下载数据包</a>
              </el-button>
              <el-popconfirm v-if="Auth('kl-version/delete')" title="是否确认删除?" @confirm="handleDelete(scope.row)">
                <template #reference>
                  <el-button
                    class="reset-margin"
                    type="text"
                    :size="size"
                    :icon="useRenderIcon('delete')">
                    删除
                  </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          class="flex justify-end mt-4"
          :small="size === 'small' ? true : false"
          v-model:page-size="Data.search_from.page_size"
          :page-sizes="[10, 20, 30, 50]"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="Data.search_from.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"/>
      </template>
    </EpTableProBar>

    <el-dialog v-model="Data.dialogVisible"
      title="添加版本"
      width="600px"
      draggable>
      <el-form
        v-loading="Data.loading_from"
        ref="ruleFormRef"
        :model="Data.data_from"
        :rules="Data.rules"
        label-width="80px">
        <el-form-item label="版本名称" prop="name">
          <el-input v-model="Data.data_from.name" show-word-limit maxlength="50"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm(ruleFormRef)">提交</el-button>
          <el-button @click="cancelForm(ruleFormRef)">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

  </div>
</template>
<style scoped lang="scss">
  :deep(.el-dropdown-menu__item i) {
    margin: 0;
  }
</style>
