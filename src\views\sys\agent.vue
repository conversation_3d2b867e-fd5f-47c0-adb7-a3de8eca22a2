<script setup lang="ts">
  import dayjs from "dayjs";
  import { FormInstance, ElMessageBox } from "element-plus";
  import { reactive, ref, onMounted, watch } from "vue";
  import { EpTableProBar } from "/@/components/ReTable";
  import { Switch, message } from "@pureadmin/components";
  import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
  import {
    agentListApi,
    agentPersonalApi,
    agentAddApi,
    agentEditApi,
    agentDeleteApi,
    agentSetStatusApi,
    hospitalListApi,
    adminListApi,
    adminDepartmentApi,
    getUserRegionApi
  } from "/@/api/sys"
  import { getRegionApi } from "/@/api/public"
  import { Auth, AuthCount } from "/@/utils/auth";
  import { dateFormat } from "/@/utils/tool";
  import {storageSession} from "/@/utils/storage";

  const formRef = ref<FormInstance>();
  const ruleFormRef = ref<FormInstance>()
  let switchLoadMap = ref({});
  const Data =  reactive({
    dialogVisible: false,
    search_from: {
      name: "",
      region_id: [],
      status: "",
      is_all:"",
      page_size: 10,
      page: 1,
      total: 0,
    },
    loading: true,
    data_list: [],
    region_tree: [],
    edit_region_tree: [],
    user_region_tree: [],
    hospital_region_tree: [],
    data_from: {
      group: "",
      name: "",
      type: '1',
      time: null,
      start_time: "",
      end_time: "",
      license_code: "",
      address: "",
      liaison: "",
      phone: "",
      email: "",
      region_id: [],
      hospital_id: [],
      is_all: '0',
      length: 0,
      create_at: "",
    },
    hospital_list: [],
    is_all_options: [
      { value: "0", label: "否"},
      { value: "1", label: "是"},
    ],
    agent_type_options: [
      { value: "1", label: "区域代理商"},
      { value: "2", label: "报单代理商"},
    ],
    rules: {
      name: [
        {required: true, message: '代理商名称必填', trigger: 'blur'},
        {min: 1, max: 20, message: '代理商名称长度限定为20个字符以内', trigger: ['blur', 'change']},
      ],
      type: [
        {required: true, message: '代理商类型必填', trigger: 'blur'},
      ],
      is_all: [
        {required: true, message: '代理商范围必填', trigger: 'blur'},
      ],
      time: [
        {required: true, message: '代理时限必填', trigger: 'blur'},
      ],
      region_id: [
        {required: true, message: '代理区域必填', trigger: 'blur'},
      ],
      address: [
        {max: 50, message: '地址长度限定为50个字符', trigger: ['blur', 'change']},
      ],
      liaison: [
        {max: 20, message: '联系人长度限定为20个字符', trigger: ['blur', 'change']},
      ],
      phone: [
        {validator: checkPhone, trigger: ['blur', 'change']},
      ],
      email: [
        {type:'email', message: '邮箱格式异常', trigger: ['blur', 'change']},
        {max: 30, message: '邮箱长度限定为30个字符', trigger: ['blur', 'change']},
      ],
    },
    user_info: null,
  })

  //校验联系电话
  function checkPhone(rule, value, callback) {
    if (value) {
      let isPhone = new RegExp("^1[34578][0-9]{9}$",'i')
      let isLand = new RegExp("^(0[0-9]{2,3}-)([0-9]{7,8})$",'i')
      if( isPhone.test(value) || isLand.test(value) ){
        callback();
      }else{
        callback(new Error('电话格式异常'));
      }
    }else {
      callback();
    }
  }

  //处理代理商区域，当为全国范围时，将区域置为空
  function handleRegion(value) {
      if(value == '1') {
        Data.data_from.region_id = []
      }
  }
  //处理代理商类型，当为报单代理商时，全国代理默认为否，不能点击
  function handleAgentType(value) {
    Data.data_from.region_id = []
    Data.data_from.hospital_id = []
    if(value == '2') {
      Data.data_from.is_all = '0'
      Data.data_from.hospital_id.push("")
    }
    Data.data_from.region_id.push("")
  }

  async function handleDelete(row) {
    let { code } = await agentDeleteApi({groups: [row.group]});
    if(code === 0){
      message.success("删除成功")
      onSearch()
    }
  }
  function handleCurrentChange(val: number) {
    Data.search_from.page = val
    onSearch()
  }
  function handleSizeChange(val: number) {
    Data.search_from.page_size = val
    onSearch()
  }

  function onChange(checked, { $index, row }) {
    ElMessageBox.confirm(
      `确认要<strong>${
        row.status === 0 ? "停用" : "启用"
      }</strong><strong style='color:var(--el-color-primary)'>${
        row.name
      }</strong>吗?`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    )
      .then(() => {
        switchLoadMap.value[$index] = Object.assign({}, switchLoadMap.value[$index], {loading: true});
        agentSetStatusApi({group: row.group, status: row.status}).then(ret => {
          switchLoadMap.value[$index] = Object.assign({}, switchLoadMap.value[$index], {loading: false});
          if(ret.code === 0){
            message.success("修改成功");
          }
          onSearch()
        })

      })
      .catch(() => {
        row.status === 0 ? (row.status = 1) : (row.status = 0);
      });
  }
  async function onSearch() {
    Data.loading = true;
    //根据用户权限，判断查询全部代理商，还是用户已绑定代理商
    let response
    if(Auth('agent/list')) {
      response = await agentListApi(Data.search_from);
    }else {
      response = await agentPersonalApi(Data.search_from);
    }
    let data = response.data
    if(data.total <= Data.search_from.page_size &&  Data.search_from.page > 1){
      Data.search_from.page = 1
      onSearch()
    }
    Data.data_list = data.list;
    Data.search_from.total = data.total;
    Data.loading = false;
  }
  const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async(valid, fields) => {
      if (valid) {
        var err_msg = ''
        //判断是否有重复区域或医院
        var dup_hospital = []
        var dup_region = []
        if(Data.data_from.is_all=='0') {
          Data.data_from.region_id.forEach((item, index) => {
            if(item?.length == 0 || item == null) {
              err_msg = '代理商区域必填'
              return
            }
            if(dup_region.indexOf(item[item.length-1]) != -1) {
              err_msg = '代理商区域重复'
              return
            }
            dup_region.push(item[item.length-1])
          })
        }
        if(err_msg) {
          message.error(err_msg)
          return
        }

        if(Data.data_from.time && Data.data_from.time?.length == 2) {
          if(Data.data_from.time[0]==''||Data.data_from.time[1]==''){
            message.error('代理商时限必填')
            return
          }
          Data.data_from.start_time = dateFormat(Data.data_from.time[0], 'YYYY-mm-dd')
          Data.data_from.end_time = dateFormat(Data.data_from.time[1], 'YYYY-mm-dd')
        }else {
          message.error('代理商时限必填')
          return
        }

        if(Data.data_from.group != ""){
          let { code, msg } = await agentEditApi(Data.data_from);
          if(code === 0){
            message.success(msg)
            Data.dialogVisible = false
            onSearch()
          }
        }else{
          let { code, msg } = await agentAddApi(Data.data_from);
          if(code === 0){
            message.success(msg)
            Data.dialogVisible = false
            onSearch()
          }
        }
      }
    })
  }

  const cancelForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    Data.dialogVisible = false
    Data.add_regions = []
    Data.data_from.region_id = []
    formEl.resetFields()
  }

  function handleUpdate(row) {
    Data.edit_region_tree = JSON.parse(JSON.stringify(Data.region_tree))
    if(row != null ) {
      Data.data_from = JSON.parse(JSON.stringify(row))
      // is_all.value = Data.data_from.is_all.toString()
      if(row.region_ids?.length > 0) {
        Data.data_from.region_id = []
        row.region_ids.forEach((item, index) => {
          let region_bit_arr = item.split("")
          if(region_bit_arr.length == 6){
            if(!(region_bit_arr[4]==0 && region_bit_arr[5] == 0)) {
              Data.data_from.region_id.push([
                region_bit_arr[0]+region_bit_arr[1]+"0000",
                region_bit_arr[0]+region_bit_arr[1]+region_bit_arr[2]+region_bit_arr[3]+'00',
                item
              ])
            }else if(region_bit_arr[4]==0 && region_bit_arr[5] == 0 && (!(region_bit_arr[2]==0 && region_bit_arr[3] == 0))) {
              Data.data_from.region_id.push([
                region_bit_arr[0]+region_bit_arr[1]+"0000",
                item
              ])
            }else {
              Data.data_from.region_id.push([item])
            }
          }
        })
      }
      Data.data_from.hospital_id = []
      Data.data_from.time = [Data.data_from.start_time, Data.data_from.end_time]
      Data.data_from.type = Data.data_from.type.toString()
      Data.data_from.is_all = Data.data_from.is_all.toString()
    } else {
      Data.data_from = {
        group: "",
        name: "",
        type: '1',
        time: null,
        start_time: "",
        end_time: "",
        license_code: "",
        address: "",
        liaison: "",
        phone: "",
        email: "",
        region_id: [],
        hospital_id: [],
        is_all: '0',
        length: 0,
        create_at: "",
      }
      Data.data_from.region_id.push("")
    }
    Data.dialogVisible = true
  }

  //检查代理商区域
  function checkAgentRegion(region_id, region_bit_arr, region_names){
    let region_name_arr = region_names?.split("/")
    var province = false
    Data.edit_region_tree?.forEach(item => {
      if(region_bit_arr[0]+region_bit_arr[1]+"0000" == item.value){
        province = true
        var city = false
        item?.children?.forEach(item1 => {
          if(region_bit_arr[0]+region_bit_arr[1]+region_bit_arr[2]+region_bit_arr[3]+'00' == item1.value){
            city = true
            var district = false
            item1?.children?.forEach(item2 => {
              if(region_id == item2.value){
                district = true
              }
            })
            if(!district){
              if(item1?.children) {
                item1.children?.push({
                  value: region_id,
                  label: region_name_arr?.[2],
                  disabled: true,
                })
              }else {
                item1.children = [{
                  value: region_id,
                  label: region_name_arr?.[2],
                  disabled: true,
                }]
              }
            }
          }
        })
        if(!city){
          if(item?.children){
            item.children?.push({
              value: region_bit_arr[0]+region_bit_arr[1]+region_bit_arr[2]+region_bit_arr[3]+'00',
              label: region_name_arr?.[1],
              disabled: true,
              children: [{
                value: region_id,
                label: region_name_arr?.[2],
                disabled: true,
              }],
            })
          }else {
            item.children = [{
              value: region_bit_arr[0]+region_bit_arr[1]+region_bit_arr[2]+region_bit_arr[3]+'00',
              label: region_name_arr?.[1],
              disabled: true,
              children: [{
                value: region_id,
                label: region_name_arr?.[2],
                disabled: true,
              }],
            }]
          }
        }
      }
    })
    if(!province){
      Data.edit_region_tree?.push({
        value: region_bit_arr[0]+region_bit_arr[1]+"0000",
        label: region_name_arr?.[0],
        disabled: true,
        children: [{
          value: region_bit_arr[0]+region_bit_arr[1]+region_bit_arr[2]+region_bit_arr[3]+'00',
          label: region_name_arr?.[1],
          disabled: true,
          children: [{
            value: region_id,
            label: region_name_arr?.[2],
            disabled: true,
          }],
        }],
      })
    }

  }

  async function getRegion() {
    let { data } = await getRegionApi();
    Data.region_tree = data;
    Data.hospital_region_tree = data;
    getUserRegion()
  }

  //获取用户区域树
  async function getUserRegion(){
    //显示用户区域
    let { code, data } = await getUserRegionApi();
    if(code === 0){
      Data.user_region_tree = data?.tree
      if(!Auth("agent/list")){
        Data.region_tree = JSON.parse(JSON.stringify(data?.tree))
      }
    }
  }

  onMounted(() => {
    Data.user_info = storageSession.getItem("info")
    getRegion()
    onSearch();
  });

  const propsSearch = {
    checkStrictly: true,
  }

  function handleAddRegion() {
    Data.data_from.region_id.push("")
    //报单代理商，则增加对应医院
    if(Data.data_from.type == '2'){
      Data.data_from.hospital_id.push("")
      Data.hospital_list.push([])
    }
  }
  function handleDelRegion(index) {
    Data.data_from.region_id.splice(index, 1)
    //报单代理商，则删除对应医院
    if(Data.data_from.type == '2'){
      Data.data_from.hospital_id.splice(index, 1)
      Data.hospital_list.splice(index, 1)
    }
  }

  function formatOptionLabel(options, value) {
    var label = ''
    options?.forEach((item, index) => {
      if(item.value == value) {
        label = item.label
        return
      }
    })
    return label
  }

  //根据区域id获取医院列表
  function changeDetailsRegionHandel(value, index) {
    Data.hospital_list[index] = []
    getHospitalList(value, index)
  }

  async function getHospitalList(region_id, index) {
    let { data } = await hospitalListApi({'region_id': region_id, 'page_size': 100000, "status":1})
    Data.hospital_list[index] = data.list;
  }
</script>

<template>
  <div class="main">
    <el-form ref="formRef" :inline="true" :model="Data.search_from" class="bg-white w-99/100 pl-8 pt-4">
      <el-form-item label="代理商名称：" prop="name">
        <el-input style="width: 200px;" v-model="Data.search_from.name" placeholder="请输入代理商户名称" clearable />
      </el-form-item>
      <el-form-item label="所属区域" prop="region_id">
        <el-cascader :props="propsSearch" :options="Data.region_tree"  v-model="Data.search_from.region_id" clearable />
      </el-form-item>
      <el-form-item label="状态：" prop="status">
        <el-select v-model="Data.search_from.status" placeholder="请选择状态" clearable>
          <el-option label="已开启" value="1" />
          <el-option label="已关闭" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('search')"
          :loading="Data.loading"
          @click="onSearch">搜索</el-button>
        <el-button :icon="useRenderIcon('refresh')" @click="resetForm(formRef)">重置</el-button>
      </el-form-item>
    </el-form>

    <EpTableProBar
      title="代理商列表"
      :loading="Data.loading"
      :columnList="[
        {label: '编号', show: true},
        {label: '代理商类型', show: true},
        {label: '代理商名称', show: true},
        {label: '全国范围', show: false},
        {label: '代理商区域', show: true},
        {label: '状态', show: true},
        {label: '创建时间', show: true},
        {label: '更新时间', show: true},
        {label: '创建者', show: true},
        {label: '代理时间', show: true}
        ]"
      :dataList="Data.data_list"
      @refresh="onSearch">
      <template #buttons>
        <el-button v-if="Auth('agent/add')" type="primary" :icon="useRenderIcon('add')" @click="handleUpdate(null)">新增代理商</el-button>
      </template>
      <template v-slot="{ size, checkList }">
        <el-table
          border
          table-layout="auto"
          :size="size"
          :data="Data.data_list"
          :header-cell-style="{ background: '#fafafa', color: '#606266' }"
          >
          <el-table-column v-if="checkList.includes('编号')" label="编号" align="center" width="100" prop="id">
            <template #default="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column v-if="checkList.includes('代理商类型')" label="代理商类型" align="center" width="100" prop="type">
            <template #default="scope">
              {{ formatOptionLabel(Data.agent_type_options, scope.row.type) }}
            </template>
          </el-table-column>
          <el-table-column v-if="checkList.includes('代理商名称')" label="代理商名称" align="center" prop="name" />
          <el-table-column v-if="checkList.includes('全国范围')" label="全国范围" align="center" width="100" prop="is_all">
            <template #default="scope">
              {{ formatOptionLabel(Data.is_all_options, scope.row.is_all) }}
            </template>
          </el-table-column>
          <el-table-column v-if="checkList.includes('代理商区域')" label="代理商区域" align="center" width="200" prop="region_names">
            <template #default="scope">
              <div>
                <div v-if="scope.row.is_all==0" v-for="(v, k) in scope.row.region_names">{{ v }}</div>
                <div v-else>全国</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column v-if="checkList.includes('状态')" label="状态" align="center" width="100" prop="status">
            <template #default="scope">
              <Switch
                :disabled="!Auth('agent/set-status')"
                :size="size === 'small' ? 'small' : 'default'"
                :loading="switchLoadMap[scope.$index]?.loading"
                v-model:checked="scope.row.status"
                :checkedValue="1"
                :unCheckedValue="0"
                checked-children="已开启"
                un-checked-children="已关闭"
                @change="checked => onChange(checked, scope)"
              />
            </template>
          </el-table-column>
          <el-table-column v-if="checkList.includes('创建时间')" label="创建时间" align="center" width="170" prop="create_at"/>
          <el-table-column v-if="checkList.includes('更新时间')" label="更新时间" align="center" width="170" prop="update_at"/>
          <el-table-column v-if="checkList.includes('创建者')" label="创建者" align="center" prop="create_name"/>
          <el-table-column v-if="checkList.includes('代理时间')" label="代理时间" align="center" width="250">
            <template #default="scope">
              <el-space v-if="scope.row.start_time!=''">
                {{ dateFormat(scope.row.start_time, 'YYYY-mm-dd') + ' 至 ' + dateFormat(scope.row.end_time, 'YYYY-mm-dd')}}
              </el-space>
              <el-space v-if="scope.row.start_time!=''&&scope.row.date_status==1" style="color: #00bfb5">{{'(临近)'}}</el-space>
              <el-space v-if="scope.row.start_time!=''&&scope.row.date_status==2" style="color: red">{{'(过期)'}}</el-space>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="center" width="200">
            <template #default="scope">
              <el-button
                v-if="Auth('agent/edit')"
                class="reset-margin"
                type="text"
                :size="size"
                @click="handleUpdate(scope.row)"
                :icon="useRenderIcon('edits')">
                修改
              </el-button>
              <el-popconfirm title="是否确认删除?" @confirm="handleDelete(scope.row)">
                <template #reference>
                  <el-button
                    v-if="Auth('agent/delete')"
                    class="reset-margin"
                    type="text"
                    :size="size"
                    :icon="useRenderIcon('delete')">
                    删除
                  </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          class="flex justify-end mt-4"
          :small="size === 'small' ? true : false"
          v-model:page-size="Data.search_from.page_size"
          :page-sizes="[10, 20, 30, 50]"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="Data.search_from.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"/>
      </template>
    </EpTableProBar>

    <el-dialog v-model="Data.dialogVisible"
      :title="Data.data_from.group == ''?'添加代理商':'编辑代理商'"
      width="500px"
      draggable
      :close-on-click-modal="false"
      @close="cancelForm(ruleFormRef)"
    >
      <el-form
        ref="ruleFormRef"
        :model="Data.data_from"
        :rules="Data.rules"
        label-width="120px">
        <el-form-item label="代理商名称" prop="name">
          <el-input v-model="Data.data_from.name" show-word-limit maxlength="20"/>
        </el-form-item>
        <el-form-item label="代理商类型" prop="type">
          <el-radio-group v-model="Data.data_from.type">
            <el-radio v-for="(item,index) in Data.agent_type_options" :label="item.value" :value="item.value" @click="handleAgentType(item.value)">{{item.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="代理商时限" prop="time">
          <el-date-picker
            v-model="Data.data_from.time"
            type="daterange"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </el-form-item>
        <el-form-item label="通讯地址" prop="address">
          <el-input v-model="Data.data_from.address" show-word-limit maxlength="50"/>
        </el-form-item>
        <el-form-item label="联系人" prop="liaison">
          <el-input v-model="Data.data_from.liaison" show-word-limit maxlength="20"/>
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="Data.data_from.phone" />
        </el-form-item>
        <el-form-item label="联系邮箱" prop="email">
          <el-input v-model="Data.data_from.email" />
        </el-form-item>
        <el-form-item :label="Data.data_from.region_id.length>1?'代理商区域1':'代理商区域'" prop="region_id">
          <div class="regions">
            <el-cascader :props="propsSearch" :options="Data.user_region_tree"  v-model="Data.data_from.region_id[0]" style="width:200px" clearable/>
            <el-button v-if="Data.data_from.region_id?.length>1" type="primary"
              size="small" circle :icon="useRenderIcon('remove')" @click="handleDelRegion(0)"></el-button>
            <el-button type="primary" size="small" circle :icon="useRenderIcon('adds')" @click="handleAddRegion()"></el-button>
          </div>
        </el-form-item>
        <div v-for="(v,k) in Data.data_from.region_id">
          <el-form-item v-if="k>0"  :label="'代理商区域'+(k+1)" prop="region_id">
            <div class="regions">
              <el-cascader :props="propsSearch" :options="Data.user_region_tree"  v-model="Data.data_from.region_id[k]" style="width:200px" clearable/>
              <el-button type="primary" size="small" circle :icon="useRenderIcon('remove')" @click="handleDelRegion(k)"></el-button>
            </div>
          </el-form-item>
        </div>
        <el-form-item>
          <el-button type="primary" @click="submitForm(ruleFormRef)">提交</el-button>
          <el-button @click="cancelForm(ruleFormRef)">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

  </div>
</template>
<style scoped lang="scss">
  :deep(.el-dropdown-menu__item i) {
    margin: 0;
  }

  .regions{
    display: flex;
    align-items: center;
    .el-button{
      margin-left: 10px;
    }
  }

</style>
