import { http } from "../utils/http";

//设置订单状态
export const orderMealOptionsApi = (params?: object) => {
  return http.request("get", "order/meal-options", { params });
};


// 获取全部订单列表
export const orderListApi = (data) => {
  return http.request("post", "order/list", { data });
};

//获取个人订单列表
export const orderPersonalApi = (data) => {
  return http.request("post", "order/personal", { data });
};

//创建订单
export const orderCreateApi = (data: object) => {
  return http.request("post", "order/create", { data });
};

//存为草稿
export const orderDraftApi = (data: object) => {
  return http.request("post", "order/draft", { data });
};

//删除订单
export const orderDeleteApi = (data) => {
  return http.request("post", "order/delete", { data });
};

//设置订单状态
export const orderSetStatusApi = (params?: object) => {
  return http.request("get", "order/set-status", { params });
};

//订单详情
export const orderDetailsApi = (params?: object) => {
  return http.request("get", "order/details", { params });
};

//订单设备详情
export const orderDeviceDetailsApi = (params?: object) => {
  return http.request("get", "order/device_details", { params });
};

//订单审核
export const orderManagerReviewApi = (data) => {
  return http.request("post", "order/manager_review", { data });
};

//订单审核
export const orderReviewApi = (data) => {
  return http.request("post", "order/review", { data });
};

//订单部署
export const orderDeloyApi = (data) => {
  return http.request("post", "order/deploy", { data });
};

//订单撤销
export const orderRevokeApi = (data) => {
  return http.request("post", "order/revoke", { data });
}

//订单升级
export const orderUpgradeApi = (data) => {
  return http.request("post", "order/upgrade", { data });
};

//订单设备信息更新
export const orderDeviceUpdateApi = (data) => {
  return http.request("post", "order/update-device", { data });
};

//创建升级订单
export const upgradeOrderCreateApi = (data) => {
  return http.request("post", "order/upgrade-order-create", { data });
};

//订单设备信息更新
export const orderTransferApi = (data) => {
  return http.request("post", "order/transfer", { data });
};

//设为医生组
export const getDoctorList = () => {
  return http.request("get", "order/doctor-list",);
};


//订单 指派医生
export const setAssignDoctor = (data: object) => {
  return http.request("post", "order/assign",{ data });
};

//订单 医生 反馈
export const setFeedbackDoctor = (data: object) => {
  return http.request("post", "order/feedback",{ data });
};

//修改信息 管理员
export const setaAminUpdate = (data: object) => {
  return http.request("post", "order/admin-update",{ data });
};




