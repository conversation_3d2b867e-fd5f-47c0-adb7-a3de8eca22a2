<script setup lang="ts">
  import dayjs from "dayjs";
  import { FormInstance, ElMessageBox } from "element-plus";
  import { reactive, ref, onMounted } from "vue";
  import { EpTableProBar } from "/@/components/ReTable";
  import { Switch, message } from "@pureadmin/components";
  import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
  import { clientListApi, licenceSetStatusApi, licenceDeleteApi } from "/@/api/licence"
  import {getRegionApi} from "/@/api/public";
  import {hospitalListApi} from "/@/api/sys";
  import { Auth } from "/@/utils/auth"

  const formRef = ref<FormInstance>();
  const ruleFormRef = ref<FormInstance>()
  let switchLoadMap = ref({});
  const Data =  reactive({
    dialogVisible: false,
    search_from: {
      ukey_code: "",
      hospital_id: null,
      role: 1,
      uuid: "",
      author_number: "",
      device_serial_number: "",
      status: "",
      page_size: 10,
      page: 1,
      total: 0
    },
    loading: true,
    region_tree: [],
    hospital_list: [],
    data_list: [],
  })

  function handleCurrentChange(val: number) {
    Data.search_from.page = val
    onSearch()
  }
  function handleSizeChange(val: number) {
    Data.search_from.page_size = val
    onSearch()
  }

  async function handleDelete(row) {
    let { code } = await licenceDeleteApi({ids: [row.id]});
    if(code === 0){
      message.success("删除成功")
      onSearch()
    }
  }

  function onChange(checked, { $index, row }) {
    ElMessageBox.confirm(
      `确认要<strong>${
        row.status === 0 ? "停用" : "启用"
      }</strong><strong style='color:var(--el-color-primary)'>${
        row.author_number
      }</strong>授权吗?`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    )
      .then(() => {
        switchLoadMap.value[$index] = Object.assign({}, switchLoadMap.value[$index], {loading: true});
        licenceSetStatusApi({id: row.id, status: row.status}).then(ret => {
          switchLoadMap.value[$index] = Object.assign({}, switchLoadMap.value[$index], {loading: false});
          if(ret.code === 0){
            message.success("修改成功");
          }
          onSearch()
        })

      })
      .catch(() => {
        row.status === 0 ? (row.status = 1) : (row.status = 0);
      });
  }
  async function onSearch() {
    Data.loading = true;
    let { data } = await clientListApi(Data.search_from);
    if(data.total <= Data.search_from.page_size &&  Data.search_from.page > 1){
      Data.search_from.page = 1
      onSearch()
    }
    Data.data_list = data.list;
    Data.search_from.total = data.total;
    Data.loading = false;
  }
  const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  async function getRegion() {
    let { data } = await getRegionApi();
    Data.region_tree = data;
  }

  function changeRegionHandel(value) {
    Data.search_from.hospital_id = "";
    Data.hospital_list = []
    getHospitalList(value)
  }

  async function getHospitalList(region_id) {
    let { data } = await hospitalListApi({'region_id': region_id, 'page_size': 1000})
    Data.hospital_list = data.list;
  }

  onMounted(() => {
    getRegion()
    onSearch();
  });
</script>

<template>
  <div class="main">
    <el-form ref="formRef" :inline="true" :model="Data.search_from" class="bg-white w-99/100 pl-8 pt-4">
      <el-form-item label="U-Key代码：" prop="ukey_code">
        <el-input style="width: 200px;" v-model="Data.search_from.ukey_code" placeholder="请输入U-Key代码" clearable />
      </el-form-item>
      <el-form-item label="设备序列号：" prop="serial_number">
        <el-input style="width: 200px;" v-model="Data.search_from.serial_number" placeholder="请输入设备序列号" clearable />
      </el-form-item>
      <el-form-item label="授权码：" prop="author_number">
        <el-input style="width: 200px;" v-model="Data.search_from.author_number" placeholder="请输入授权码" clearable />
      </el-form-item>
      <el-form-item label="UUID：" prop="uuid">
        <el-input style="width: 200px;" v-model="Data.search_from.uuid" placeholder="请输入UUID" clearable />
      </el-form-item>
      <el-form-item label="选择区域:" prop="region_ids">
        <el-cascader :options="Data.region_tree"  v-model="Data.search_from.region_ids" @change="changeRegionHandel"/>
      </el-form-item>
      <el-form-item label="绑定单位:" prop="hospital_id">
        <el-select v-model="Data.search_from.hospital_id" placeholder="请选择授权单位" clearable>
          <el-option v-for="(item,index) in Data.hospital_list" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态：" prop="status">
        <el-select v-model="Data.search_from.status" placeholder="请选择状态" clearable>
          <el-option label="已开启" value="1" />
          <el-option label="已关闭" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('search')"
          :loading="Data.loading"
          @click="onSearch">搜索</el-button>
        <el-button :icon="useRenderIcon('refresh')" @click="resetForm(formRef)">重置</el-button>
      </el-form-item>
    </el-form>

    <EpTableProBar
      title="客户端记录"
      :loading="Data.loading"
      :columnList="[
        {label: '授权编号', show: true},
        {label: '配对服务', show: true},
        {label: '被授权单位', show: true},
        {label: 'UKey代码', show: true},
        {label: '设备系列号', show: true},
        {label: 'UUID', show: true},
        {label: '版本号', show: true},
        {label: 'AI版本号', show: true},
        {label: '授权时间', show: true},
        {label: '配对时间', show: true},
        {label: '授权状态', show: true}]"
      :dataList="Data.data_list"
      @refresh="onSearch">
      <template #buttons>

      </template>
      <template v-slot="{ size, checkList }">
        <el-table
          border
          table-layout="auto"
          :size="size"
          :data="Data.data_list"
          :header-cell-style="{ background: '#fafafa', color: '#606266' }"
          @selection-change="handleSelectionChange">
          <el-table-column v-if="checkList.includes('授权编号')" prop="author_number" label="授权码" align="center"></el-table-column>
          <el-table-column v-if="checkList.includes('配对服务')" prop="server_author_number" label="配对服务" align="center"></el-table-column>
          <el-table-column v-if="checkList.includes('被授权单位')" prop="hospital_name" label="被授权单位" align="center"></el-table-column>
          <el-table-column v-if="checkList.includes('UKey代码')" prop="ukey_code" label="UKey代码" align="center"></el-table-column>
          <el-table-column v-if="checkList.includes('设备系列号')" prop="device_serial_number" label="设备系列号" align="center"></el-table-column>
          <el-table-column v-if="checkList.includes('UUID')" prop="uuid" label="UUID" align="center"></el-table-column>
          <el-table-column v-if="checkList.includes('版本号')" prop="client_version" label="版本号" align="center"></el-table-column>
          <el-table-column v-if="checkList.includes('AI版本号')" prop="ai_version" label="AI版本号" align="center"></el-table-column>
          <el-table-column v-if="checkList.includes('配对时间')" prop="pair_at" label="配对时间" align="center"></el-table-column>
          <el-table-column v-if="checkList.includes('授权时间')" prop="update_at" label="授权时间" align="center"></el-table-column>
          <el-table-column v-if="checkList.includes('授权状态')" label="状态" align="center" prop="status">
            <template #default="scope">
              <Switch
                :disabled="!Auth('client/set-status')"
                :size="size === 'small' ? 'small' : 'default'"
                :loading="switchLoadMap[scope.$index]?.loading"
                v-model:checked="scope.row.status"
                :checkedValue="1"
                :unCheckedValue="0"
                checked-children="已开启"
                un-checked-children="已关闭"
                @change="checked => onChange(checked, scope)"
              />
            </template>
          </el-table-column>
<!--          <el-table-column fixed="right" label="操作" align="center">-->
<!--            <template #default="scope">-->
<!--              <el-button-->
<!--                class="reset-margin"-->
<!--                type="text"-->
<!--                :size="size"-->
<!--                @click="handleUpdate(scope.row)"-->
<!--                :icon="useRenderIcon('edits')">-->
<!--                记录-->
<!--              </el-button>-->
<!--              <el-popconfirm title="是否确认删除?" @confirm="handleDelete(scope.row)">-->
<!--                <template #reference>-->
<!--                  <el-button-->
<!--                    class="reset-margin"-->
<!--                    type="text"-->
<!--                    :size="size"-->
<!--                    :icon="useRenderIcon('delete')">-->
<!--                    删除-->
<!--                  </el-button>-->
<!--                </template>-->
<!--              </el-popconfirm>-->
<!--            </template>-->
<!--          </el-table-column>-->
        </el-table>
        <el-pagination
          class="flex justify-end mt-4"
          :small="size === 'small' ? true : false"
          v-model:page-size="Data.search_from.page_size"
          :page-sizes="[10, 20, 30, 50]"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="Data.search_from.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"/>
      </template>
    </EpTableProBar>

  </div>
</template>
