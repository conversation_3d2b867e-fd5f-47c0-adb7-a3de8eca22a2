import { $t } from "/@/plugins/i18n";
const Layout = () => import("/@/layout/index.vue");

const initRouter = {
  path: "/init",
  name: "init",
  value: "init",
  component: Layout,
  redirect: "/init/ukey",
  api: [],
  meta: {
    icon: "setting",
    title: "硬件初始化",
    i18n: true,
    rank: 4
  },
  children: [
    {
      path: "/init/ukey",
      name: "ukey",
      value: "ukey",
      component: () => import("/@/views/init/ukey.vue"),
      api: ['ukey/list', "ukey/add", "ukey/set-status", "ukey/delete"],
      meta: {
        title: "U-Key管理",
        icon: "admin-line",
      }
    },
    {
      path: "/init/leader",
      name: "leader",
      value: "leader",
      component: () => import("/@/views/init/leader.vue"),
      api: ['leader-key/list', 'leader-key/add', 'leader-key/set-status', 'leader-key/delete'],
      meta: {
        title: "主任密钥管理",
        icon: "virus-line",
      }
    }
  ]
};

export default initRouter;
