{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "strict": false, "jsx": "preserve", "importHelpers": true, "experimentalDecorators": true, "strictFunctionTypes": false, "skipLibCheck": true, "esModuleInterop": true, "isolatedModules": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "sourceMap": true, "baseUrl": ".", "allowJs": false, "resolveJsonModule": true, "lib": ["dom", "esnext"], "incremental": true, "paths": {"/@/*": ["src/*"], "@build/*": ["build/*"], "/#/*": ["types/*"]}, "types": ["node", "vite/client", "element-plus/global"], "typeRoots": ["./node_modules/@types/", "./types"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "types/*.d.ts", "mock/*.ts", "vite.config.ts"], "exclude": ["node_modules", "dist", "**/*.js"]}