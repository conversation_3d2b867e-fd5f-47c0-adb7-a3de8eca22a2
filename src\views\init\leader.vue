<script setup lang="ts">
  import { loadEnv } from "@build/index";
  import dayjs from "dayjs";
  import { Auth } from "/@/utils/auth"
  import { FormInstance, ElMessageBox, ElLoading } from "element-plus";
  import { reactive, ref, onMounted, onUnmounted, watch } from "vue";
  import { EpTableProBar } from "/@/components/ReTable";
  import { Switch, message } from "@pureadmin/components";
  import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
  import {
    leader<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    leader<PERSON><PERSON><PERSON>dd<PERSON><PERSON>,
    leader<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    leader<PERSON>eySetStatusApi,
    leader<PERSON>eyInfoApi
  } from "/@/api/init_hardware"
  import { hospitalListApi } from "/@/api/sys"
  import { getRegionApi } from "/@/api/public"
  import { download } from "/@/utils/tool";
  import { Base64 }  from "/@/utils/base64";

  const { VITE_PROXY_DOMAIN, VITE_PROXY_DOMAIN_REAL } = loadEnv();

  const formRef = ref<FormInstance>();
  const ruleFormRef = ref<FormInstance>()
  let switchLoadMap = ref({});

  const Data =  reactive({
    ws: null,
    dialogVisible: false,
    search_from: {
      serial_number: "",
      code: "",
      type: "",
      status: "",
      page_size: 10,
      page: 1,
      total: 0,
    },
    data_from: {
      tip: "",
      serial_number: "",
      code: "",
      region_id: [],
      hospital_id: "",
      manager: "",
      public: ""
    },
    loading: true,
    data_list: [],
    region_tree: [],
    hospital_list: [],
    role_options: {
      0: '管理员',
      1: `普通用户`,
    },
    key_data: {
      key_id: "",
      public_key: "",
      key_init_status: 0,
      msg_type: 0,
    },
    rules: {
      serial_number: [
        {required: true, message: '主任密钥硬件ID必填', trigger: 'blur'},
      ],
      code: [
        {required: true, message: '请输入主任密钥代码', trigger: 'blur'},
        {min: 5, max: 5, message: '主任密钥代码限定为5个字符', trigger: ['blur', 'change']},
      ],
      region_id: [
        {required: true, message: '区域必填', trigger: ['blur', 'change']},
      ],
      hospital_id: [
        {required: true, message: '单位必填', trigger: ['blur', 'change']},
      ],
      manager: [
        {required: true, message: '管理员名称必填', trigger: ['blur', 'change']},
        {min: 1, max: 20, message: '管理员名称不能超过20个字符', trigger: ['blur', 'change']},
      ],
      public: [
        {required: true, message: '普通用户名称必填', trigger: ['blur', 'change']},
        {min: 1, max: 20, message: '普通用户名称不能超过20个字符', trigger: ['blur', 'change']},
      ],
    },
  })


  function handleUpdate(row) {
    const loading = ElLoading.service({
      lock: true,
      text: '识别主任密钥...',
      background: 'rgba(0, 0, 0, 0.9)',
    })

    Data.data_from.tip = ""
    initSocket(loading)
  }
  //重置ukey
  function resetUkeyHandel(){
    if(Data.ws != null){
      Data.ws.send(JSON.stringify({msg_type:103}))
    }
  }
  async function handleDelete(row) {
    let { code } = await leaderKeyDeleteApi({ids: [row.id]});
    if(code === 0){
      message.success("删除成功")
      onSearch()
    }
  }
  function handleCurrentChange(val: number) {
    Data.search_from.page = val
    onSearch()
  }
  function handleSizeChange(val: number) {
    Data.search_from.page_size = val
    onSearch()
  }
  function handleSelectionChange(val) {
    console.log("handleSelectionChange", val);
  }

  function onChange(checked, { $index, row }) {
    ElMessageBox.confirm(
      `确认要<strong>${row.status === 0 ? "停用" : "启用"}</strong><strong style='color:var(--el-color-primary)'>${row.code}</strong>角色吗?`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    )
      .then(() => {
        switchLoadMap.value[$index] = Object.assign({}, switchLoadMap.value[$index], {loading: true});
        leaderKeySetStatusApi({id: row.id, status: row.status}).then(ret => {
          switchLoadMap.value[$index] = Object.assign({}, switchLoadMap.value[$index], {loading: false});
          if(ret.code === 0){
            message.success("修改成功");
          }
          onSearch()
        })
      })
      .catch(() => {
        row.status === 0 ? (row.status = 1) : (row.status = 0);
      });
  }
  async function onSearch() {
    Data.loading = true;
    let { data } = await leaderKeyListApi(Data.search_from);
    if(data.total <= Data.search_from.page_size &&  Data.search_from.page > 1){
      Data.search_from.page = 1
      onSearch()
    }
    Data.data_list = data.list;
    Data.search_from.total = data.total;
    Data.loading = false;
  }
  const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    getRegion()
    onSearch()
  });

  onUnmounted(() => {
    if(Data.ws != null) {
      Data.ws.close()
    }
  })

  const initSocket = (loading) => {
    try {
      Data.ws = new WebSocket("ws://127.0.0.1:26666/hardware/websocket"),
        // ws连接成功
        Data.ws.onopen = function () {
          Data.ws.send(JSON.stringify({msg_type:101}))
        }
      // ws连接关闭
      Data.ws.onclose = function () {
        Data.ws.close()
      }
      // ws连接错误
      Data.ws.onerror = function () {
        loading.close()
        ElMessageBox.confirm(
          '授权密钥插件异常，请检查插件是否正确安装！若未安装，是否下载授权插件？',
          "系统提示",
          {
            confirmButtonText: "下载插件",
            cancelButtonText: "取消",
            type: "warning",
            dangerouslyUseHTMLString: true,
            draggable: true
          }
        )
          .then(() => {
            let t = new Date().getTime()
            download(VITE_PROXY_DOMAIN_REAL + "../plugin/AyjHardwareMgrService.exe", "AyjHardwareMgrService.exe")
          }).catch(() => {})
        Data.ws.close()
      };
      // ws数据返回处理
      Data.ws.onmessage = function (result) {
        loading.close()

        let data = JSON.parse(result.data)
        if(data.code === 0){
          // message.success(data.msg)

          Data.key_data = data.data
          if(data.data.msg_type == 101){
            resetForm(ruleFormRef.value)
            Data.data_from.serial_number = data.data.key_id
            Data.dialogVisible = true
            leaderKeyInfoApi({'serial_number': Data.data_from.serial_number}).then(res => {
              if(res.data != null) {
                Data.data_from.tip = "此主任密钥已经被注册"
              }
            })

          }

          if(data.data.msg_type == 103){
            Data.key_data.key_init_status = 0
          }

          if(data.data.msg_type == 102){
            addLeaderKey()
          }

        }else {
          message.error(data.msg)
        }
      };
    } catch (e) {
      alert(e.message);
    }
  };

  function WriteDataToLeaderKey() {
    let timestamp = Date.parse(Date()) / 1000
    var base64 = new Base64();
    let user_data = base64.encode(JSON.stringify([
      {user_id:1, username: "1", role_type: 0, role_name: Data.role_options[0], real_name: Data.data_from.manager, password: "c86038fe04bc7097e218b4fd6b767f4b", update_at: timestamp, delete_at: 0},
      {user_id:2, username: "2", role_type: 1, role_name: Data.role_options[1], real_name: Data.data_from.public, password: "c86038fe04bc7097e218b4fd6b767f4b", update_at: timestamp, delete_at: 0}
    ]))
    Data.loading = true
    Data.ws.send(JSON.stringify({msg_type:102, data: {
        user_data: user_data}}))
  }

  //创建主任密钥
  function addLeaderKey() {
    leaderKeyAddApi(Data.data_from).then(ret => {
      Data.loading = false
      if(ret.code === 0){
        message.success("主人密钥创建成功")
        Data.dialogVisible = false
        onSearch()
      }
    })
  }

  const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async(valid, fields) => {
      if (valid) {
        if(Data.data_from.manager == Data.data_from.public){
          message.error("管理员用户名不能和普通用户名相同")
          return
        }
        confirmSubmit()
      }
    })
  }

  function confirmSubmit() {
    ElMessageBox.confirm(
      '确认初始化主任密钥?',
      'Warning',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }
    ).then(() => {
      WriteDataToLeaderKey()
    })
      .catch(() => {})
  }

  async function getRegion() {
    let { data } = await getRegionApi();
    Data.region_tree = data;
  }

  function changeRegionHandel(value) {
    Data.data_from.hospital_id = "";
    Data.hospital_list = []
    getHospitalList(value)
  }

  async function getHospitalList(region_id) {
    let { data } = await hospitalListApi({'region_id': region_id, 'page_size': 1000})
    Data.hospital_list = data.list;
  }

</script>

<template>
  <div class="main">
    <el-form ref="formRef" :inline="true" :model="Data.search_from" class="bg-white w-99/100 pl-8 pt-4">
      <el-form-item label="硬件ID：" prop="serial_number">
        <el-input style="width: 200px;" v-model="Data.search_from.serial_number" placeholder="请输入硬件ID" clearable />
      </el-form-item>
      <el-form-item label="密钥代码：" prop="code">
        <el-input style="width: 200px;" v-model="Data.search_from.code" placeholder="请输入U-Key代码" clearable />
      </el-form-item>
      <el-form-item label="状态：" prop="status">
        <el-select v-model="Data.search_from.status" placeholder="请选择状态" clearable>
          <el-option label="已开启" value="1" />
          <el-option label="已关闭" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('search')"
          :loading="Data.loading"
          @click="onSearch">搜索</el-button>
        <el-button :icon="useRenderIcon('refresh')" @click="resetForm(formRef)">重置</el-button>
      </el-form-item>
    </el-form>

    <EpTableProBar
      title="主任密钥代码列表"
      :loading="Data.loading"
      :columnList="[
        {label: '编号', show: true},
        {label: '硬件ID', show: true},
        {label: '密钥代码', show: true},
        {label: '医院', show: true},
        {label: '授权设备', show: true},
        {label: '状态', show: true},
        {label: '创建时间', show: true},
        {label: '创建者', show: true},
        ]"
      :dataList="Data.data_list"
      @refresh="onSearch">
      <template #buttons>
        <el-button v-if="Auth('leader-key/add')" type="primary" :icon="useRenderIcon('add')" @click="handleUpdate(null)">添加主任密钥</el-button>
      </template>
      <template v-slot="{ size, checkList }">
        <el-table
          border
          table-layout="auto"
          :size="size"
          :data="Data.data_list"
          :header-cell-style="{ background: '#fafafa', color: '#606266' }"
          @selection-change="handleSelectionChange">
          <el-table-column v-if="checkList.includes('编号')" label="编号" align="center" prop="id" />
          <el-table-column v-if="checkList.includes('硬件ID')" label="硬件ID" align="center" prop="serial_number" />
          <el-table-column v-if="checkList.includes('密钥代码')" label="密钥代码" align="center" width="100" prop="code"></el-table-column>
          <el-table-column v-if="checkList.includes('医院')" label="医院" align="center" prop="hospital_name"></el-table-column>
          <el-table-column v-if="checkList.includes('授权设备')" label="授权设备" align="center" prop="server_author_number"/>
          <el-table-column v-if="checkList.includes('状态')" label="状态" align="center" width="130" prop="status">
            <template #default="scope">
              <Switch
                :disabled="!Auth('leader-key/set-status')"
                :size="size === 'small' ? 'small' : 'default'"
                :loading="switchLoadMap[scope.$index]?.loading"
                v-model:checked="scope.row.status"
                :checkedValue="1"
                :unCheckedValue="0"
                checked-children="已开启"
                un-checked-children="已关闭"
                @change="checked => onChange(checked, scope)"
              />
            </template>
          </el-table-column>
          <el-table-column v-if="checkList.includes('创建时间')" label="创建时间" align="center" width="180" prop="create_at"/>
          <el-table-column v-if="checkList.includes('创建者')" label="创建者" align="center" width="180" prop="create_name"/>
          <el-table-column fixed="right" label="操作" width="180" align="center">
            <template #default="scope">
              <el-popconfirm v-if="Auth('leader-key/delete')" title="是否确认删除?" @confirm="handleDelete(scope.row)">
                <template #reference>
                  <el-button
                    class="reset-margin"
                    type="text"
                    :size="size"
                    :icon="useRenderIcon('delete')">
                    删除
                  </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          class="flex justify-end mt-4"
          :small="size === 'small' ? true : false"
          v-model:page-size="Data.search_from.page_size"
          :page-sizes="[10, 20, 30, 50]"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="Data.search_from.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"/>
      </template>
    </EpTableProBar>


    <el-dialog v-loading="Data.dialogVisible" v-model="Data.dialogVisible" title="添加主任密钥" width="420px" draggable @close="resetForm(ruleFormRef)">
      <el-form
        v-loading="Data.loading"
        ref="ruleFormRef"
        :model="Data.data_from"
        :rules="Data.rules"
        label-position="left"
        label-width="120px">
        <p style="color: red;margin-bottom: 20px" v-if="Data.data_from.tip">注意：{{Data.data_from.tip}}</p>
        <el-form-item label="主任密钥ID:" prop="serial_number">
          <span style="margin-right: 15px;">{{Data.data_from.serial_number}}</span>
          <el-button :disabled="Data.data_from.tip != ''" v-if="Data.key_data.key_init_status !== 0" type="danger" @click="resetUkeyHandel">重置主任密钥</el-button>
        </el-form-item>
        <el-form-item label="主任密钥代码:" prop="code">
          <el-input style="width: 214px;margin-right: 10px;" v-model="Data.data_from.code" show-word-limit maxlength="5" />
        </el-form-item>
        <el-form-item label="选择区域:" prop="region_id">
          <el-cascader :options="Data.region_tree"  v-model="Data.data_from.region_id" @change="changeRegionHandel"/>
        </el-form-item>
        <el-form-item label="绑定单位:" prop="hospital_id">
          <el-select v-model="Data.data_from.hospital_id" placeholder="请选择授权单位" clearable>
            <el-option v-for="(item,index) in Data.hospital_list" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="管理员名称:" prop="manager">
          <el-input style="width: 214px;margin-right: 10px;" v-model="Data.data_from.manager"/>
        </el-form-item>
        <el-form-item label="普通用户名称:" prop="public" disabled="">
          <el-input style="width: 214px;margin-right: 10px;" v-model="Data.data_from.public"/>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="Data.dialogVisible = false">取消</el-button>
          <el-button :disabled="Data.data_from.tip != '' || Data.key_data.key_init_status !== 0" type="primary" @click="submitForm(ruleFormRef)">确认</el-button>
        </span>
      </template>
    </el-dialog>

  </div>
</template>
