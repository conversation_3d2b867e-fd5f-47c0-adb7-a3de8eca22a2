<script setup lang="ts">
  import dayjs from "dayjs";
  import {FormInstance, ElMessageBox, ElTree} from "element-plus";
  import {reactive, ref, onMounted} from "vue";
  import {EpTableProBar} from "/@/components/ReTable";
  import {Switch, message} from "@pureadmin/components";
  import {useRenderIcon} from "/@/components/ReIcon/src/hooks";
  import {
    roleListApi,
    roleAddApi,
    roleEditApi,
    roleDeleteApi,
    roleSetStatusApi,
    roleDepartmentApi,
    setDoctorSign,

  } from "/@/api/sys"
  import ElTreeLine from "/@/components/ReTreeLine";
  import {Auth, AuthCount} from "/@/utils/auth";

  const AuthMenuOptions = [
    {"label": "首页", "api": "welcome"},
    {
      "label": "授权管理",
      "api": "auth",
      "children": [
        {
          "label": "订单管理",
          "api": "order",
          "children": [
            {"label": "个人订单列表", "api": "order/personal"},
            {"label": "全部订单列表", "api": "order/list"},
            {"label": "显示订单金额", "api": "order/view-amount"},
            {"label": "修改订单金额", "api": "order/edit-amount"},
            {"label": "创建订单", "api": "order/create"},
            {"label": "订单详情", "api": "order/details"},
            {"label": "订单升级", "api": "order/upgrade"},
            {"label": "财务复核", "api": "order/review"},
            {"label": "总经理审批", "api": "order/manager_review"},
            {"label": "实施部署", "api": "order/deploy"},
            {"label": "订单转移", "api": "order/transfer"},
            {"label": "订单撤销", "api": "order/revoke"},
            {"label": "派遣医生", "api": "order/assign"},
            {"label": "医生反馈", "api": "order/feedback"},
            {"label": "删除订单", "api": "order/delete"},
            {"label": "查看设备详情", "api": "order/device_details"},
            {"label": "修改订单设备信息", "api": "order/update-device"}
          ]
        },
        {
          "label": "授权记录",
          "api": "licence",
          "children": [
            {"label": "授权列表", "api": "licence/list"},
            {"label": "更新状态", "api": "licence/set-status"},
            {"label": "删除授权", "api": "licence/delete"}
          ]
        }
      ]
    },
    {
      "label": "知识图谱",
      "api": "kl",
      "children": [
        {
          "label": "综合征",
          "api": "kl-syndrome/tree",
          "children": [
            {"label": "综合征列表", "api": "kl-syndrome/tree"},
            {"label": "详情", "api": "kl-syndrome/details"},
            {"label": "添加", "api": "kl-syndrome/add"},
            {"label": "编辑", "api": "kl-syndrome/edit"},
            {"label": "设置状态", "api": "kl-syndrome/set-status"},
            {"label": "删除", "api": "kl-syndrome/delete"},
            {"label": "排序", "api": "kl-syndrome/set-sort"},
          ]
        },
        {
          "label": "特征",
          "api": "kl-feature/tree",
          "children": [
            {"label": "特征列表", "api": "kl-feature/tree"},
            {"label": "详情", "api": "kl-feature/details"},
            {"label": "添加", "api": "kl-feature/add"},
            {"label": "编辑", "api": "kl-feature/edit"},
            {"label": "设置状态", "api": "kl-feature/set-status"},
            {"label": "删除", "api": "kl-feature/delete"},
            {"label": "排序", "api": "kl-feature/set-sort"},
          ]
        },
        {
          "label": "版本管理",
          "api": "kl-version/list",
          "children": [
            {"label": "版本列表", "api": "kl-version/list"},
            {"label": "创建版本", "api": "kl-version/add"},
            {"label": "下载数据包", "api": "kl-version/download"},
            {"label": "删除版本", "api": "kl-version/delete"}
          ]
        }
      ]
    },
    {
      "label": "硬件初始化",
      "api": "init",
      "children": [
        {
          "label": "U-KEY管理",
          "api": "ukey/list",
          "children": [
            {"label": "U-Key列表", "api": "ukey/list"},
            {"label": "添加U-Key", "api": "ukey/add"},
            {"label": "更新U-Key状态", "api": "ukey/set-status"},
            {"label": "删除/修改U-Key", "api": "ukey/delete"}
          ]
        },
        {
          "label": "主任密钥管理",
          "api": "leader-key/list",
          "children": [
            {"label": "主任密钥列表", "api": "leader-key/list"},
            {"label": "添加主任密钥", "api": "leader-key/add"},
            {"label": "更新主任密钥状态", "api": "leader-key/set-status"},
            {"label": "删除主任密钥", "api": "leader-key/delete"}
          ]
        }
      ]
    },
    {
      "label": "设备管理",
      "api": "device",
      "children": [
        {
          "label": "服务器", "api": "server/list", "children": [
            {"label": "设置状态", "api": "server/set-status"},
          ]
        },
        {
          "label": "客户端", "api": "client/list", "children": [
            {"label": "设置状态", "api": "client/set-status"},
          ]
        },
        {
          "label": "版本管理",
          "api": "version/list",
          "children": [
            {"label": "版本列表", "api": "version/list"},
            {"label": "添加", "api": "version/add"},
            {"label": "编辑", "api": "version/edit"},
            {"label": "设置状态", "api": "version/set-status"},
            {"label": "删除", "api": "version/delete"},
            {"label": "详情", "api": "version/details"},
            {"label": "设备列表", "api": "version/device-list"},
          ]
        }
      ]
    },
    {
      "label": "系统管理",
      "api": "sys",
      "children": [
        {
          "label": "用户管理",
          "api": "sys-admin",
          "children": [
            {"label": "全部列表", "api": "sys-admin/list"},
            {"label": "部门列表", "api": "sys-admin/department"},
            {"label": "添加", "api": "sys-admin/add"},
            {"label": "编辑", "api": "sys-admin/edit"},
            {"label": "设置状态", "api": "sys-admin/set-status"},
            {"label": "删除", "api": "sys-admin/delete"},
            {"label": "销售设置", "api": "sys-admin/sales-bind"},
            {"label": "短信登录设置", "api": "sys-admin/set-login-status"}
          ]
        },
        {
          "label": "角色管理",
          "api": "sys-role",
          "children": [
            {"label": "全部列表", "api": "sys-role/list"},
            {"label": "部门列表", "api": "sys-role/department"},
            {"label": "添加", "api": "sys-role/add"},
            {"label": "编辑", "api": "sys-role/edit"},
            {"label": "组权限管理", "api": "sys-role/group"},
            {"label": "设置状态", "api": "sys-role/set-status"},
            {"label": "删除", "api": "sys-role/delete"}
          ]
        },
        {
          "label": "医院管理",
          "api": "hospital",
          "children": [
            {"label": "全部列表", "api": "hospital/list"},
            {"label": "个人列表", "api": "hospital/personal"},
            {"label": "添加", "api": "hospital/add"},
            {"label": "编辑", "api": "hospital/edit"},
            {"label": "设置状态", "api": "hospital/set-status"},
            {"label": "删除", "api": "hospital/delete"}
          ]
        },
        {
          "label": "代理商管理",
          "api": "agent",
          "children": [
            {"label": "全部列表", "api": "agent/list"},
            {"label": "个人列表", "api": "agent/personal"},
            {"label": "添加", "api": "agent/add"},
            {"label": "编辑", "api": "agent/edit"},
            {"label": "设置状态", "api": "agent/set-status"},
            {"label": "自动绑定", "api": "agent/auto-bind"},
            {"label": "删除", "api": "agent/delete"}
          ]
        },
        {
          "label": "系统日志",
          "api": "syslog",
        }
      ]
    }
  ]
  const DefaultRole = [
    {
      value: 'A', label: '销售部', role: [
        {
          "label": "授权管理",
          "api": "auth",
          "children": [
            {
              "label": "订单管理",
              "api": "order",
              "children": [
                {"label": "个人订单列表", "api": "order/personal"},
                {"label": "全部订单列表", "api": "order/list"},
                {"label": "显示订单金额", "api": "order/view-amount"},
                {"label": "修改订单金额", "api": "order/edit-amount"},
                {"label": "创建订单", "api": "order/create"},
                {"label": "订单详情", "api": "order/details"},
                {"label": "订单升级", "api": "order/upgrade"},
                {"label": "订单转移", "api": "order/transfer"},
                {"label": "查看设备详情", "api": "order/device_details"},
                {"label": "修改订单设备信息", "api": "order/update-device"}
              ]
            }
          ]
        },
        {
          "label": "系统管理",
          "api": "sys",
          "children": [
            {
              "label": "用户管理",
              "api": "sys-admin",
              "children": [
                {"label": "部门列表", "api": "sys-admin/department"},
                {"label": "添加", "api": "sys-admin/add"},
                {"label": "编辑", "api": "sys-admin/edit"},
                {"label": "设置状态", "api": "sys-admin/set-status"},
                {"label": "删除", "api": "sys-admin/delete"},
                {"label": "销售设置", "api": "sys-admin/sales-bind"},
              ]
            },
            {
              "label": "角色管理",
              "api": "sys-role",
              "children": [
                {"label": "部门列表", "api": "sys-role/department"},
                {"label": "添加", "api": "sys-role/add"},
                {"label": "编辑", "api": "sys-role/edit"},
                {"label": "设置状态", "api": "sys-role/set-status"},
                {"label": "删除", "api": "sys-role/delete"}
              ]
            },
            {
              "label": "医院管理",
              "api": "hospital",
              "children": [
                {"label": "全部列表", "api": "hospital/list"},
                {"label": "个人列表", "api": "hospital/personal"},
                {"label": "添加", "api": "hospital/add"},
                {"label": "编辑", "api": "hospital/edit"},
                {"label": "设置状态", "api": "hospital/set-status"},
                {"label": "删除", "api": "hospital/delete"}
              ]
            },
            {
              "label": "代理商管理",
              "api": "agent",
              "children": [
                {"label": "全部列表", "api": "agent/list"},
                {"label": "个人列表", "api": "agent/personal"},
                {"label": "添加", "api": "agent/add"},
                {"label": "编辑", "api": "agent/edit"},
                {"label": "设置状态", "api": "agent/set-status"},
                {"label": "删除", "api": "agent/delete"}
              ]
            }
          ]
        }
      ]
    },
    {
      value: 'B', label: '销售', role: [
        {
          "label": "授权管理",
          "api": "auth",
          "children": [
            {
              "label": "订单管理",
              "api": "order",
              "children": [
                {"label": "个人订单列表", "api": "order/personal"},
                {"label": "显示订单金额", "api": "order/view-amount"},
                {"label": "修改订单金额", "api": "order/edit-amount"},
                {"label": "创建订单", "api": "order/create"},
                {"label": "订单详情", "api": "order/details"},
                {"label": "订单升级", "api": "order/upgrade"},
                {"label": "查看设备详情", "api": "order/device_details"},
                {"label": "修改订单设备信息", "api": "order/update-device"}
              ]
            },
            {
              "label": "授权记录",
              "api": "licence/list",
              "children": [
                {"label": "授权列表", "api": "licence/list"},
                {"label": "更新状态", "api": "licence/set-status"},
                {"label": "删除授权", "api": "licence/delete"}
              ]
            }
          ]
        },
        {
          "label": "系统管理",
          "api": "sys",
          "children": [
            {
              "label": "医院管理",
              "api": "hospital",
              "children": [
                {"label": "全部列表", "api": "hospital/list"},
                {"label": "个人列表", "api": "hospital/personal"},
              ]
            },
            {
              "label": "代理商管理",
              "api": "agent",
              "children": [
                {"label": "个人列表", "api": "agent/personal"},
              ]
            }
          ]
        }
      ]
    },
    {
      value: 'C', label: '财务', role: [
        {
          "label": "授权管理",
          "api": "auth",
          "children": [
            {
              "label": "订单管理",
              "api": "order",
              "children": [
                {"label": "全部订单列表", "api": "order/list"},
                {"label": "显示订单金额", "api": "order/view-amount"},
                {"label": "订单详情", "api": "order/details"},
                {"label": "财务复核", "api": "order/review"},
                {"label": "查看设备详情", "api": "order/device_details"}
              ]
            }
          ]
        },
        {
          "label": "系统管理",
          "api": "sys",
          "children": [
            {
              "label": "用户管理",
              "api": "sys-admin",
              "children": [
                {"label": "全部列表", "api": "sys-admin/list"}
              ]
            },
            {
              "label": "医院管理",
              "api": "hospital",
              "children": [
                {"label": "全部列表", "api": "hospital/list"},
                {"label": "个人列表", "api": "hospital/personal"},
                {"label": "添加", "api": "hospital/add"},
                {"label": "编辑", "api": "hospital/edit"},
                {"label": "设置状态", "api": "hospital/set-status"},
                {"label": "删除", "api": "hospital/delete"}
              ]
            },
            {
              "label": "代理商管理",
              "api": "agent",
              "children": [
                {"label": "全部列表", "api": "agent/list"},
                {"label": "全部列表", "api": "agent/personal"},
                {"label": "添加", "api": "agent/add"},
                {"label": "编辑", "api": "agent/edit"},
                {"label": "设置状态", "api": "agent/set-status"},
                {"label": "删除", "api": "agent/delete"}
              ]
            }
          ]
        }
      ]
    },
    {
      value: 'D', label: '实施部署', role: [
        {
          "label": "授权管理",
          "api": "auth",
          "children": [
            {
              "label": "订单管理",
              "api": "order",
              "children": [
                {"label": "个人订单列表", "api": "order/personal"},
                {"label": "全部订单列表", "api": "order/list"},
                {"label": "实施部署", "api": "order/deploy"},
                {"label": "查看设备详情", "api": "order/device_details"}
              ]
            },
            {
              "label": "授权记录",
              "api": "licence/list",
              "children": [
                {"label": "授权列表", "api": "licence/list"},
                {"label": "更新状态", "api": "licence/set-status"}
              ]
            }
          ]
        },
        {
          "label": "知识图谱",
          "api": "kl",
          "children": [
            {
              "label": "综合征",
              "api": "kl-syndrome/tree",
              "children": [
                {"label": "综合征列表", "api": "kl-syndrome/tree"},
                {"label": "详情", "api": "kl-syndrome/details"},
                {"label": "添加", "api": "kl-syndrome/add"},
                {"label": "编辑", "api": "kl-syndrome/edit"},
                {"label": "设置状态", "api": "kl-syndrome/set-status"},
                {"label": "排序", "api": "kl-syndrome/set-sort"}
              ]
            },
            {
              "label": "特征",
              "api": "kl-feature/tree",
              "children": [
                {"label": "特征列表", "api": "kl-feature/tree"},
                {"label": "详情", "api": "kl-feature/details"},
                {"label": "添加", "api": "kl-feature/add"},
                {"label": "编辑", "api": "kl-feature/edit"},
                {"label": "设置状态", "api": "kl-feature/set-status"},
                {"label": "排序", "api": "kl-feature/set-sort"}
              ]
            },
            {
              "label": "版本管理",
              "api": "kl-version/list",
              "children": [
                {"label": "版本列表", "api": "kl-version/list"},
                {"label": "创建版本", "api": "kl-version/add"},
                {"label": "下载数据包", "api": "kl-version/download"},
                {"label": "删除版本", "api": "kl-version/delete"}
              ]
            }
          ]
        },
        {
          "label": "硬件初始化",
          "api": "init",
          "children": [
            {
              "label": "U-KEY管理",
              "api": "ukey/list",
              "children": [
                {"label": "U-Key列表", "api": "ukey/list"},
                {"label": "添加U-Key", "api": "ukey/add"},
                {"label": "更新U-Key状态", "api": "ukey/set-status"},
                {"label": "删除/修改U-Key", "api": "ukey/delete"}
              ]
            },
            {
              "label": "主任密钥管理",
              "api": "leader-key/list",
              "children": [
                {"label": "主任密钥列表", "api": "leader-key/list"},
                {"label": "添加主任密钥", "api": "leader-key/add"},
                {"label": "更新主任密钥状态", "api": "leader-key/set-status"},
                {"label": "删除主任密钥", "api": "leader-key/delete"}
              ]
            }
          ]
        },
        {
          "label": "设备管理",
          "api": "device",
          "children": [
            {
              "label": "服务器", "api": "server/list", "children": [
                {"label": "设置状态", "api": "server/set-status"},
              ]
            },
            {
              "label": "客户端", "api": "client/list", "children": [
                {"label": "设置状态", "api": "client/set-status"},
              ]
            },
            {
              "label": "版本管理",
              "api": "version/list",
              "children": [
                {"label": "版本列表", "api": "version/list"},
                {"label": "添加", "api": "version/add"},
                {"label": "编辑", "api": "version/edit"},
                {"label": "设置状态", "api": "version/set-status"},
                {"label": "删除", "api": "version/delete"},
                {"label": "详情", "api": "version/details"},
                {"label": "设备列表", "api": "version/device-list"},
              ]
            }
          ]
        },
        {
          "label": "系统管理",
          "api": "sys",
          "children": [
            {
              "label": "用户管理",
              "api": "sys-admin",
              "children": [
                {"label": "部门列表", "api": "sys-admin/department"},
                {"label": "添加", "api": "sys-admin/add"},
                {"label": "编辑", "api": "sys-admin/edit"},
                {"label": "设置状态", "api": "sys-admin/set-status"},
                {"label": "删除", "api": "sys-admin/delete"}
              ]
            },
            {
              "label": "角色管理",
              "api": "sys-role",
              "children": [
                {"label": "部门列表", "api": "sys-role/department"},
                {"label": "添加", "api": "sys-role/add"},
                {"label": "编辑", "api": "sys-role/edit"},
                {"label": "设置状态", "api": "sys-role/set-status"},
                {"label": "删除", "api": "sys-role/delete"}
              ]
            },
            {
              "label": "医院管理",
              "api": "hospital",
              "children": [
                {"label": "全部列表", "api": "hospital/list"},
                {"label": "个人列表", "api": "hospital/personal"},
                {"label": "添加", "api": "hospital/add"},
                {"label": "编辑", "api": "hospital/edit"},
                {"label": "设置状态", "api": "hospital/set-status"},
              ]
            }
          ]
        }
      ]
    },
    {
      value: 'E', label: '总经理', role: [
        {"label": "首页", "api": "welcome"},
        {
          "label": "授权管理",
          "api": "auth",
          "children": [
            {
              "label": "订单管理",
              "api": "order",
              "children": [
                {"label": "个人订单列表", "api": "order/personal"},
                {"label": "全部订单列表", "api": "order/list"},
                {"label": "显示订单金额", "api": "order/view-amount"},
                {"label": "修改订单金额", "api": "order/edit-amount"},
                {"label": "创建订单", "api": "order/create"},
                {"label": "订单详情", "api": "order/details"},
                {"label": "订单升级", "api": "order/upgrade"},
                {"label": "财务复核", "api": "order/review"},
                {"label": "总经理审批", "api": "order/manager_review"},
                {"label": "实施部署", "api": "order/deploy"},
                {"label": "订单转移", "api": "order/transfer"},
                {"label": "订单撤销", "api": "order/revoke"},
                {"label": "删除订单", "api": "order/delete"},
                {"label": "查看设备详情", "api": "order/device_details"},
                {"label": "修改订单设备信息", "api": "order/update-device"}
              ]
            },
            {
              "label": "授权记录",
              "api": "licence/list",
              "children": [
                {"label": "授权列表", "api": "licence/list"},
                {"label": "更新状态", "api": "licence/set-status"},
                {"label": "删除授权", "api": "licence/delete"}
              ]
            }
          ]
        },
        {
          "label": "知识图谱",
          "api": "kl",
          "children": [
            {
              "label": "综合征",
              "api": "kl-syndrome/tree",
              "children": [
                {"label": "综合征列表", "api": "kl-syndrome/tree"},
                {"label": "详情", "api": "kl-syndrome/details"},
                {"label": "添加", "api": "kl-syndrome/add"},
                {"label": "编辑", "api": "kl-syndrome/edit"},
                {"label": "设置状态", "api": "kl-syndrome/set-status"},
                {"label": "删除", "api": "kl-syndrome/delete"},
                {"label": "排序", "api": "kl-syndrome/set-sort"},
              ]
            },
            {
              "label": "特征",
              "api": "kl-feature/tree",
              "children": [
                {"label": "特征列表", "api": "kl-feature/tree"},
                {"label": "详情", "api": "kl-feature/details"},
                {"label": "添加", "api": "kl-feature/add"},
                {"label": "编辑", "api": "kl-feature/edit"},
                {"label": "设置状态", "api": "kl-feature/set-status"},
                {"label": "删除", "api": "kl-feature/delete"},
                {"label": "排序", "api": "kl-feature/set-sort"},
              ]
            },
            {
              "label": "版本管理",
              "api": "kl-version/list",
              "children": [
                {"label": "版本列表", "api": "kl-version/list"},
                {"label": "创建版本", "api": "kl-version/add"},
                {"label": "下载数据包", "api": "kl-version/download"},
                {"label": "删除版本", "api": "kl-version/delete"}
              ]
            }
          ]
        },
        {
          "label": "硬件初始化",
          "api": "init",
          "children": [
            {
              "label": "U-KEY管理",
              "api": "ukey/list",
              "children": [
                {"label": "U-Key列表", "api": "ukey/list"},
                {"label": "添加U-Key", "api": "ukey/add"},
                {"label": "更新U-Key状态", "api": "ukey/set-status"},
                {"label": "删除/修改U-Key", "api": "ukey/delete"}
              ]
            },
            {
              "label": "主任密钥管理",
              "api": "leader-key/list",
              "children": [
                {"label": "主任密钥列表", "api": "leader-key/list"},
                {"label": "添加主任密钥", "api": "leader-key/add"},
                {"label": "更新主任密钥状态", "api": "leader-key/set-status"},
                {"label": "删除主任密钥", "api": "leader-key/delete"}
              ]
            }
          ]
        },
        {
          "label": "设备管理",
          "api": "device",
          "children": [
            {
              "label": "服务器", "api": "server/list", "children": [
                {"label": "设置状态", "api": "server/set-status"},
              ]
            },
            {
              "label": "客户端", "api": "client/list", "children": [
                {"label": "设置状态", "api": "client/set-status"},
              ]
            },
            {
              "label": "版本管理",
              "api": "version/list",
              "children": [
                {"label": "版本列表", "api": "version/list"},
                {"label": "添加", "api": "version/add"},
                {"label": "编辑", "api": "version/edit"},
                {"label": "设置状态", "api": "version/set-status"},
                {"label": "删除", "api": "version/delete"},
                {"label": "详情", "api": "version/details"},
                {"label": "设备列表", "api": "version/device-list"},
              ]
            }
          ]
        },
        {
          "label": "系统管理",
          "api": "sys",
          "children": [
            {
              "label": "用户管理",
              "api": "sys-admin",
              "children": [
                {"label": "全部列表", "api": "sys-admin/list"},
                {"label": "部门列表", "api": "sys-admin/department"},
                {"label": "添加", "api": "sys-admin/add"},
                {"label": "编辑", "api": "sys-admin/edit"},
                {"label": "设置状态", "api": "sys-admin/set-status"},
                {"label": "删除", "api": "sys-admin/delete"},
                {"label": "销售设置", "api": "sys-admin/sales-bind"},
                {"label": "短信登录设置", "api": "sys-admin/set-login-status"}
              ]
            },
            {
              "label": "角色管理",
              "api": "sys-role",
              "children": [
                {"label": "全部列表", "api": "sys-role/list"},
                {"label": "部门列表", "api": "sys-role/department"},
                {"label": "添加", "api": "sys-role/add"},
                {"label": "编辑", "api": "sys-role/edit"},
                {"label": "组权限管理", "api": "sys-role/group"},
                {"label": "设置状态", "api": "sys-role/set-status"},
                {"label": "删除", "api": "sys-role/delete"}
              ]
            },
            {
              "label": "医院管理",
              "api": "hospital",
              "children": [
                {"label": "全部列表", "api": "hospital/list"},
                {"label": "个人列表", "api": "hospital/personal"},
                {"label": "添加", "api": "hospital/add"},
                {"label": "编辑", "api": "hospital/edit"},
                {"label": "设置状态", "api": "hospital/set-status"},
                {"label": "删除", "api": "hospital/delete"}
              ]
            },
            {
              "label": "代理商管理",
              "api": "agent",
              "children": [
                {"label": "全部列表", "api": "agent/list"},
                {"label": "个人列表", "api": "agent/personal"},
                {"label": "添加", "api": "agent/add"},
                {"label": "编辑", "api": "agent/edit"},
                {"label": "设置状态", "api": "agent/set-status"},
                {"label": "自动绑定", "api": "agent/auto-bind"},
                {"label": "删除", "api": "agent/delete"}
              ]
            },
            {
              "label": "系统日志",
              "api": "syslog",
            }
          ]
        }
      ]
    },
    {
      value: 'F', label: '医生组', role: [
        {
          "label": "授权管理",
          "api": "auth",
          "children": [
            {
              "label": "订单管理",
              "api": "order",
              "children": [
                {"label": "个人订单列表", "api": "order/personal"},
                {"label": "查看设备详情", "api": "order/device_details"},
                {"label": "医生反馈", "api": "order/feedback"},
              ]
            }
          ]
        },

      ]
    },
  ]

  const formRef = ref<FormInstance>();
  const ruleFormRef = ref<FormInstance>();
  const ruleGroupFormRef = ref<FormInstance>();
  const treeRef = ref<InstanceType<typeof ElTree>>();
  const treeGroupRef = ref<InstanceType<typeof ElTree>>();
  let switchLoadMap = ref({});
  const Data = reactive({
    dialogVisible: false,
    dialogVisibleGroup: false,
    search_from: {
      keyword: "",
      role_id: "",
      status: "",
      page_size: 10,
      page: 1,
      total: 0,
    },
    loading: true,
    data_list: [],
    department_options: [],
    data_from: {
      id: 0,
      up_id: 0,
      up_info: null,
      name: "",
      default_group: "",
      groups: [],
      rule: []
    }
  })

  function getRole(value) {
    var menu = []
    DefaultRole?.forEach(item => {
      if (item.value == value) {
        menu = item.role
        return
      }
    })
    if (menu.length == 0) {
      menu = AuthMenuOptions
    }
    return menu
  }

  function defaultCheckedRole() {
    var menu = []
    DefaultRole?.forEach(item => {
      if (item.value == Data.data_from.default_group) {
        menu = item.role
        return
      }
    })
    if (menu.length > 0) {
      menu.forEach(item => {
        Data.data_from.rule.push(item.api)
      })
    } else {
      treeGroupRef.value?.setCheckedKeys([])
    }
  }

  async function handleDelete(row) {
    let {code} = await roleDeleteApi({ids: [row.id]});
    if (code === 0) {
      message.success("删除成功")
      onSearch()
    }
  }

  function handleCurrentChange(val: number) {
    Data.search_from.page = val
    onSearch()
  }

  function handleSizeChange(val: number) {
    Data.search_from.page_size = val
    onSearch()
  }

  function handleSelectionChange(val) {
    console.log("handleSelectionChange", val);
  }

  function onChange(checked, {$index, row}) {
    ElMessageBox.confirm(
      `确认要<strong>${
        row.status === 0 ? "停用" : "启用"
      }</strong><strong style='color:var(--el-color-primary)'>${
        row.name
      }</strong>角色吗?`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    )
      .then(() => {
        switchLoadMap.value[$index] = Object.assign({}, switchLoadMap.value[$index], {loading: true});
        roleSetStatusApi({id: row.id, status: row.status}).then(ret => {
          switchLoadMap.value[$index] = Object.assign({}, switchLoadMap.value[$index], {loading: false});
          if (ret.code === 0) {
            message.success("修改成功");
          }
          onSearch()
        })

      })
      .catch(() => {
        row.status === 0 ? (row.status = 1) : (row.status = 0);
      });
  }

  async function onSearch() {
    Data.loading = true;
    //根据用户权限，判断查询全部列表，还是部门列表
    let response
    if (Auth('sys-role/list')) {
      response = await roleListApi(Data.search_from);
    } else {
      response = await roleDepartmentApi(Data.search_from);
    }
    let data = response?.data
    // let {data} = await roleListApi(Data.search_from);
    if (data.total <= Data.search_from.page_size && Data.search_from.page > 1) {
      Data.search_from.page = 1
      onSearch()
    }
    data.list?.forEach(item => {
      item['rule'] = JSON.parse(item['rule'])
    })
    let arr = data.list.map((sub) => {
      if (sub.sign == '1') {
        return {
          ...sub,
          sign: true,
        }
      }
      if (sub.sign == '0') {
        return {
          ...sub,
          sign: false,
        }
      }
    });
    Data.data_list = arr;
    Data.search_from.total = data.total;
    Data.loading = false;
  }

  const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
      if (valid) {
        Data.data_from.rule = getCheckedNodes()
        Data.data_from.up_id = Data.data_from.up_info?.id
        if (Data.data_from.id > 0) {
          let {code, msg} = await roleEditApi(Data.data_from);
          if (code === 0) {
            message.success(msg)
            Data.dialogVisible = false
            onSearch()
          }
        } else {
          let {code, msg} = await roleAddApi(Data.data_from);
          if (code === 0) {
            message.success(msg)
            Data.dialogVisible = false
            onSearch()
          }
        }
      }
    })
  }

  const submitGroupForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
      if (valid) {
        Data.data_from.rule = getGroupCheckedNodes()
        if (Data.data_from.id > 0) {
          let {code, msg} = await roleEditApi(Data.data_from);
          if (code === 0) {
            message.success(msg)
            Data.dialogVisibleGroup = false
            onSearch()
          }
        } else {
          let {code, msg} = await roleAddApi(Data.data_from);
          if (code === 0) {
            message.success(msg)
            Data.dialogVisibleGroup = false
            onSearch()
          }
        }
      }
    })
  }

  const cancelForm = (formEl: FormInstance | undefined) => {
    console.log(formEl)
    if (!formEl) return
    Data.dialogVisible = false
    Data.dialogVisibleGroup = false
    formEl.resetFields()
  }

  function handleUpdate(row, up_info) {
    cancelForm(ruleFormRef.value)
    console.log("up_info", row, up_info)
    formatRoleGroup(up_info?.rule)
    Data.dialogVisible = true
    setTimeout(() => {
      treeRef.value!.setCheckedKeys([], false)
      if (row != null) {
        Data.data_from = JSON.parse(JSON.stringify(row))
        Data.data_from.rule = []
        Data.data_from.rule = JSON.parse(row.rule)
        Data.data_from.up_info = up_info
        console.log("edit", Data.data_from)
      } else {
        Data.data_from = {
          id: 0,
          up_id: 0,
          up_info: up_info,
          name: "",
          default_group: "",
          groups: [],
          rule: []
        }
      }
    }, 100)
  }

  //格式化角色分组的权限
  function formatRoleGroup(rule) {
    Data.department_options = []
    if (rule?.length == 0) {
      return
    }
    var i = 0
    var j = 0
    AuthMenuOptions.forEach((item, index) => {
      console.log(item.api, checkRule(item.children, rule), rule.indexOf(item.api) != -1)
      if (item.api == 'welcome' && rule.indexOf(item.api) != -1) { //首页权限
        Data.department_options.push({"label": item.label, "api": item.api, "children": []})
        i++
      } else if (item.api == 'auth' && checkRule(item.children, rule)) { //授权管理
        Data.department_options.push({"label": item.label, "api": item.api, "children": []})
        j = 0
        item.children?.forEach((item1, index1) => {
          if (item1.api == 'order' && checkRule(item1.children, rule)) {//订单管理
            Data.department_options[i]["children"].push({"label": item1.label, "api": item1.api, "children": []})
            item1.children?.forEach((item2, index2) => {
              if (rule.indexOf(item2.api) != -1) {
                Data.department_options[i]["children"][j]["children"].push({"label": item2.label, "api": item2.api})
              }
            })
            j++
          } else if (item1.api == 'licence' && checkRule(item1.children, rule)) { //授权记录
            Data.department_options[i]["children"].push({"label": item1.label, "api": item1.api, "children": []})
            item1.children?.forEach((item2, index2) => {
              if (rule.indexOf(item2.api) != -1) {
                Data.department_options[i]["children"][j]["children"].push({"label": item2.label, "api": item2.api})
              }
            })
            j++
          }
        })
        i++
      } else if (item.api == 'kl' && checkRule(item.children, rule)) { //知识图谱
        Data.department_options.push({"label": item.label, "api": item.api, "children": []})
        j = 0
        item.children?.forEach((item1, index1) => {
          if (item1.api == 'kl-syndrome/tree' && checkRule(item1.children, rule)) {//综合征
            Data.department_options[i]["children"].push({"label": item1.label, "api": item1.api, "children": []})
            item1.children?.forEach((item2, index2) => {
              if (rule.indexOf(item2.api) != -1) {
                Data.department_options[i]["children"][j]["children"].push({"label": item2.label, "api": item2.api})
              }
            })
            j++
          } else if (item1.api == 'kl-feature/tree' && checkRule(item1.children, rule)) { //特征
            Data.department_options[i]["children"].push({"label": item1.label, "api": item1.api, "children": []})
            item1.children?.forEach((item2, index2) => {
              if (rule.indexOf(item2.api) != -1) {
                Data.department_options[i]["children"][j]["children"].push({"label": item2.label, "api": item2.api})
              }
            })
            j++
          } else if (item1.api == 'kl-version/list' && checkRule(item1.children, rule)) { //版本管理
            Data.department_options[i]["children"].push({"label": item1.label, "api": item1.api, "children": []})
            item1.children?.forEach((item2, index2) => {
              if (rule.indexOf(item2.api) != -1) {
                Data.department_options[i]["children"][j]["children"].push({"label": item2.label, "api": item2.api})
              }
            })
            j++
          }
        })
        i++
      } else if (item.api == 'init' && checkRule(item.children, rule)) { //硬件初始化
        Data.department_options.push({"label": item.label, "api": item.api, "children": []})
        j = 0
        item.children?.forEach((item1, index1) => {
          if (item1.api == 'ukey/list' && checkRule(item1.children, rule)) {//U-KEY管理
            Data.department_options[i]["children"].push({"label": item1.label, "api": item1.api, "children": []})
            item1.children?.forEach((item2, index2) => {
              if (rule.indexOf(item2.api) != -1) {
                Data.department_options[i]["children"][j]["children"].push({"label": item2.label, "api": item2.api})
              }
            })
            j++
          } else if (item1.api == 'leader-key/list' && checkRule(item1.children, rule)) { //主任密钥管理
            Data.department_options[i]["children"].push({"label": item1.label, "api": item1.api, "children": []})
            item1.children?.forEach((item2, index2) => {
              if (rule.indexOf(item2.api) != -1) {
                Data.department_options[i]["children"][j]["children"].push({"label": item2.label, "api": item2.api})
              }
            })
            j++
          }
        })
        i++
      } else if (item.api == 'device' && checkRule(item.children, rule)) { //设备管理
        Data.department_options.push({"label": item.label, "api": item.api, "children": []})
        j = 0
        item.children?.forEach((item1, index1) => {
          if (item1.api == 'server/list' && checkRule(item1.children, rule)) {//服务器
            Data.department_options[i]["children"].push({"label": item1.label, "api": item1.api, "children": []})
            item1.children?.forEach((item2, index2) => {
              if (rule.indexOf(item2.api) != -1) {
                Data.department_options[i]["children"][j]["children"].push({"label": item2.label, "api": item2.api})
              }
            })
            j++
          } else if (item1.api == 'client/list' && checkRule(item1.children, rule)) { //客户端
            Data.department_options[i]["children"].push({"label": item1.label, "api": item1.api, "children": []})
            item1.children?.forEach((item2, index2) => {
              if (rule.indexOf(item2.api) != -1) {
                Data.department_options[i]["children"][j]["children"].push({"label": item2.label, "api": item2.api})
              }
            })
            j++
          } else if (item1.api == 'version/list' && checkRule(item1.children, rule)) { //版本管理
            Data.department_options[i]["children"].push({"label": item1.label, "api": item1.api, "children": []})
            item1.children?.forEach((item2, index2) => {
              if (rule.indexOf(item2.api) != -1) {
                Data.department_options[i]["children"][j]["children"].push({"label": item2.label, "api": item2.api})
              }
            })
            j++
          }
        })
        i++
      } else if (item.api == 'sys' && checkRule(item.children, rule)) { //系统管理
        Data.department_options.push({"label": item.label, "api": item.api, "children": []})
        j = 0
        item.children?.forEach((item1, index1) => {
          if (item1.api == 'sys-admin' && checkRule(item1.children, rule)) {//用户管理
            Data.department_options[i]["children"].push({"label": item1.label, "api": item1.api, "children": []})
            item1.children?.forEach((item2, index2) => {
              if (rule.indexOf(item2.api) != -1) {
                Data.department_options[i]["children"][j]["children"].push({"label": item2.label, "api": item2.api})
              }
            })
            j++
          } else if (item1.api == 'sys-role' && checkRule(item1.children, rule)) { //角色管理
            Data.department_options[i]["children"].push({"label": item1.label, "api": item1.api, "children": []})
            item1.children?.forEach((item2, index2) => {
              if (rule.indexOf(item2.api) != -1) {
                Data.department_options[i]["children"][j]["children"].push({"label": item2.label, "api": item2.api})
              }
            })
            j++
          } else if (item1.api == 'hospital' && checkRule(item1.children, rule)) { //医院管理
            Data.department_options[i]["children"].push({"label": item1.label, "api": item1.api, "children": []})
            item1.children?.forEach((item2, index2) => {
              if (rule.indexOf(item2.api) != -1) {
                Data.department_options[i]["children"][j]["children"].push({"label": item2.label, "api": item2.api})
              }
            })
            j++
          } else if (item1.api == 'agent' && checkRule(item1.children, rule)) { //代理商管理
            Data.department_options[i]["children"].push({"label": item1.label, "api": item1.api, "children": []})
            item1.children?.forEach((item2, index2) => {
              if (rule.indexOf(item2.api) != -1) {
                Data.department_options[i]["children"][j]["children"].push({"label": item2.label, "api": item2.api})
              }
            })
            j++
          }
        })
        i++
      }
    })
  }

  //判断是否拥有角色权限
  function checkRule(children, rule) {
    var is_had = false
    children?.forEach(item => {
      rule?.forEach(item1 => {
        if (item.api == item1) {
          is_had = true
          return
        }
      })
      if (item.children != null) {
        item.children?.forEach(item2 => {
          rule?.forEach(item1 => {
            if (item2.api == item1) {
              is_had = true
              return
            }
          })
        })
      }
    })
    return is_had
  }

  function handleUpdateGroup(row) {
    cancelForm(ruleGroupFormRef.value)
    Data.dialogVisibleGroup = true
    setTimeout(() => {
      treeGroupRef.value!.setCheckedKeys([], false)
      if (row != null) {
        Data.data_from = JSON.parse(JSON.stringify(row))
        //格式化角色权限
        console.log("role", row, Data.data_from.rule)
      } else {
        Data.data_from = {
          id: 0,
          up_id: 0,
          up_info: null,
          name: "",
          default_group: "",
          groups: [],
          rule: []
        }
      }
    }, 100)
  }

  const getCheckedNodes = () => {
    let menu = treeRef.value!.getCheckedNodes(false, false)
    let value = []
    menu.forEach(item => {
      value.push(item.api)
    })
    return value
  }

  const getGroupCheckedNodes = () => {
    let menu = treeGroupRef.value!.getCheckedNodes(false, false)
    let value = []
    menu?.forEach(item => {
      value.push(item.api)
    })
    return value
  }

  onMounted(() => {
    onSearch();
  });

  const allowDrop = (draggingNode, dropNode, type) => {
    if (type === "inner") return;
    const draggingLabel = draggingNode.data.label;
    const dropNodeLabel = dropNode.data.label;
    let a = [];
    loop(Data.tree_data, draggingLabel, (it, index, arr) => {
      a = arr;
    });
    if (a.some((it) => it.label === dropNodeLabel)) {
      return true;
    }
    return false;
  };

  function loop(data, key, callback) {
    data?.forEach((item, index, arr) => {
      if (item.label === key) {
        return callback(item, index, arr);
      }
      if (item.children) {
        return loop(item.children, key, callback);
      }
    });
  }

  //设为医生组
  function setDoctors(data) {
    console.log(data.id)
    let tost = ElMessageBox.confirm(
      `确定${data.sign ? '增加为' : '取消'}医生组吗？`,
      {
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        center: true,
        dangerouslyUseHTMLString: true,
        draggable: true,
        customClass: 'hq-confirm'
      }
    ).then(async (event) => {
      data.sign = data.sign;
      await doctorSign(data);
    }).catch(async (event) => {
      data.sign = !data.sign;
    });
  }

  async function doctorSign(data) {
    let result = await setDoctorSign({
      sign: data.sign ? '1' : '0',
      id: data.id
    });
     if(result.code=='0'){
       message.success("设置成功");
     }else{
       message.success(result.code);
     }


  }

</script>

<template>
  <div class="main">
    <el-form ref="formRef" :inline="true" :model="Data.search_from" class="bg-white w-99/100 pl-8 pt-4">
      <!--      <el-form-item label="角色名称：" prop="keyword">-->
      <!--        <el-input style="width: 200px;" v-model="Data.search_from.keyword" placeholder="请输入角色名称" clearable/>-->
      <!--      </el-form-item>-->
      <el-form-item label="状态：" prop="status">
        <el-select v-model="Data.search_from.status" placeholder="请选择状态" clearable>
          <el-option label="已开启" value="1"/>
          <el-option label="已关闭" value="0"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('search')"
          :loading="Data.loading"
          @click="onSearch">搜索
        </el-button>
        <el-button :icon="useRenderIcon('refresh')" @click="resetForm(formRef)">重置</el-button>
      </el-form-item>
    </el-form>

    <EpTableProBar
      title="角色列表"
      :loading="Data.loading"
      :columnList="[
        {label: 'ID', show: true},
        {label: '角色分组', show: true},
        {label: '状态', show: true},
        {label: '创建时间', show: true},
        {label: '创建者', show: true},
        ]"
      :dataList="Data.data_list"
      @refresh="onSearch">
      <template #buttons>
        <el-button v-if="Auth('sys-role/group')" type="primary" :icon="useRenderIcon('add')"
                   @click="handleUpdateGroup(null)">新增角色分组
        </el-button>
      </template>
      <template v-slot="{ size, checkList }">
        <el-table
          border
          table-layout="auto"
          :size="size"
          :data="Data.data_list"
          :header-cell-style="{ background: '#fafafa', color: '#606266' }"
          @selection-change="handleSelectionChange">
          <el-table-column type="expand">
            <template #default="props">
              <div style="margin: 5px 20px;">
                <el-table :data="props.row.groups" border>
                  <el-table-column label="ID" align="center" width="150" prop="id"/>
                  <el-table-column label="角色名称" align="center" prop="name"/>
                  <el-table-column label="状态" align="center" width="150" prop="status">
                    <template #default="scope2">
                      <Switch
                        :disabled="!Auth('sys-role/set-status') || scope2.row.id == 1"
                        :size="size === 'small' ? 'small' : 'default'"
                        :loading="switchLoadMap[scope2.$index]?.loading"
                        v-model:checked="scope2.row.status"
                        :checkedValue="1"
                        :unCheckedValue="0"
                        checked-children="已开启"
                        un-checked-children="已关闭"
                        @change="checked => onChange(checked, scope2)"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="创建时间" align="center" width="170" prop="create_at"/>
                  <el-table-column label="创建者" align="center" prop="create_name"/>
                  <el-table-column label="操作" width="400" align="center">
                    <template #default="scope2">

                      <el-button
                        v-if="Auth('sys-role/edit') && scope2.row.id != 1"
                        class="reset-margin"
                        type="text"
                        :size="size"
                        @click="handleUpdate(scope2.row, props.row)"
                        :icon="useRenderIcon('edits')">
                        权限修改
                      </el-button>
                      <el-popconfirm v-if="Auth('sys-role/delete') && scope2.row.id != 1" title="是否确认删除?"
                                     @confirm="handleDelete(scope2.row)">
                        <template #reference>
                          <el-button
                            class="reset-margin"
                            type="text"
                            :size="size"
                            :icon="useRenderIcon('delete')">
                            删除
                          </el-button>
                        </template>
                      </el-popconfirm>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </template>
          </el-table-column>
          <el-table-column v-if="checkList.includes('ID')" label="ID" align="center" width="150" prop="id"/>
          <el-table-column v-if="checkList.includes('角色分组')" label="角色分组" align="center" prop="name"/>
          <el-table-column v-if="checkList.includes('状态')" label="状态" align="center" width="130" prop="status">
            <template #default="scope">
              <Switch
                :disabled="!Auth('sys-role/set-status') || scope.row.id == 1"
                :size="size === 'small' ? 'small' : 'default'"
                :loading="switchLoadMap[scope.$index]?.loading"
                v-model:checked="scope.row.status"
                :checkedValue="1"
                :unCheckedValue="0"
                checked-children="已开启"
                un-checked-children="已关闭"
                @change="checked => onChange(checked, scope)"
              />
            </template>
          </el-table-column>
          <el-table-column v-if="checkList.includes('创建时间')" label="创建时间" align="center" width="170" prop="create_at"/>
          <el-table-column v-if="checkList.includes('创建者')" label="创建者" align="center" prop="create_name"/>
          <el-table-column fixed="right" label="操作" width="400" align="center">
            <template #default="scope">


              <el-button class="reset-margin"
                         type="text"
                         :size="size"
                         v-if="Auth('sys-role/group') && scope.row.id != 1" @click.stop>
                <el-checkbox v-model="scope.row.sign"
                             @change="setDoctors(scope.row)"
                             class="reset-margin" label="设为医生组" size="large"/>
              </el-button>


              <el-button
                v-if="Auth('sys-role/group') && scope.row.id != 1"
                class="reset-margin"
                type="text"
                :size="size"
                @click="handleUpdateGroup(scope.row)"
                :icon="useRenderIcon('edits')">
                修改组权限
              </el-button>
              <el-button
                v-if="Auth('sys-role/add') && scope.row.id != 1"
                class="reset-margin"
                type="text"
                :size="size"
                @click="handleUpdate(null, scope.row)"
                :icon="useRenderIcon('add')">
                新增角色
              </el-button>
              <el-popconfirm v-if="Auth('sys-role/delete') && scope.row.id != 1" title="是否确认删除?"
                             @confirm="handleDelete(scope.row)">
                <template #reference>
                  <el-button
                    class="reset-margin"
                    type="text"
                    :size="size"
                    :icon="useRenderIcon('delete')">
                    删除
                  </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          class="flex justify-end mt-4"
          :small="size === 'small' ? true : false"
          v-model:page-size="Data.search_from.page_size"
          :page-sizes="[10, 20, 30, 50]"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="Data.search_from.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"/>
      </template>
    </EpTableProBar>

    <el-dialog v-model="Data.dialogVisible"
               :title="Data.data_from.id == 0?'添加角色':'编辑角色'"
               width="600px"
               top="50px"
               :close-on-click-modal="false"
               draggable>
      <el-form
        ref="ruleFormRef"
        :model="Data.data_from"
        :rules="{
              name: [
                {required: true, message: '角色名称必填', trigger: 'blur'},
                {min: 1, max: 20, message: '角色名称限定20字符长度', trigger: ['blur', 'change']},
              ]}"
        label-width="90px">
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="角色分组">
              <el-space>{{ Data.data_from.up_info?.name }}</el-space>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="角色名称" prop="name">
              <el-input :disabled="Data.data_from.id > 0" v-model="Data.data_from.name" show-word-limit maxlength="20"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="权限配置" prop="rule">
              <el-tree style="width: 100%"
                       ref="treeRef"
                       :data="Data.department_options"
                       :props="{children: 'children',label: 'label'}"
                       show-checkbox
                       default-expand-all
                       :default-checked-keys="Data.data_from.rule"
                       node-key="api"
                       :indent="30">
                <template v-slot:default="{ node }">
                  <el-tree-line :node="node" :showLabelLine="true">
                    <template v-slot:node-label>
                      <span class="text-sm">{{node.label}}</span>
                    </template>
                  </el-tree-line>
                </template>
              </el-tree>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
           <el-button @click="cancelForm(ruleFormRef)">取消</el-button>
           <el-button type="primary" @click="submitForm(ruleFormRef)">提交</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="Data.dialogVisibleGroup"
               :title="Data.data_from.id == 0?'添加角色分组':'编辑角色分组'"
               width="600px"
               top="50px"
               :close-on-click-modal="false"
               draggable>
      <el-form
        ref="ruleGroupFormRef"
        :model="Data.data_from"
        :rules="{
              name: [
                {required: true, message: '角色名称必填', trigger: 'blur'},
                {min: 1, max: 20, message: '角色名称限定20字符长度', trigger: ['blur', 'change']},
              ]}"
        label-width="110px">
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="角色分组名称" prop="name">
              <el-input :disabled="Data.data_from.id > 0" v-model="Data.data_from.name" show-word-limit maxlength="20"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="默认角色分组">
              <el-select style="width:200px" v-model="Data.data_from.default_group" placeholder="请选择默认角色分组"
                         @change="defaultCheckedRole" clearable>
                <el-option v-for="(item,index) in DefaultRole" :label="item.label" :value="item.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="分组权限配置" prop="rule">
              <el-tree style="width: 100%"
                       ref="treeGroupRef"
                       :data="getRole(Data.data_from.default_group)"
                       :props="{children: 'children',label: 'label'}"
                       show-checkbox
                       default-expand-all
                       :default-checked-keys="Data.data_from.rule"
                       node-key="api"
                       :indent="30"
              >
                <template v-slot:default="{ node }">
                  <el-tree-line :node="node" :showLabelLine="true">
                    <template v-slot:node-label>
                      <span class="text-sm">{{node.label}}</span>
                    </template>
                  </el-tree-line>
                </template>
              </el-tree>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
           <el-button @click="cancelForm(ruleGroupFormRef)">取消</el-button>
           <el-button type="primary" @click="submitGroupForm(ruleGroupFormRef)">提交</el-button>
        </span>
      </template>
    </el-dialog>

  </div>
</template>
<style scoped lang="scss">
  :deep(.el-dropdown-menu__item i) {
    margin: 0;
  }

</style>
