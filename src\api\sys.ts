import { http } from "../utils/http";

// 获取全部用户列表
export const adminListApi = (params?: object) => {
  return http.request("get", "sys-admin/list", { params });
};

// 获取部门用户列表
export const adminDepartmentApi = (params?: object) => {
  return http.request("get", "sys-admin/department", { params });
};

//添加用户
export const adminAddApi = (data: object) => {
  return http.request("post", "sys-admin/add", { data });
};

//编辑用户
export const adminEditApi = (data) => {
  return http.request("post", "sys-admin/edit", { data });
};

//删除用户
export const adminDeleteApi = (data) => {
  return http.request("post", "sys-admin/delete", { data });
};

//设置用户状态
export const adminSetStatusApi = (params?: object) => {
  return http.request("get", "sys-admin/set-status", { params });
};

// 获取角色列表
export const roleListApi = (params?: object) => {
  return http.request("get", "sys-role/list", { params });
};

// 获取部门角色列表
export const roleDepartmentApi = (params?: object) => {
  return http.request("get", "sys-role/department", { params });
};

//添加角色
export const roleAddApi = (data: object) => {
  return http.request("post", "sys-role/add", { data });
};

//编辑角色
export const roleEditApi = (data) => {
  return http.request("post", "sys-role/edit", { data });
};

//删除角色
export const roleDeleteApi = (data) => {
  return http.request("post", "sys-role/delete", { data });
};

//设置角色状态
export const roleSetStatusApi = (params?: object) => {
  return http.request("get", "sys-role/set-status", { params });
};

export const roleAuthMenuApi = (params?: object) => {
  return http.request("get", "sys-role/auth-menu", { params });
};

//获取部门结构数据
export const departmentTreeApi = (data) => {
  return http.request("post", "sys-admin/get-department-tree", { data });
};

//获取单位列表数据
export const hospitalListApi = (data) => {
  return http.request("post", "hospital/list", { data });
};

//获取用户创建医院列表
export const hospitalPersonalApi = (data) => {
  return http.request("post", "hospital/personal", { data });
};

//添加单位
export const hospitalAddApi = (data: object) => {
  return http.request("post", "hospital/add", { data });
};

//订单添加单位
export const hospitalOrderAddApi = (data: object) => {
  return http.request("post", "hospital/order-add", { data });
};

//编辑单位
export const hospitalEditApi = (data) => {
  return http.request("post", "hospital/edit", { data });
};

//删除单位
export const hospitalDeleteApi = (data) => {
  return http.request("post", "hospital/delete", { data });
};

//设置单位状态
export const hospitalSetStatusApi = (params?: object) => {
  return http.request("get", "hospital/set-status", { params });
};

export const uploadApi = (data) => {
  return http.request("post", "file/upload", { data });
};

//获取全部代理商分页数据
export const agentListApi = (data) => {
  return http.request("post", "agent/list", { data });
};

//获取用户已绑定代理商分页数据
export const agentPersonalApi = (data) => {
  return http.request("post", "agent/personal", { data });
};

//获取代理商全部数据
export const  agentAllApi = (data) => {
  return http.request("post", "agent/all", { data });
};

//添加代理商
export const agentAddApi = (data: object) => {
  return http.request("post", "agent/add", { data });
};

//订单添加代理商
export const agentOrderAddApi = (data: object) => {
  return http.request("post", "agent/order-add", { data });
};

//编辑代理商
export const agentEditApi = (data) => {
  return http.request("post", "agent/edit", { data });
};

//删除代理商
export const agentDeleteApi = (data) => {
  return http.request("post", "agent/delete", { data });
};

//设置代理商状态
export const agentSetStatusApi = (params?: object) => {
  return http.request("get", "agent/set-status", { params });
};

//查询销售已绑定代理商
export const agentHadBindApi = (params?: object) => {
  return http.request("get", "agent/had-bind", { params });
};

//查询已绑定代理商的销售
export const agentHadBindUserApi = (params?: object) => {
  return http.request("get", "agent/had-bind-user", { params });
};

//绑定代理商
export const agentBindApi = (data: object) => {
  return http.request("post", "agent/bind", { data });
};

//取消绑定代理商
export const agentCancelBindApi = (data: object) => {
  return http.request("post", "agent/cancel-bind", { data });
};

//获取用户绑定代理商区域数据
export const getBindRegionApi = () => {
  return http.request("get", "agent/bind-region-tree");
};

//登录模式
export const loginQueryApi = (params?: object) => {
  return http.request("get", "sys-admin/query-login", { params });
};

export const loginSetStatusApi = (params?: object) => {
  return http.request("get", "sys-admin/set-login-status", { params });
};

//获取用户区域树
export const getUserRegionApi = () => {
  return http.request("get", "sys-admin/user-region-tree");
};

//设为医生组
export const setDoctorSign = (data: object) => {
  return http.request("post", "sys-role/sign",{ data });
};


//系统日志
export const getLogslist = (data: object) => {
  return http.request("post", "logs/list",{ data });
};



