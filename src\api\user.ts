import { http } from "../utils/http";

interface userType extends Promise<any> {
  svg?: string;
  code?: number;
  info?: object;
}

// 获取验证码
export const getVerify = (): userType => {
  return http.request("get", "public/captcha");
};

// 登录
export const getLogin = (data: object) => {
  return http.request("post", "public/login", { data });
};

// 刷新token
export const refreshToken = (data: object) => {
  return http.request("post", "public/refreshToken", { data });
};

// export const searchVague = (data: object) => {
//   return http.request("post", "/searchVague", { data });
// };
