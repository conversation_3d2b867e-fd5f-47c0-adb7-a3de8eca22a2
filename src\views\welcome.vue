<template>
  <div v-if="Auth('welcome')">
    <table class="title-box">
      <tr>
        <td width="16%">
          <div class="title-qc">质控中心</div>
        </td>
        <td width="80%">
          <div class="title-ac">客户端</div>
        </td>
      </tr>
    </table>
    <table class="data-box data-box-item" v-if="Data.device_data.length == 5">
      <tr>
        <td width="16%">
          <div :class="{'qc':Data.use_type == 5}" @click="clickDataQC(5)">
            <img src="../assets/dashboard/0.png" alt="">
            质控中心
            <p>{{formatNumber(Data.device_data[4].value) }}</p>
          </div>
        </td>
        <td width="16%">
          <div :class="{'active':Data.use_type == 0}" @click="clickData(0)">
            <img src="../assets/dashboard/0.png" alt="">
            设备总数
            <p>{{formatNumber(Data.device_data[0].value + Data.device_data[1].value + Data.device_data[2].value + Data.device_data[3].value) }}</p>
          </div>
        </td>
        <td width="16%">
          <div :class="{'active':Data.use_type == 4}" @click="clickData(4)">
            <img src="../assets/dashboard/1.png" alt="">
            售卖设备
            <p>{{formatNumber(Data.device_data[0].value)}}</p>
          </div>
        </td>
        <td width="16%">
          <div :class="{'active':Data.use_type == 1}" @click="clickData(1)">
            <img src="../assets/dashboard/2.png" alt="">
            临床试验用
            <p>{{formatNumber(Data.device_data[1].value)}}</p>
          </div>
        </td>
        <td width="16%">
          <div :class="{'active':Data.use_type == 3}" @click="clickData(3)">
            <img src="../assets/dashboard/3.png" alt="">
            代理商样机
            <p>{{formatNumber(Data.device_data[3].value)}}</p>
          </div>
        </td>
        <td width="16%">
          <div :class="{'active':Data.use_type == 2}" @click="clickData(2)">
            <img src="../assets/dashboard/4.png" alt="">
            厂家样机
            <p>{{formatNumber(Data.device_data[2].value)}}</p>
          </div>
        </td>
      </tr>
    </table>

    <el-row >
      <el-col :span="12" style="padding: 15px;">
        <div id="map" style="height: calc(100vh - 270px);background-color: white;border-radius: 6px;"></div>
      </el-col>
      <el-col :span="12" style="padding: 15px;">


        <div id="bar" style="height: calc(100vh - 270px);background-color: white;border-radius: 6px;"></div>



      </el-col>
    </el-row>
  </div>
  <div v-else>
    <img :src="ayj_welcome" alt="" style="height:calc(100vh - 140px);width: 100%">
  </div>
</template>
<script setup lang="ts">
  import { reactive, ref, onMounted, onUnmounted, onBeforeMount, nextTick, watch } from "vue";
  import { orderRegionDataApi, deviceDataApi } from "/@/api/home"
  import * as echarts from 'echarts';
  import 'echarts/map/js/china.js';
  import { shallowRef } from "vue";
  import {Auth} from "/@/utils/auth";
  import ayj_welcome from "/@/assets/ayj_welcome.jpg";

  const Data =  reactive({
    use_type: 0,
    device_data: [],
  });
  const myChart = shallowRef();
  const charts = shallowRef();

  onMounted(() => {
    let load_router  = localStorage.getItem('_load_router')
    if( load_router != '1'){
      localStorage.setItem('_load_router', '1')
      setTimeout(()=>{
        window.location.reload()
      }, 1000)
    }
    if(Auth('welcome')) {
      getOrderRegionData(0)
      getDeviceData()
    }
  });

  function formatNumber(num) {
    let reg = /(?=(\B)(\d{3})+$)/g;
    return num.toString().replace(reg, ',');
  }

  function clickData(use_type){
    Data.use_type = use_type
    getOrderRegionData(use_type)
    drawBar(Data.device_data.slice(0,4))
  }

  function clickDataQC(use_type){
    Data.use_type = use_type
    getOrderRegionData(use_type)
    drawBar(Data.device_data.slice(4,5))
  }

  function getOrderRegionData(use_type){
    orderRegionDataApi({use_type:use_type}).then(ret => {
      drawMap(ret.data)
    })
  }

  function getDeviceData(){
    deviceDataApi().then(ret => {
      Data.device_data = ret.data
      drawBar(Data.device_data.slice(0,4))
    })
  }

 onUnmounted(() => {
   if(Auth('welcome')) {
     myChart.value.dispose();
     charts.value.dispose();
   }
  });

  function drawBar(data){
    // 基于准备好的dom，初始化echarts实例
    // let myChart = echarts.init(document.querySelector(`#bar`))
    myChart.value = echarts.init(document.querySelector(`#bar`))
    // 绘制图表
    myChart.value.setOption({
      title: {
        text: '设备用途',
        // subtext: '',
        left: 'left',
        padding: 20,
        textStyle: {
          fontSize: 30
        }
      },
      tooltip: {
        trigger: 'item'
      },
      legend: {
        type: 'scroll',
        orient: 'vertical',
        right: 20,
        top: 20,
      },
      color: ['#3270ff', '#26e39c', '#f6b439', '#6236ff'],
      series: [
        {
          name: '设备用途汇总',
          type: 'pie',
          radius: ['40%', '67%'],
          avoidLabelOverlap: true,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: true,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '20',
              fontWeight: 'bold',
            }
          },
          labelLine: {
            show: true
          },
          label: {
            normal: {
              formatter: params => {
                return (
                  '{name|' + params.name + '}{value|' + params.value + '}'
                );
              },
              rich: {
                icon: {
                  fontSize: 16
                },
                name: {
                  fontSize: 14,
                  padding: [0, 10, 0, 4],
                  color: '#666666'
                },
                value: {
                  fontSize: 18,
                  fontWeight: 'bold',
                  color: '#333333'
                }
              }
            }
          },
          data: data,
        }
      ],

    });
    window.addEventListener("resize", function () {
      myChart.value.resize();
    })
  }
  function drawMap(data) {
    // let charts = echarts.init(document.querySelector(`#map`));
    charts.value = echarts.init(document.querySelector(`#map`));
    let max = 0
    data.forEach(item => {
      if(item.value > max){
        max = item.value
      }
    })

    charts.value.setOption({
      backgroundColor: "#FFFFFF",
      title: {
        text: "设备分布",
        subtext: "",
        left: 'left',
        padding: 20,
        textStyle: {
          fontSize: 30
        }
      },
      tooltip: {
        trigger: "item"
      },

      //左侧小导航图标
      visualMap: {
        min: 0,
        max: max,
        text: ["高", "低"],
        inRange: {
          color: ["#FFFFFF", "#B80A09"]//此处是设置颜色过渡
        }
      },

      //配置属性
      series: [
        {
          name: "设备分布",
          type: "map",
          mapType: "china", // ---此处是中国地图样式-------需要注意：省份中应使用汉字即如 ‘mapType:"河南"’
          roam: true,
          label: {
            normal: {
              show: true //省份名称----你可以选择true，展示每个省份的名称
            },
            emphasis: {
              show: true, //是否有高亮效果
              textStyle: {
                fontWeight: 'bold'
              }
            }
          },
          data: data
        }
      ]
    }),
      window.addEventListener("resize", function () {
        charts.value.resize();
      })
  }

</script>

<style scoped>
  .data-box{
    width: 100%;
    padding: 10px;
  }
  .data-box tr td{
    padding: 15px;
  }
  .data-box tr td div{
    height: 120px;
    width: 100%;
    border-radius: 10px;
    /*border: 1px solid #3270ff;*/
    background-color: white;
    font-size: 20px;
    font-weight: 500;
    color: #909399;
    font-family: MicrosoftYaHei;
    padding: 20px 0 0 20px;
    position: relative;
  }

  .data-box-item .active{
    background-image: url("../assets/dashboard/choose.png");
    background-repeat: no-repeat;
    background-position: top right;
    border: 1px solid #3270ff;
  }

  .data-box-item .qc{
    background-image: url("../assets/dashboard/choose1.png");
    background-repeat: no-repeat;
    background-position: top right;
    border: 1px solid #04bab1;
  }

  .title-box{
    width: 100%;
    padding: 0 10px;
  }
  .title-box tr td{
    padding: 0 15px;
  }
  .title-box tr td div{
    height: 30px;
    width: 100%;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    font-size: 20px;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: MicrosoftYaHei;
    position: relative;
  }

  .title-box .title-qc{
    background-color: #04bab1;
  }

  .title-box .title-ac{
    background-color: #3270ff;
  }

  .data-box tr td div p{
    font-size: 30px;
    line-height: 50px;
    font-weight: bold;
    color: black;
  }
  .data-box tr td div img{
    position: absolute;
    right: 0;
    bottom: 0;
  }
</style>
