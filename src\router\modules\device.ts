import { $t } from "/@/plugins/i18n";
const Layout = () => import("/@/layout/index.vue");

const deviceRouter = {
  path: "/device",
  name: "device",
  component: Layout,
  redirect: "/device/server",
  api: [],
  meta: {
    icon: "broadcast-line",
    title: "设备管理",
    i18n: true,
    rank: 6
  },
  children: [
    {
      path: "/device/server",
      name: "server",
      component: () => import("/@/views/device/server.vue"),
      api: ['server/list'],
      meta: {
        title: "服务器",
        icon: "bug-line",
      }
    },
    {
      path: "/device/client",
      name: "client",
      component: () => import("/@/views/device/client.vue"),
      api: ['client/list'],
      meta: {
        title: "客户端",
        icon: "fingerprint-fill",
      }
    },
    {
      path: "/service/version",
      name: "version",
      component: () => import("/@/views/device/version.vue"),
      api: ['version/list', 'version/add', 'version/download', 'version/delete'],
      meta: {
        title: "程序版本",
        icon: "node-tree",
      }
    },
    {
      path: "/service/version/device-list",
      name: "device-list",
      component: () => import("/@/views/device/version_device.vue"),
      api: ['version/device-list'],
      meta: {
        title: "设备列表",
        showLink: false,
      },
    }
  ]
};

export default deviceRouter;
