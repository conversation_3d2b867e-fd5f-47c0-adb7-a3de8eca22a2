import { http } from "../utils/http";

// 获取版本列表
export const versionListApi = (params?: object) => {
  return http.request("get", "version/list", { params });
};

//添加版本
export const versionAddApi = (data: object) => {
  return http.request("post", "version/add", { data });
};

//编辑版本
export const versionEditApi = (data) => {
  return http.request("post", "version/edit", { data });
};

//删除版本
export const versionDeleteApi = (data) => {
  return http.request("post", "version/delete", { data });
};

//设置版本状态
export const versionSetStatusApi = (params?: object) => {
  return http.request("get", "version/set-status", { params });
};

//切换版本渠道
export const versionSwitchApi = (data) => {
  return http.request("post", "version/switch-channel", { data });
};

//获取版本详情
export const versionDetailsApi = (params?: object) => {
  return http.request("get", "version/details", { params });
};

// 获取版本设备列表
export const versionDeviceListApi = (params?: object) => {
  return http.request("get", "version/device-list", { params });
};
