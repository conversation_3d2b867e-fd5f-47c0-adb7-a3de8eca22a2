<script setup lang="ts">
import Motion from "./utils/motion";
import {createRouter, useRouter} from "vue-router";
import { loginRules, REGEXP_PWD } from "./utils/rule";
import update from "./components/update.vue";
import { initRouter } from "/@/router/utils";
import { message } from "@pureadmin/components";
import type { FormInstance } from "element-plus";
import { storageSession } from "/@/utils/storage";
import {ref, reactive, watch, computed, onMounted} from "vue";
import { useUserStoreHook } from "/@/store/modules/user";
import { bg, logo, avatar, currentWeek } from "./utils/static";
// import logo from "/@/assets/logo.png";
import { ReImageVerify } from "/@/components/ReImageVerify";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import { loginApi, getSmsApi, loginSmsApi, loginQueryApi } from "/@/api/public";
import { setToken } from "/@/utils/auth";
import { getCurrentInstance } from "vue";
import {usePermissionStoreHook} from "/@/store/modules/permission";
import authorRouter from "/@/router/modules/author";

const title = getCurrentInstance().appContext.config.globalProperties.$config?.Title;

const imgCode = ref("");
const router = useRouter();
const loading = ref(false);
const checked = ref(false);
const ruleFormRef = ref<FormInstance>();
const currentPage = computed(() => {
  return useUserStoreHook().currentPage;
});

const ruleForm = reactive({
  username: "",
  password: "",
  code: ""
});

const Data =  reactive({
  sms: {
    id: 0,
    status: 0,
  },
  duration: 60,
  btnTitle: "获取验证码",
  is_send_sms: false,
})

const loginSMSRules = reactive(<FormRules>{
  username: [{ required: true, message: "请输入账号", trigger: "blur" }],
  password: [
    {
      validator: (rule, value, callback) => {
        if (value === "") {
          callback(new Error("请输入密码"));
        } else if (!REGEXP_PWD.test(value)) {
          callback(
            new Error("密码格式应为8-18位数字、字母、符号的任意两种组合")
          );
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  code: [
    { required: true, message: "请输入短信验证码", trigger: "blur" }
  ]
});

onMounted(() => {
  localStorage.removeItem("_load_router")
  getLoginStatus()
});

async function getLoginStatus() {
    let { code, data } = await loginQueryApi({type: 'SMS'});
    if(code === 0) {
      if(data?.length>0){
        Data.sms.id = data[0].id
        Data.sms.status = data[0].status
      }
    }
  }

const onLogin = async (formEl: FormInstance | undefined) => {
  loading.value = true;
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      // 模拟请求，需根据实际开发进行修改
      if(Data.sms.status) {
        loginSmsApi(ruleForm).then(ret => {
          loading.value = false;
          if(ret.code === 0) {
            storageSession.setItem("info", ret.data);
            setToken(ret.data);
            message.success("登陆成功");
            router.replace("/");
          }
        })
        return fields;
      }
      loginApi(ruleForm).then(ret => {
        loading.value = false;
        if(ret.code === 0) {
          storageSession.setItem("info", ret.data);
          setToken(ret.data);
          message.success("登陆成功");
          // setTimeout(()=>{
          //   useRouter()
          // }, 500)
          router.replace("/");
        }else if(ret.code === 2) {
          //验证码错误
        }
      })
    } else {
      loading.value = false;
      return fields;
    }
  });
};

function getSmsHandel() {
  console.log("btn", Data.duration, Data.btnTitle)
  getSmsApi({username: ruleForm.username}).then(ret => {
    if(ret.code === 0){
      Data.duration--
      Data.btnTitle = Data.duration + ' s'
      let timer = setInterval(()=>{
        Data.duration--
        Data.btnTitle = Data.duration + ' s'
        if(Data.duration <= 0){
          clearInterval(timer)
          Data.duration = 60
          Data.btnTitle = "获取验证码"
        }
      }, 1000)
      Data.is_send_sms = true
    }
  })
}

function onHandle(value) {
  useUserStoreHook().SET_CURRENTPAGE(value);
}

watch(imgCode, value => {
  useUserStoreHook().SET_VERIFYCODE(value);
});
</script>

<template>
  <img :src="bg" class="wave"/>
  <div class="login-container">
    <div class="img">
    </div>
    <img :src="logo" style="position: absolute;top: 30px;" />
    <div style="position: absolute;top: 25px;left:250px;color:#547D96;font-size: 35px">广州爱孕记OA订单系统</div>
    <div class="login-box">
      <div class="login-form">
        <el-form
          v-if="currentPage === 0"
          ref="ruleFormRef"
          :model="ruleForm"
          :rules="Data.sms.status?loginSMSRules:loginRules"
          size="large"
        >
          <Motion :delay="100">
            <el-form-item><el-span style="color: #547D96;font-size: 20px">用户登录</el-span></el-form-item>
          </Motion>
          <Motion :delay="100">
            <el-form-item prop="username">
              <el-input
                clearable
                style="height:50px"
                :input-style="{ 'user-select': 'none' }"
                v-model="ruleForm.username"
                placeholder="账号"
                :prefix-icon="useRenderIcon('user')"
              />
            </el-form-item>
          </Motion>
          <Motion :delay="150">
            <el-form-item prop="password">
              <el-input
                clearable
                style="height:50px"
                :input-style="{ 'user-select': 'none' }"
                show-password
                v-model="ruleForm.password"
                placeholder="密码"
                :prefix-icon="useRenderIcon('lock')"
              />
            </el-form-item>
          </Motion>
          <Motion :delay="200">
            <el-form-item prop="code">
              <el-input v-if="Data.sms.status" :disabled="!Data.is_send_sms" v-model="ruleForm.code" placeholder="请输入验证码" style="width: 190px;height: 50px" maxlength="6" clearable
                @input="ruleForm.code=ruleForm.code.replace(/[^\d]/g, '')"/>
              <el-button v-if="Data.sms.status" :disabled="Data.duration >= 0 && Data.duration < 60"
                @click="getSmsHandel()" style="margin-left: 8px;width: 110px;height: 50px" type="dashed">{{ Data.btnTitle }}</el-button>
            </el-form-item>
          </Motion>
          <Motion :delay="250">
            <el-form-item>
              <el-button
                class="w-full mt-4"
                size="default"
                type="primary"
                style="background:-webkit-gradient(linear, 100% 0%, 0% 0%,from(#A7D0ED), to(#97D6C0));height:50px;font-size: 20px;font-weight: bold"
                :loading="loading"
                @click="onLogin(ruleFormRef)"
              >
                登录
              </el-button>
            </el-form-item>
          </Motion>
        </el-form>
      </div>
    </div>
  </div>
</template>

<style scoped>
@import url("/@/style/login.css");
</style>

<style lang="scss" scoped>
:deep(.el-input-group__append, .el-input-group__prepend) {
  padding: 0;
}
</style>
