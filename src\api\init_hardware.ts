import { http } from "../utils/http";

// 获取ukey列表
export const ukeyListApi = (params?: object) => {
  return http.request("get", "ukey/list", { params });
};

// 获取Ukey信息
export const ukeyInfoApi = (params?: object) => {
  return http.request("get", "ukey/info", { params });
};

// 获取Ukey管理员列表
export const ukeyAdminListApi = (params?: object) => {
  return http.request("get", "ukey/admin-list", { params });
};

//添加ukey
export const ukeyAddApi = (data: object) => {
  return http.request("post", "ukey/add", { data });
};

//修改ukey
export const ukeyEditApi = (data: object) => {
  return http.request("post", "ukey/edit", { data });
};

//删除ukey
export const ukeyDeleteApi = (data) => {
  return http.request("post", "ukey/delete", { data });
};

//设置ukey状态
export const ukeySetStatusApi = (params?: object) => {
  return http.request("get", "ukey/set-status", { params });
};

// 获取主任密钥列表
export const leaderKeyListApi = (params?: object) => {
  return http.request("get", "leader-key/list", { params });
};

// 获取主任密钥信息
export const leaderKeyInfoApi = (params?: object) => {
  return http.request("get", "leader-key/info", { params });
};

//添加主任密钥
export const leaderKeyAddApi = (data: object) => {
  return http.request("post", "leader-key/add", { data });
};

//删除主任密钥
export const leaderKeyDeleteApi = (data) => {
  return http.request("post", "leader-key/delete", { data });
};

//设置主任密钥状态
export const leaderKeySetStatusApi = (params?: object) => {
  return http.request("get", "leader-key/set-status", { params });
};
