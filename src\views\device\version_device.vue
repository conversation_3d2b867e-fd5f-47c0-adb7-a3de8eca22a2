<script setup lang="ts">
  import dayjs from "dayjs";
  import {FormInstance, ElMessageBox} from "element-plus";
  import {reactive, ref, onMounted} from "vue";
  import {EpTableProBar} from "/@/components/ReTable";
  import {Switch, message} from "@pureadmin/components";
  import {useRenderIcon} from "/@/components/ReIcon/src/hooks";
  import {
    versionListApi,
    versionAddApi,
    versionEditApi,
    versionDeleteApi,
    versionSetStatusApi,
    versionSwitchApi,
    versionDeviceListApi,
    versionDetailsApi
  } from "/@/api/version"
  import {hospitalListApi, uploadApi} from "/@/api/sys";
  import {formatOptions} from "/@/utils/tool";
  import {Auth} from "/@/utils/auth";
  import OSS from 'ali-oss'
  import {errorMessage} from "/@/utils/message";
  import {orderMealOptionsApi} from "/@/api/order";
  import {getRegionApi} from "/@/api/public";
  import router from "/@/router";

  const formRef = ref<FormInstance>();
  const ruleFormRef = ref<FormInstance>()
  let switchLoadMap = ref({});
  let client

  let OPTIONS = {
    status_options: {1: "启用", 2: "禁用"},
    channel_options: {0: "全部", 1: "正式版", 2: "试用版"},
    update_options: {1: "在线更新", 2: "U盘更新"},
    update_succeed: {1: "成功", 2: "失败"},
  }

  const Data = reactive({
    dialogVisible: false,

    search_from: {
      version_id: '',
      keyword: "",
      region: "",
      region_ids: [],
      hospital: "",
      hospital_id: "",
      serial_number: "",
      author_number: "",
      uuid: "",
      time: null,
      start_time: "",
      end_time: "",
      page_size: 10,
      page: 1,
      total: 0,
    },
    version_id: '',
    version_info: {
      version_number: "",
      channel: "",
      info: "",
      package_url: "",
      update_at: "",
      bug_fix: "",
      update_range: "",
      status: 0,
      remark: "",
    },
    loading: true,
    meal_options: [],
    region_tree: [],
    hospital_list: [],
    data_list: [],
  })


  async function onSearch() {
    Data.loading = true;
    Data.search_from.version_id = Data.version_id
    let {data} = await versionDeviceListApi(Data.search_from);
    if(data.total <= Data.search_from.page_size &&  Data.search_from.page > 1){
      Data.search_from.page = 1
      onSearch()
    }
    Data.data_list = data.list;
    Data.search_from.total = data.total;
    Data.loading = false;
    if(data.list == null){
      Data.data_list = []
    }
  }

  async function getRegion() {
    let { data } = await getRegionApi();
    Data.region_tree = data;
  }

  async function getHospitalList(region_id) {
    let { data } = await hospitalListApi({'region_id': region_id, 'page_size': 1000})
    Data.hospital_list = data.list;
  }

  function changeRegionHandel(value) {
    Data.search_from.hospital_id = "";
    Data.hospital_list = []
    getHospitalList(value)
  }

  async function getVersionInfo(version_id) {
    let { data } = await versionDetailsApi({'version_id': Data.version_id})
    Data.version_info = data;
  }

  onMounted(() => {
    if(!router.currentRoute.value.query.version_id){
      router.push({'name': 'version'})
      return
    }
    client = new OSS({
      region: "guangzhou",
      accessKeyId: "LTAI5t5fJKXCXU6wfoeUohrX",
      accessKeySecret: "******************************",
      bucket: "remote-qc-center",
      secure: false,
      endpoint: "oss-cn-guangzhou.aliyuncs.com",
    })
    Data.version_id = router.currentRoute.value.query.version_id
    getRegion();
    onSearch();
    getMealOptions()
    getVersionInfo(Data.version_id)
  });

  async function getMealOptions() {
    let { data } = await orderMealOptionsApi();
    Data.meal_options = data.meal_options.slice(7);
  }

  function formatUpdateRange(update_range) {
    if(update_range.length == 0) {
      return "全部"
    }
    let meals = update_range.split(",")
    let names = ""
    meals.forEach(id => {
      Data.meal_options.forEach(item1 => {
        if(id == item1.id) {
          names = names + ", " + item1.name
        }
      })
    })
    return names.slice(1)
  }

  function formatPackageUrl(package_url) {
    if(package_url.search(/(\d{13}-)/g) != -1) {
      var index = package_url.indexOf('-')
      return package_url.slice(index+1)
    }
    return package_url
  }

  const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    formEl.resetFields();
    Data.search_from.keyword = ''
    onSearch();
  };

</script>

<template>
  <div class="main">
    <div class="version-device">
      <div class="version">
        <el-space class="title">版本信息</el-space>
        <div class="ver-info">
          <div class="info">
            <div class="col1">
              <el-space>版本号：</el-space>
              <el-space style="color: #04bab1">{{Data.version_info.version_number}}</el-space>
            </div>
            <div class="col2">
              <el-space>渠道：{{OPTIONS.channel_options[Data.version_info.channel]}}</el-space>
            </div>
            <div class="col3">
              <el-space>发布信息：{{Data.version_info.info}}</el-space>
            </div>
          </div>
          <div class="info">
            <div class="col1">
              <el-space>更新包：</el-space>
              <a :style="Data.version_info.status==2?'pointer-events:none;':'color: #04bab1;'" target="downloadFile"
                 :href="'https://aiyunji-versin-mgr.oss-cn-hangzhou.aliyuncs.com/setup_package/' + Data.version_info.package_url">{{formatPackageUrl(Data.version_info.package_url)}}</a>
            </div>
            <div class="col2">
              <el-space>更新时间：{{Data.version_info.update_at}}</el-space>
            </div>
            <div class="col3">
              <el-space>BUG修复信息：{{Data.version_info.bug_fix}}</el-space>
            </div>
          </div>
          <div class="info">
            <div class="col1">
              <el-space>套餐：{{formatUpdateRange(Data.version_info.update_range)}}</el-space>
            </div>
            <div class="col2">
              <el-space>状态：</el-space>
              <el-space style="color: #04bab1">{{OPTIONS.status_options[Data.version_info.status]}}</el-space>
            </div>
            <div class="col3">
              <el-space>备注：{{Data.version_info.remark}}</el-space>
            </div>
          </div>
        </div>
        <div class="ver-search">
          <el-form ref="formRef" :inline="true" :model="Data.search_from" class="bg-white w-99/100 pl-8 pt-4">
            <el-form-item label="关键词搜索：" prop="key_word" label-width="110px">
              <el-input style="width: 200px;" v-model="Data.search_from.keyword" placeholder="授权单位、U-Key" clearable/>
            </el-form-item>
            <el-form-item label="选择区域:" prop="region_ids" label-width="100px">
              <el-cascader :options="Data.region_tree" v-model="Data.search_from.region_ids" @change="changeRegionHandel"/>
            </el-form-item>
            <el-form-item label="授权单位:" prop="hospital_id" label-width="100px">
              <el-select v-model="Data.search_from.hospital_id" placeholder="请选择授权单位" clearable>
                <el-option v-for="(item,index) in Data.hospital_list" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="设备序列号：" prop="serial_number">
              <el-input style="width: 200px;" v-model="Data.search_from.serial_number" placeholder="请输入设备序列号"
                        clearable/>
            </el-form-item>
            <el-form-item label="授权码：" prop="author_number" label-width="110px">
              <el-input style="width: 200px;" v-model="Data.search_from.author_number" placeholder="请输入授权码" clearable/>
            </el-form-item>
            <el-form-item label="UUID：" prop="uuid" label-width="100px">
              <el-input style="width: 200px;" v-model="Data.search_from.uuid" placeholder="请输入UUID" clearable/>
            </el-form-item>
            <el-form-item label="更新时间：" prop="time" label-width="100px">
              <el-date-picker
                v-model="Data.search_from.time"
                type="datetimerange"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
              />
            </el-form-item>
            <el-form-item class="search" style="width: 40%">
              <el-button
                type="primary"
                :icon="useRenderIcon('search')"
                :loading="Data.loading"
                @click="onSearch">搜索
              </el-button>
              <el-button :icon="useRenderIcon('refresh')" @click="resetForm(formRef)">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="device">
        <EpTableProBar
          title="设备列表"
          :loading="Data.loading"
          :columnList="[
        {label: '升级前版本号', show: true},
        {label: '升级状态', show: true},
        {label: '授权码', show: true},
        {label: '区域', show: true},
        {label: '授权单位', show: true},
        {label: 'UKey代码', show: true},
        {label: '设备系列号', show: true},
        {label: 'UUID', show: true},
        {label: '更新方式', show: true},
        {label: '更新时间', show: true}]"
          :dataList="Data.data_list"
          @refresh="onSearch">
          <template #buttons></template>
          <template v-slot="{ size, checkList }">
            <el-table
              border
              table-layout="auto"
              :size="size"
              :data="Data.data_list"
              :header-cell-style="{ background: '#fafafa', color: '#606266' }"
              @selection-change="handleSelectionChange">
              <el-table-column v-if="checkList.includes('升级前版本号')" prop="cur_version_number" label="升级前版本号" align="center"></el-table-column>
              <el-table-column v-if="checkList.includes('升级状态')" prop="update_succeed" label="升级状态" align="center">
                <template v-slot="scope">
                  <el-space>{{OPTIONS.update_succeed[scope.row.update_succeed]}}</el-space>
                </template>
              </el-table-column>
              <el-table-column v-if="checkList.includes('授权码')" prop="author_number" label="授权码" align="center"></el-table-column>
              <el-table-column v-if="checkList.includes('区域')" prop="region_name" label="区域" align="center"></el-table-column>
              <el-table-column v-if="checkList.includes('授权单位')" prop="hospital_name" label="授权单位" align="center"></el-table-column>
              <el-table-column v-if="checkList.includes('UKey代码')" prop="ukey_code" label="UKey代码" align="center"></el-table-column>
              <el-table-column v-if="checkList.includes('设备系列号')" prop="serial_number" label="设备系列号" align="center"></el-table-column>
              <el-table-column v-if="checkList.includes('UUID')" prop="uuid" label="uuid" align="center"></el-table-column>
              <el-table-column v-if="checkList.includes('更新方式')" prop="update_type" label="更新方式" align="center">
                <template v-slot="scope">
                  <el-space>{{OPTIONS.update_options[scope.row.update_type]}}</el-space>
                </template>
              </el-table-column>
              <el-table-column v-if="checkList.includes('更新时间')" prop="update_time" label="更新时间" align="center"></el-table-column>
            </el-table>
            <el-pagination
              class="flex justify-end mt-4"
              :small="size === 'small' ? true : false"
              v-model:page-size="Data.search_from.page_size"
              :page-sizes="[10, 20, 30, 50]"
              :background="true"
              layout="total, sizes, prev, pager, next, jumper"
              :total="Data.search_from.total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"/>
          </template>
        </EpTableProBar>
      </div>
    </div>
  </div>
</template>
<style scoped lang="scss">
  :deep(.el-dropdown-menu__item i) {
    margin: 0;
  }

  .version-device {
    .version {
      margin: 10px;
      background-color: white;
      .title{
        margin: 5px 24px;
        font-weight: bold;
      }
      .ver-info {
        display: flex;
        flex-direction: column;
        margin: 10px 20px;
        .info {
          display: flex;
          justify-content: flex-start;
          margin: 5px 20px;
          .col1 {
            width: 30%;
          }
          .col2 {
            width: 30%;
          }
          .col3 {
            width: 40%;
          }
        }
      }
      .ver-search {
        .el-form .el-form-item{
          width: 20%;
        }
      }
    }

    .device {
      margin: 10px;
      background-color: white;
    }
  }

</style>
