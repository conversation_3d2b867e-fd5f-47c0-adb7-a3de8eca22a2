<script setup lang="ts">
  import { loadEnv } from "@build/index";
  import dayjs from "dayjs";
  import { FormInstance, ElMessageBox, ElLoading } from "element-plus";
  import { reactive, ref, onMounted, onUnmounted, watch } from "vue";
  import { EpTableProBar } from "/@/components/ReTable";
  import { Switch, message } from "@pureadmin/components";
  import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
  import {
    ukeyListApi,
    ukeyAddApi,
    ukeyEditApi,
    ukeyDeleteApi,
    ukeySetStatusApi,
    ukeyInfoApi,
    ukeyAdminListApi,
  } from "/@/api/init_hardware"
  import { download } from "/@/utils/tool"
  import { Auth } from "/@/utils/auth";

  const { VITE_PROXY_DOMAIN, VITE_PROXY_DOMAIN_REAL } = loadEnv();

  const formRef = ref<FormInstance>();
  const ruleFormRef = ref<FormInstance>()
  let switchLoadMap = ref({});

  const Data =  reactive({
    ws: null,
    dialogVisible: false,
    search_from: {
      serial_number: "",
      code: "",
      username: "",
      type: "",
      status: "",
      page_size: 10,
      page: 1,
      total: 0,
    },
    data_from: {
      id: 0,
      tip: "",
      serial_number: "",
      code: "",
      type: "",
      public_key: "",
      admin_id: "",
      auth_times: 1,
      remark: '',
    },
    loading: true,
    data_list: [],
    admin_list: [],
    ukey_data: {
      key_id: "",
      public_key: "",
      key_init_status: 0,
      msg_type: 0,
    },
    rules: {
      serial_number: [
        {required: true, message: '请输入U-Key', trigger: 'blur'},
      ],
      code: [
        {required: true, message: '请输入U-Key代码', trigger: 'blur'},
        {min: 5, max: 5, message: 'U-Key代码限定为5个字符', trigger: ['blur', 'change']},
      ],
      type: [
        {required: true, message: '授权类型必填', trigger: ['blur', 'change']},
      ],
      admin_id: [
        {required: true, message: '绑定用户必填', trigger: ['blur', 'change']},
      ],
      auth_times: [
        {required: true, message: '授权次数必填', trigger: ['blur', 'change']},
      ],
    },
    max_auth_times: 999,
    min_auth_times: 1,
  })

  async function getAdminList() {
    let { data } = await ukeyAdminListApi({page_size: 1000});
    Data.admin_list = data.list;
  }

  function handleUpdate(row) {
    if(row != null ){
      getAdminList()
      Data.data_from = JSON.parse(JSON.stringify(row))
      Data.data_from.tip = ""
      Data.ukey_data = {
        key_id: "",
        public_key: "",
        key_init_status: 0,
        msg_type: 0,
      }
      Data.min_auth_times = row?.used_times>0?row.used_times:1
      Data.dialogVisible = true
    }else {
      const loading = ElLoading.service({
        lock: true,
        text: '识别U-Key...',
        background: 'rgba(0, 0, 0, 0.9)',
      })
      Data.data_from = {
          id: 0,
          tip: "",
          serial_number: "",
          code: "",
          type: "",
          public_key: "",
          admin_id: "",
          auth_times: 1,
          remark: '',
      }
      Data.min_auth_times = 1

      setTimeout(()=>{
        initSocket(loading)
      }, 100)
    }
  }

  //清除添加/修改缓存
  function clearCache() {
    Data.data_from = {
      id: 0,
      tip: "",
      serial_number: "",
      code: "",
      type: "",
      public_key: "",
      admin_id: "",
      auth_times: 1,
      remark: '',
    }
    Data.ukey_data = {
      key_id: "",
      public_key: "",
      key_init_status: 0,
      msg_type: 0,
    }
    Data.dialogVisible = false
  }

  //重置ukey
  function resetUkeyHandel(){
    if(Data.ws != null){
      Data.ws.send(JSON.stringify({msg_type:203}))
    }
  }
  async function handleDelete(row) {
    let { code } = await ukeyDeleteApi({ids: [row.id]});
    if(code === 0){
      message.success("删除成功")
      onSearch()
    }
  }
  function handleCurrentChange(val: number) {
    Data.search_from.page = val
    onSearch()
  }
  function handleSizeChange(val: number) {
    Data.search_from.page_size = val
    onSearch()
  }
  function handleSelectionChange(val) {
    console.log("handleSelectionChange", val);
  }

  function onChange(checked, { $index, row }) {
    ElMessageBox.confirm(
      `确认要<strong>${row.status === 0 ? "停用" : "启用"}</strong><strong style='color:var(--el-color-primary)'>${row.code}</strong>吗?`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    )
      .then(() => {
        switchLoadMap.value[$index] = Object.assign({}, switchLoadMap.value[$index], {loading: true});
        ukeySetStatusApi({id: row.id, status: row.status}).then(ret => {
          switchLoadMap.value[$index] = Object.assign({}, switchLoadMap.value[$index], {loading: false});
          if(ret.code === 0){
            message.success("修改成功");
          }
          onSearch()
        })
      })
      .catch(() => {
        row.status === 0 ? (row.status = 1) : (row.status = 0);
      });
  }
  async function onSearch() {
    Data.loading = true;
    let { data } = await ukeyListApi(Data.search_from);
    if(data.total <= Data.search_from.page_size &&  Data.search_from.page > 1){
      Data.search_from.page = 1
      onSearch()
    }
    Data.data_list = data.list;
    Data.search_from.total = data.total;
    Data.loading = false;
  }
  const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    formEl.resetFields();
    onSearch();
  };

  onMounted(() => {
    onSearch();
  });

  onUnmounted(() => {
    if(Data.ws != null) {
      Data.ws.close()
    }
  })

  const initSocket = (loading) => {
    try {
      Data.ws = new WebSocket("ws://127.0.0.1:26666/hardware/websocket"),
      // ws连接成功
      Data.ws.onopen = function () {
        Data.ws.send(JSON.stringify({msg_type:201}))
      }
      // ws连接关闭
      Data.ws.onclose = function () {
        Data.ws.close()
      }
      // ws连接错误
      Data.ws.onerror = function () {
        loading.close()
        ElMessageBox.confirm(
          'U-Key授权插件异常，请检查插件是否正确安装！若未安装，是否下载授权插件？',
          "系统提示",
          {
            confirmButtonText: "下载插件",
            cancelButtonText: "取消",
            type: "warning",
            dangerouslyUseHTMLString: true,
            draggable: true
          }
        )
        .then(() => {
          let t = new Date().getTime()
          download(VITE_PROXY_DOMAIN_REAL + "../plugin/AyjHardwareMgrService.exe", "AyjHardwareMgrService.exe")
        }).catch(() => {})
        Data.ws.close()
      };
      // ws数据返回处理
      Data.ws.onmessage = function (result) {
        loading.close()
        let data = JSON.parse(result.data)
        if(data.code === 0){
          Data.ukey_data = data.data
          Data.data_from.serial_number = data.data.key_id
          // message.success(data.msg)

          if(data.data.msg_type == 201){
            Data.dialogVisible = true
            ukeyInfoApi({'serial_number': Data.data_from.serial_number}).then(res => {
              if(res.data != null) {
                Data.data_from.tip = "此U-Key已经被注册"
              }
            })

            resetForm(ruleFormRef.value)
            getAdminList()
          }


          if(data.data.msg_type == 202){
            Data.data_from.public_key = data.data.public_key
            ukeyAddApi(Data.data_from).then(ret => {
              if(ret.code === 0){
                  message.success("ukey创建成功")
                  clearCache()
                  onSearch()
              }
            })
          }

          if(data.data.msg_type == 203){
              Data.ukey_data.key_init_status = 0
          }
        }else {
          message.error(data.msg)
        }
      };
    } catch (e) {
      alert(e.message);
    }

  };


  const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async(valid, fields) => {
      if (valid) {
        if(Data.data_from.id > 0){
          ukeyEditApi(Data.data_from).then(ret => {
            if(ret.code === 0){
              message.success("ukey修改成功")
              clearCache()
              onSearch()
            }
          })
        }else {
          confirmSubmit()
        }
      }
    })
  }

  function confirmSubmit() {
    ElMessageBox.confirm(
      '确认初始化U-Key?',
      'Warning',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }
    ).then(() => {
      Data.ws.send(JSON.stringify({msg_type:202}))
    })
    .catch(() => {
    })
  }

  function TypeOptions(value) {
    let type = [
        {value: 1, label: "正式授权密钥"},
        {value: 2, label: "测试机授权密钥"},
        {value: 3, label: "内侧授权密钥"},
      ]

    if(value > 0){
      let str = ""
      type.forEach(item => {
        if(item.value == value){
          str = item.label
        }
      })
      return str
    }
    return type
  }
</script>

<template>
  <div class="main">
    <el-form ref="formRef" :inline="true" :model="Data.search_from" class="bg-white w-99/100 pl-8 pt-4">
      <el-form-item label="硬件ID：" prop="serial_number">
        <el-input style="width: 200px;" v-model="Data.search_from.serial_number" placeholder="请输入硬件ID" clearable />
      </el-form-item>
      <el-form-item label="U-Key代码：" prop="code">
        <el-input style="width: 200px;" v-model="Data.search_from.code" placeholder="请输入U-Key代码" clearable />
      </el-form-item>
      <el-form-item label="用户名：" prop="username">
        <el-input style="width: 200px;" v-model="Data.search_from.username" placeholder="请输入用户名称" clearable />
      </el-form-item>
      <el-form-item label="密钥类型：" prop="type">
        <el-select v-model="Data.search_from.type" placeholder="请选择状态" clearable>
          <el-option v-for="(item,index) in TypeOptions()" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态：" prop="status">
        <el-select v-model="Data.search_from.status" placeholder="请选择状态" clearable>
          <el-option label="已开启" value="1" />
          <el-option label="已关闭" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('search')"
          :loading="Data.loading"
          @click="onSearch">搜索</el-button>
        <el-button :icon="useRenderIcon('refresh')" @click="resetForm(formRef)">重置</el-button>
      </el-form-item>
    </el-form>

    <EpTableProBar
      title="U-Key列表"
      :loading="Data.loading"
      :columnList="[
        {label: '编号', show: true},
        {label: '硬件ID', show: true},
        {label: 'U-Key代码', show: true},
        {label: '类型', show: true},
        {label: '公钥', show: false},
        {label: '绑定用户', show: true},
        {label: '授权次数', show: true},
        {label: '已授权次数', show: true},
        {label: '创建者', show: true},
        {label: '备注', show: true},
        {label: '状态', show: true},
        {label: '更新时间', show: true}]"
      :dataList="Data.data_list"
      @refresh="onSearch">
      <template #buttons>
        <el-button v-if="Auth('ukey/add')" type="primary" :icon="useRenderIcon('add')" @click="handleUpdate(null)">添加U-Key</el-button>
      </template>
      <template v-slot="{ size, checkList }">
        <el-table
          border
          table-layout="auto"
          :size="size"
          :data="Data.data_list"
          :header-cell-style="{ background: '#fafafa', color: '#606266' }"
          @selection-change="handleSelectionChange">
          <el-table-column v-if="checkList.includes('编号')" label="编号" align="center" prop="id" />
          <el-table-column v-if="checkList.includes('硬件ID')" label="硬件ID" align="center" width="160" prop="serial_number" />
          <el-table-column v-if="checkList.includes('U-Key代码')" label="U-Key代码" align="center" width="100" prop="code"></el-table-column>
          <el-table-column v-if="checkList.includes('类型')" label="类型" align="center" width="150" prop="type">
            <template #default="scope">
              {{TypeOptions(scope.row.type)}}
            </template>
          </el-table-column>
          <el-table-column v-if="checkList.includes('绑定用户')" label="绑定用户" align="center" prop="username"/>
          <el-table-column v-if="checkList.includes('授权次数')" label="授权次数" align="center" width="100" prop="auth_times" />
          <el-table-column v-if="checkList.includes('已授权次数')" label="已授权次数" align="center" width="100" prop="used_times" />
          <el-table-column v-if="checkList.includes('创建者')" label="创建者" align="center" prop="create_name" />
          <el-table-column v-if="checkList.includes('备注')" label="备注" align="center" prop="remark" />
          <el-table-column v-if="checkList.includes('状态')" label="状态" align="center" width="130" prop="status">
            <template #default="scope">
              <Switch
                :disabled="!Auth('ukey/set-status')"
                :size="size === 'small' ? 'small' : 'default'"
                :loading="switchLoadMap[scope.$index]?.loading"
                v-model:checked="scope.row.status"
                :checkedValue="1"
                :unCheckedValue="0"
                checked-children="已开启"
                un-checked-children="已关闭"
                @change="checked => onChange(checked, scope)"
              />
            </template>
          </el-table-column>
          <el-table-column v-if="checkList.includes('更新时间')" label="创建时间" align="center" width="180" prop="update_at"/>
          <el-table-column fixed="right" label="操作" width="180" align="center">
            <template #default="scope">
              <el-button v-if="Auth('ukey/delete')"
                class="reset-margin"
                type="text"
                :size="size"
                @click="handleUpdate(scope.row)"
                :icon="useRenderIcon('edits')">
                修改
              </el-button>
              <el-popconfirm  v-if="Auth('ukey/delete')" title="是否确认删除?" @confirm="handleDelete(scope.row)">
                <template #reference>
                  <el-button
                    class="reset-margin"
                    type="text"
                    :size="size"
                    :icon="useRenderIcon('delete')">
                    删除
                  </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          class="flex justify-end mt-4"
          :small="size === 'small' ? true : false"
          v-model:page-size="Data.search_from.page_size"
          :page-sizes="[10, 20, 30, 50]"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="Data.search_from.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"/>
      </template>
    </EpTableProBar>


    <el-dialog v-model="Data.dialogVisible" :title="Data.data_from.id>0?'修改U-Key':'添加U-Key'" width="450px" draggable @close="clearCache">
      <el-form
        ref="ruleFormRef"
        :model="Data.data_from"
        :rules="Data.rules"
        label-position="left"
        label-width="150px">
        <p style="color: red;margin-bottom: 20px" v-if="Data.data_from.tip">注意：{{Data.data_from.tip}}</p>
        <el-form-item label="识别到的U-Key:" prop="serial_number">
          <span style="margin-right: 15px;">{{Data.data_from.serial_number}}</span>
          <el-button :disabled="Data.data_from.tip != ''" v-if="Data.ukey_data.key_init_status !== 0" type="danger" @click="resetUkeyHandel">重置U-Key</el-button>
        </el-form-item>
        <el-form-item label="U-Key代码:" prop="code">
          <el-input :disabled="Data.data_from.id>0" style="width: 214px;margin-right: 10px;" v-model="Data.data_from.code" show-word-limit maxlength="5"/>
        </el-form-item>
        <el-form-item label="U-Key授权类型:" prop="type">
          <el-select :disabled="Data.data_from.id>0" v-model="Data.data_from.type" placeholder="请选择授权类型" clearable>
            <el-option v-for="(item,index) in TypeOptions()" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="绑定授权用户:" prop="admin_id">
          <el-select :disabled="Data.data_from.id>0" v-model="Data.data_from.admin_id" placeholder="请选择授权用户" clearable>
              <el-option v-for="(item,index) in Data.admin_list" :label="item.username" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="设置授权次数:" prop="auth_times">
          <el-input-number :disabled="Data.max_auth_times<=0" v-model="Data.data_from.auth_times" :min="Data.min_auth_times" :max="Data.max_auth_times"/>
        </el-form-item>
        <el-form-item label="备注:" prop="remark">
          <el-input style="width: 214px;margin-right: 10px;" v-model="Data.data_from.remark" show-word-limit maxlength="20"/>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="clearCache">取消</el-button>
          <el-button :disabled="Data.data_from.tip != '' || Data.ukey_data.key_init_status !== 0" type="primary" @click="submitForm(ruleFormRef)">确认</el-button>
        </span>
      </template>
    </el-dialog>

  </div>

</template>
<style scoped lang="scss">
  :deep(.el-dropdown-menu__item i) {
    margin: 0;
  }

</style>
