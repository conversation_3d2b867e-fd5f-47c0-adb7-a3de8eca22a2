import { http } from "../utils/http";

// 获取特征树列表
export const featureTreeApi = (params?: object) => {
  return http.request("get", "kl-feature/tree", { params });
};

// 获取特征详情
export const featureDetailsApi = (params?: object) => {
  return http.request("get", "kl-feature/details", { params });
};

//设置特征下级列表
export const featureChildListApi = (params?: object) => {
  return http.request("get", "kl-feature/child-list", { params });
};

//添加特征
export const featureAddApi = (data: object) => {
  return http.request("post", "kl-feature/add", { data });
};

//编辑特征
export const featureEditApi = (data) => {
  return http.request("post", "kl-feature/edit", { data });
};

//删除特征
export const featureDeleteApi = (data) => {
  return http.request("post", "kl-feature/delete", { data });
};

//设置特征状态
export const featureSetStatusApi = (params?: object) => {
  return http.request("get", "kl-feature/set-status", { params });
};

//特征列表同级排序
export const featureSetSortApi = (params?: object) => {
  return http.request("get", "kl-feature/set-sort", { params });
};

//特征分组
export const featureGroupListApi = (params?: object) => {
  return http.request("get", "kl-feature/group-list", { params });
};


// 获取综合征树列表
export const syndromeTreeApi = (params?: object) => {
  return http.request("get", "kl-syndrome/tree", { params });
};

// 获取综合征类型
export const syndromeTypeApi = (params?: object) => {
  return http.request("get", "kl-syndrome/type", { params });
};


//添加综合征
export const syndromeAddApi = (data: object) => {
  return http.request("post", "kl-syndrome/add", { data });
};

//编辑综合征
export const syndromeEditApi = (data) => {
  return http.request("post", "kl-syndrome/edit", { data });
};

//删除综合征
export const syndromeDeleteApi = (data) => {
  return http.request("post", "kl-syndrome/delete", { data });
};

//设置综合征状态
export const syndromeSetStatusApi = (params?: object) => {
  return http.request("get", "kl-syndrome/set-status", { params });
};

//综合征列表同级排序
export const syndromeSetSortApi = (params?: object) => {
  return http.request("get", "kl-syndrome/set-sort", { params });
};

//综合征详情接口
export const syndromeDetailsApi = (params?: object) => {
  return http.request("get", "kl-syndrome/details", { params });
};

//知识图谱版本列表
export const versionListApi = (params?: object) => {
  return http.request("get", "kl-version/list", { params });
};

//知识图谱创建版本
export const versionAddApi = (data) => {
  return http.request("post", "kl-version/add", { data });
};

//删除版本
export const versionDeleteApi = (data) => {
  return http.request("post", "kl-version/delete", { data });
};




