import {$t} from "/@/plugins/i18n";

const Layout = () => import("/@/layout/index.vue");

const sysRouter = {
  path: "/sys",
  name: "sys",
  value: "sys",
  component: Layout,
  redirect: "/sys/admin",
  api: [],
  meta: {
    icon: "setting",
    title: "系统管理",
    i18n: true,
    rank: 8
  },
  children: [
    {
      path: "/sys/admin",
      name: "admin",
      value: "admin",
      component: () => import("/@/views/sys/admin.vue"),
      api: ['sys-admin/list', 'sys-admin/department', "sys-admin/add", "sys-admin/edit", "sys-admin/set-status", "sys-admin/delete"],
      meta: {
        title: "用户管理",
        icon: "admin-line",
      }
    },
    {
      path: "/sys/role",
      name: "role",
      value: "role",
      component: () => import("/@/views/sys/role.vue"),
      api: ['sys-role/list', 'sys-role/department', 'sys-role/add', 'sys-role/edit', 'sys-role/set-status', 'sys-role/delete'],
      meta: {
        title: "系统角色",
        icon: "virus-line",
      }
    },
    {
      path: "/sys/hospital",
      name: "hospital",
      value: "hospital",
      component: () => import("/@/views/sys/hospital.vue"),
      api: ['hospital/list', 'hospital/personal', 'hospital/add', 'hospital/edit', 'hospital/set-status', 'hospital/delete'],
      meta: {
        title: "医院管理",
        icon: "hospital-line",
      }
    },

    {
      path: "/sys/agent",
      name: "agent",
      value: "agent",
      component: () => import("/@/views/sys/agent.vue"),
      api: ['agent/list', 'agent/personal', 'agent/add', 'agent/edit', 'agent/set-status', 'agent/delete'],
      meta: {
        title: "代理商管理",
        icon: "proxy",
      }
    },

    // {
    //   path: "/sys/syslog",
    //   name: "syslog",
    //   value: "syslog",
    //   component: () => import("/@/views/sys/syslog.vue"),
    //   api: ['hospital/list', 'hospital/personal', 'hospital/add', 'hospital/edit', 'hospital/set-status', 'hospital/delete'],
    //   meta: {
    //     alicion:'icon-rizhi',
    //     title: "系统日志",
    //     icon: "syslog-line",
    //   }
    // },


  ]
};
if (JSON.parse(sessionStorage.getItem("info"))?.id == '1') {
  sysRouter.children.push(
    {
      path: "/sys/syslog",
      name: "syslog",
      value: "syslog",
      component: () => import("/@/views/sys/syslog.vue"),
      api: ['hospital/list', 'hospital/personal', 'hospital/add', 'hospital/edit', 'hospital/set-status', 'hospital/delete'],
      meta: {
        // @ts-ignore
        "alicion":"icon-rizhi",
        title: "系统日志",
        icon: "syslog-line",
      }
    },
  )
}

export default sysRouter;
