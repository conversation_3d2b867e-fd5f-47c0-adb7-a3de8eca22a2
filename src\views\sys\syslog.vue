<template>
  <div class="main">


    <el-form
             ref="ruleFormRef"
             :inline="true"
             :model="_self.qurey"
      class="bg-white w-99/100 pl-8 pt-4">

      <el-form-item label="账户名称：" prop="name">
        <el-input style="width: 200px;" v-model="_self.qurey.name" placeholder="请输入账户名称" clearable/>
      </el-form-item>

      <el-form-item label="信息类型：" prop="status">
        <el-select v-model="_self.qurey.func_type" placeholder="请选择信息类型" clearable>
          <el-option
            v-for="item in _self.options"
            :key="item.value"
            :label="item.value"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="关键字搜索：" prop="name">
        <el-input style="width: 200px;" v-model="_self.qurey.sign" placeholder="请输入(主要标识或者修改项)关键字搜索" clearable/>
      </el-form-item>


      <el-form-item label="日志时间：" prop="name">


        <el-date-picker
          v-model="_self.qurey.start_time"
          type="date"
          placeholder="年/月/日"
        />
        <span style="margin: 0 10px">
                至
          </span>

        <el-date-picker
          v-model="_self.qurey.end_time"
          type="date"
          placeholder="年/月/日"
        />

      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          @click="submitForm(ruleFormRef)">搜索
        </el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>


    <!--    表格 -->
    <div class="w-99/100 mt-6 p-2 bg-white">

      <el-table :data="_self.tableData" style="width: 100%;text-align: center">
        <el-table-column prop="create_at" label="操作时间"/>
        <el-table-column prop="operator" label="操作用户"/>
        <el-table-column prop="func_path" label="功能路径"/>
        <el-table-column prop="opt_type" label="操作类型"/>
        <el-table-column prop="func_type" label="功能类型"/>
        <el-table-column prop="sign" label="主要标识"/>
        <el-table-column prop="contents" label="修改项" width="200"/>
        <el-table-column prop="visitor" label="IP 和 浏览器"/>
      </el-table>

      <el-pagination
        class="flex justify-end mt-4"
        v-model:page-size="_self.qurey.page_size"
        :page-sizes="[10, 20, 30, 50]"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="_self.qurey.total"
        @size-change="handleSizeChangeUpgrade"
        @current-change="handleCurrentChange"/>

    </div>


  </div>
</template>

<script setup lang="ts">
import {getLogslist} from "/@/api/sys"
import {onMounted, reactive, ref} from 'vue';
const loading = ref(false);
import type { FormInstance, FormRules } from 'element-plus';
const ruleFormRef = ref<FormInstance>();
const formSize = ref('default')
let _self = reactive({
    qurey: {
      name: '',
      sign: '',
      func_type: '' ,//功能类型
      opt_type: '',      //操作类型
      page: 1,
      page_size: 10,
      start_time: "",
      end_time: "",
      total:0,
    },
    options: [
      {value:'销售订单',},
      {value:'升级订单',},
      {value:'订单设备',},
      {value:'授权记录',},
      {value:'U-key',},
      {value:'主任密钥',},
      {value:'服务器',},
      {value:'客户端',},
      {value:'程序版本',},
      {value:'用户账户',},
      {value:'短信登录状态',},
      {value:'系统角色',},
      {value:'医院',},
      {value:'代理商',},
      {value:'登入系统',},
    ],
    tableData :[
      {
        date: '2024-01-26 19:00:01',
        name: 'admin',
        address: '授权管理',
      },
    ]
  });
async function handleCurrentChange(val: number) {
     console.log(val)
    _self.qurey.page=val;
     await Logslist()
  }

async function handleSizeChangeUpgrade(val: number) {
  console.log(val)

  _self.qurey.page_size=val;
  await Logslist()
}


async function Logslist(){
  let data=await getLogslist(_self.qurey);
   if(data.code=='0'){
     _self.tableData=data?.data?.list;
     _self.qurey.total= data?.data?.total
   }
   console.log(data)
 }



onMounted(async () => {
    loading.value=true;
  await Logslist()
  });

const submitForm = async (formEl: FormInstance | undefined) => {
  var StartcurrentDate = new Date(_self.qurey.start_time);
  var EndtcurrentDate = new Date(_self.qurey.end_time)
  _self.qurey.start_time=_self.qurey.start_time?StartcurrentDate.getFullYear()+'-'+StartcurrentDate.getMonth()+1+'-'+StartcurrentDate.getDate():'';
  _self.qurey.end_time=_self.qurey.end_time?EndtcurrentDate.getFullYear()+'-'+EndtcurrentDate.getMonth()+1+'-'+EndtcurrentDate.getDate():'';

  await Logslist()
}



const  resetForm =async () => {
  _self.qurey= {
    name: '',
    sign: '',
    func_type: '' ,//功能类型
    opt_type: '',      //操作类型
    page: 1,
    page_size: 10,
    start_time: "",
    end_time: "",
    total:0
  };
  await Logslist()
}





</script>

<style scoped lang="scss">
  .el-table .el-table__cell {
    text-align: center !important;
  }
</style>
