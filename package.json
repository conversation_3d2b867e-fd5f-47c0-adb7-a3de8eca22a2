{"name": "pure-admin-thin", "version": "3.2.0", "private": true, "scripts": {"dev": "cross-env --max_old_space_size=4096 vite", "serve": "pnpm dev", "build": "rimraf dist && cross-env vite build", "build:staging": "rimraf dist && cross-env vite build --mode staging  --废弃命令", "report": "rimraf dist && cross-env vite build", "preview": "vite preview", "preview:build": "pnpm build && vite preview", "clean:cache": "rm -rf node_modules && rm -rf .eslintcache && pnpm install", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,css,scss,postcss,less}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged -c ./.husky/lintstagedrc.js", "lint:pretty": "pretty-quick --staged", "lint": "pnpm lint:eslint && pnpm lint:prettier && pnpm lint:stylelint", "prepare": "husky install", "preinstall": "npx only-allow pnpm"}, "browserslist": ["> 1%", "not ie 11", "not op_mini all"], "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@ctrl/tinycolor": "^3.4.0", "@iconify-icons/eos-icons": "^1.2.6", "@iconify-icons/fa": "^1.2.4", "@pureadmin/components": "^1.0.6", "@vueuse/core": "^8.3.1", "@vueuse/motion": "^2.0.0-beta.12", "@vueuse/shared": "^8.3.1", "ali-oss": "^6.17.1", "animate.css": "^4.1.1", "axios": "^0.27.2", "css-color-function": "^1.3.3", "dayjs": "^1.11.0", "echarts": "^4.9.0", "element-plus": "^2.1.11", "element-resize-detector": "^1.2.3", "js-base64": "^3.7.2", "js-cookie": "^3.0.1", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lodash-unified": "^1.0.2", "mitt": "^3.0.0", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "npx": "^10.2.2", "path": "^0.12.7", "pinia": "^2.0.13", "qrcode": "^1.5.0", "qs": "^6.10.2", "resize-observer-polyfill": "^1.5.1", "responsive-storage": "^1.0.11", "rgb-hex": "^4.0.0", "socket.io-client": "^4.5.1", "update-browserslist-db": "^1.0.13", "vue": "^3.2.33", "vue-clipboard3": "^2.0.0", "vue-i18n": "^9.2.0-beta.35", "vue-router": "^4.0.14", "vue-types": "^4.1.1", "wangeditor": "^4.7.15"}, "devDependencies": {"@commitlint/cli": "13.1.0", "@commitlint/config-conventional": "13.1.0", "@element-plus/icons-vue": "^2.1.0", "@iconify-icons/ep": "^1.2.4", "@iconify-icons/ri": "^1.2.1", "@iconify/vue": "^3.2.0", "@intlify/vite-plugin-vue-i18n": "^3.4.0", "@pureadmin/theme": "^1.1.0", "@types/ali-oss": "^6.16.11", "@types/element-resize-detector": "1.1.3", "@types/js-cookie": "^3.0.1", "@types/lodash": "^4.14.180", "@types/lodash-es": "^4.17.6", "@types/mockjs": "1.0.3", "@types/node": "14.14.14", "@types/nprogress": "0.2.0", "@types/qrcode": "^1.4.2", "@types/qs": "^6.9.7", "@typescript-eslint/eslint-plugin": "^5.10.2", "@typescript-eslint/parser": "^5.10.2", "@vitejs/plugin-legacy": "^1.8.1", "@vitejs/plugin-vue": "^2.3.1", "@vitejs/plugin-vue-jsx": "^1.3.10", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^10.0.0", "autoprefixer": "^10.4.17", "browserslist": "^4.22.2", "caniuse-lite": "^1.0.30001650", "cross-env": "^7.0.3", "eslint": "^8.8.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.4.1", "husky": "7.0.2", "lint-staged": "11.1.2", "picocolors": "^1.0.0", "postcss": "^8.4.6", "postcss-html": "^1.3.0", "postcss-import": "14.0.0", "postcss-scss": "^4.0.3", "prettier": "^2.5.1", "pretty-quick": "3.1.1", "rimraf": "3.0.2", "rollup": "^2.70.1", "rollup-plugin-visualizer": "^5.6.0", "sass": "^1.50.1", "stylelint": "^14.3.0", "stylelint-config-html": "^1.0.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-recommended": "^6.0.0", "stylelint-config-standard": "^24.0.0", "stylelint-order": "^5.0.0", "typescript": "^4.6.3", "vite": "^2.9.6", "vite-plugin-mock": "^2.9.6", "vite-plugin-remove-console": "^0.0.7", "vite-plugin-windicss": "^1.8.4", "vite-svg-loader": "2.2.0", "vue-eslint-parser": "^8.2.0", "windicss": "^3.5.1"}, "repository": "**************:xiaoxian521/vue-pure-admin.git", "author": "xiaoxian521", "license": "MIT"}