import { $t } from "/@/plugins/i18n";
const Layout = () => import("/@/layout/index.vue");

const klRouter = {
  path: "/kl",
  name: "kl",
  value: "kl",
  component: Layout,
  redirect: "/kl/syndrome",
  api: [],
  meta: {
    icon: "setting",
    title: "知识图谱",
    i18n: true,
    rank: 3
  },
  children: [
    {
      path: "/kl/syndrome",
      name: "kl-syndrome",
      value: "kl-syndrome",
      component: () => import("/@/views/kl/syndrome.vue"),
      api: ['kl-syndrome/tree', "kl-syndrome/tree", "kl-syndrome/details","kl-syndrome/add", "kl-syndrome/edit", "kl-syndrome/set-status", "kl-syndrome/delete", "kl-syndrome/set-sort"],
      meta: {
        title: "综合征",
        icon: "admin-line",
      }
    },
    {
      path: "/kl/feature",
      name: "feature",
      value: "feature",
      component: () => import("/@/views/kl/feature.vue"),
      api: ['kl-feature/tree', 'kl-feature/details', 'kl-feature/add', 'kl-feature/edit', 'kl-feature/set-status',"kl-feature/delete","kl-feature/set-sort"],
      meta: {
        title: "特征",
        icon: "virus-line",
      }
    },
    {
      path: "/kl/version",
      name: "kl-version",
      component: () => import("/@/views/kl/version.vue"),
      api: ['kl-version/list', 'kl-version/add', 'kl-version/download', 'kl-version/delete'],
      meta: {
        title: "版本管理",
        icon: "hospital-line",
      }
    }
  ]
};

export default klRouter;
