@font-face {
  font-family: "iconfont"; /* Project id 2208059 */
  src: url("iconfont.woff2?t=1638023560828") format("woff2"),
    url("iconfont.woff?t=1638023560828") format("woff"),
    url("iconfont.ttf?t=1638023560828") format("truetype");
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.team-icontabs::before {
  content: "\e63e";
}

.team-iconlogo::before {
  content: "\e620";
}

.team-iconxinpin::before {
  content: "\e614";
}

.team-iconxinpinrenqiwang::before {
  content: "\e615";
}

.team-iconexit-fullscreen::before {
  content: "\e62a";
}

.team-iconfullscreen::before {
  content: "\e62b";
}
